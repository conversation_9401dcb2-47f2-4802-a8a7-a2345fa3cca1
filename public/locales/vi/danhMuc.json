{"ma": "Mã", "ten": "<PERSON><PERSON><PERSON>", "timTen": "<PERSON><PERSON><PERSON> tên", "timMa": "<PERSON><PERSON><PERSON> mã", "moTa": "<PERSON><PERSON>", "vuiLongNhapMoTa": "<PERSON><PERSON> lòng nhập mô tả", "vuiLongNhapMoTaKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập mô tả không quá 1000 ký tự", "vuiLongNhapGiaTriKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập giá trị không quá 1000 ký tự", "coQuanCongTac": "<PERSON><PERSON> quan công tác", "coHieuLuc": "<PERSON><PERSON> l<PERSON>", "khongHieuLuc": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON><PERSON> l<PERSON>", "chonHieuLuc": "<PERSON><PERSON><PERSON> l<PERSON>", "timCoQuanCongTac": "<PERSON><PERSON><PERSON> c<PERSON> quan công tác", "vuiLongNhapTen": "<PERSON><PERSON> lòng nhập tên", "vuiLongNhapTenTitleKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên {{ title }} không quá 1000 ký tự", "vuiLongNhapCoQuanCongTa": "<PERSON><PERSON> lòng nhập cơ quan công tác", "thuocDauSao": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>u sao", "thongTinChiTiet": "Th<PERSON>ng tin chi tiết", "nhapMa": "<PERSON>h<PERSON><PERSON> mã", "hieuLuc": "<PERSON><PERSON><PERSON>", "vuiLongChonDichVu": "<PERSON><PERSON> lòng chọn dịch vụ", "vuiLongChonAnhLuocDoPhauThuatThuThuat": "<PERSON><PERSON> lòng chọn ảnh lược đồ phẫu thuật, thủ thuật", "nhapTenDichVu": "<PERSON><PERSON><PERSON><PERSON> tên d<PERSON> vụ", "chonTenDichVu": "<PERSON><PERSON><PERSON> tên d<PERSON> vụ", "vuiLongChonTenDichVu": "<PERSON>ui lòng chọn tên dịch vụ", "tenDichVu": "<PERSON><PERSON><PERSON> v<PERSON>", "vuiLongNhapTenDichVu": "<PERSON><PERSON> lòng nhập tên dịch vụ", "vuiLongNhapTenDichVuKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên dịch vụ không quá 1000 ký tự", "maDichVu": "Mã d<PERSON>ch vụ", "vuiLongNhapMaDichVu": "<PERSON><PERSON> lòng nhập mã dịch vụ", "vuiLongNhapGiaTriKhongQua20KyTu": "<PERSON><PERSON> lòng nhập giá trị không quá 20 ký tự!", "cachThucCanThiep": "<PERSON><PERSON><PERSON> thức can thiệp", "ketQua": "<PERSON><PERSON><PERSON> qu<PERSON>", "nhapTen": "<PERSON><PERSON><PERSON><PERSON> tên", "tenMau": "<PERSON><PERSON>n mẫu", "huy": "<PERSON><PERSON><PERSON>", "luu": "<PERSON><PERSON><PERSON>", "ketLuan": "<PERSON><PERSON><PERSON> l<PERSON>", "danhMucMauKetQuaCDHATDCN": "<PERSON><PERSON> m<PERSON>c mẫu kết quả CĐHA - TDCN", "soNgayCanhBaoHsd": "<PERSON><PERSON> ng<PERSON> cảnh báo HSD", "themMoi": "<PERSON><PERSON><PERSON><PERSON>", "mucDichSuDung": "<PERSON><PERSON><PERSON> đ<PERSON>ch sử dụng", "chonMucDichSuDung": "<PERSON><PERSON><PERSON> mục đích sử dụng", "quanTriHeThong": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON> hệ thống", "nhomTinhNang": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> n<PERSON>ng", "danhMucVaiTro": "<PERSON><PERSON> mục vai trò", "quanLyTaiKhoan": "<PERSON><PERSON><PERSON><PERSON> lý tài <PERSON>n", "loaiDoiTuong": "<PERSON><PERSON><PERSON> đối t<PERSON>", "danhMucLoaiHinhThanhToan": "<PERSON><PERSON> m<PERSON> hình <PERSON>h toán", "loaiHinhThanhToan": "<PERSON><PERSON><PERSON> hình <PERSON>h toán", "chonLoaiHinhThanhToan": "<PERSON><PERSON><PERSON> lo<PERSON>i hình <PERSON>h toán", "danhMucGoiDichVu": "<PERSON><PERSON> m<PERSON>c g<PERSON>i d<PERSON>ch vụ", "khoaChiDinhGoi": "Khoa chỉ định gói", "dichVuTrongGoi": "<PERSON><PERSON><PERSON> vụ trong gói", "thongTinGoi": "Thông tin gói", "hanCheKhoaChiDinh": "<PERSON><PERSON><PERSON> chế khoa chỉ định", "chonHanCheKhoaChiDinh": "<PERSON><PERSON><PERSON> hạn chế khoa chỉ định", "loaiDv": "Loại DV", "goiDichVu": "<PERSON><PERSON><PERSON> v<PERSON>", "danhMucLoaiDoiTuong": "<PERSON><PERSON> m<PERSON><PERSON> đối tư<PERSON>", "macDinh": "Mặc định", "maGiuongGuiBHYT": "Mã Giường gửi BHYT", "timMaGiuongGuiBHYT": "Tìm mã giường gửi BHYT", "nhapMaGiuongGuiBHYT": "Nhập mã giường gửi BHYT", "thongTinGoiDichVu": "Thông tin gói dịch vụ", "quay": "<PERSON><PERSON><PERSON><PERSON>", "danhMucQuay": "<PERSON><PERSON>", "khongLaySo": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>y số", "chonKhongLaySo": "<PERSON><PERSON><PERSON> không lấy số", "maQuay": "<PERSON><PERSON> qu<PERSON>y", "timMaQuay": "<PERSON><PERSON><PERSON> mã quầy", "vuiLongNhapMaQuay": "<PERSON>ui lòng nhập mã quầy!", "vuiLongNhapMaQuayKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã quầy không quá 20 ký tự!", "tenQuay": "<PERSON><PERSON><PERSON>", "timTenQuay": "<PERSON><PERSON><PERSON> tên qu<PERSON>y", "vuiLongNhapTenQuay": "<PERSON>ui lòng nhập tên quầy!", "vuiLongNhapTenQuayKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên quầy không quá 1000 ký tự!", "loaiQuay": "<PERSON><PERSON><PERSON> qu<PERSON>", "chonLoaiQuay": "<PERSON><PERSON><PERSON> lo<PERSON> qu<PERSON>", "timLoaiQuay": "<PERSON><PERSON><PERSON> lo<PERSON> qu<PERSON>", "vuiLongChonLoaiQuay": "<PERSON>ui lòng chọn loại quầy!", "soLuongHangCho": "Số l<PERSON>ng hàng chờ", "timSoLuongHangCho": "<PERSON><PERSON><PERSON> s<PERSON> lư<PERSON> hàng chờ", "vuiLongNhapSoLuongHangCho": "<PERSON>ui lòng nhập số lượng hàng chờ!", "vuiLongNhapSoLuongHangChoKhongQua2KyTu": "<PERSON><PERSON> lòng nhập số lượng hàng chờ không quá 2 ký tự!", "maThuoc": "<PERSON><PERSON> th<PERSON>", "vuiLongNhapMaThuoc": "<PERSON><PERSON> lòng nhập mã thuốc", "vuiLongNhapMaThuocKhongQua20KyTu": "<PERSON>ui lòng nhập mã thuốc không quá 20 ký tự!", "vuiLongNhapMaThuocKhongQua50KyTu": "<PERSON><PERSON> lòng nhập mã thuốc không quá 50 ký tự!", "vuiLongNhapMaThuocKhongQua200KyTu": "<PERSON>ui lòng nhập mã thuốc không quá 200 ký tự!", "tenThuoc": "<PERSON><PERSON><PERSON>", "vuiLongNhapTenThuoc": "<PERSON>ui lòng nhập tên thuốc!", "vuiLongNhapTenThuocKhongQua1024KyTu": "<PERSON><PERSON> lòng nhập tên thuốc không quá 1024 ký tự!", "maHoatChat": "<PERSON><PERSON> hoạt chất", "vuiLongNhapMaHoatChat": "<PERSON><PERSON> lòng nhập mã hoạt chất", "chonMaHoatChat": "<PERSON><PERSON><PERSON> mã hoạt chất", "tenHoatChat": "<PERSON><PERSON><PERSON> chất", "nhapTenHoatChat": "<PERSON><PERSON><PERSON><PERSON> tên ho<PERSON> chất", "hamLuong": "<PERSON><PERSON><PERSON>", "nhapHamLuong": "<PERSON><PERSON><PERSON><PERSON>", "nhomThuoc": "<PERSON><PERSON><PERSON><PERSON>", "chonMaNhomThuoc": "<PERSON><PERSON><PERSON> mã nhóm thuốc", "phanNhomThuoc": "<PERSON><PERSON> nh<PERSON> thu<PERSON>c", "chonPhanNhomThuoc": "<PERSON><PERSON><PERSON> phân nhóm thuốc", "phanLoaiThuoc": "<PERSON><PERSON> lo<PERSON>i thuốc", "vuiLongChonPhanLoaiThuoc": "<PERSON><PERSON> lòng chọn phân loại thuốc", "chonPhanLoaiThuoc": "<PERSON><PERSON><PERSON> phân loại thuốc", "donViSoCap": "Đơn vị sơ cấp", "batBuocDienDonViSoCapKhiHeSoDinhMucLonHon1": "<PERSON><PERSON><PERSON> buộ<PERSON> điền Đ<PERSON>n vị sơ cấp khi <PERSON>ệ số định mức >1", "chonDonViSoCap": "<PERSON><PERSON><PERSON> đơn vị sơ cấp", "donViThuCap": "Đơn vị thứ cấp", "vuiLongChonDonViThuCap": "<PERSON><PERSON> lòng chọn đơn vị thứ cấp", "chonDonViThuCap": "<PERSON><PERSON><PERSON> đơn vị thứ cấp", "heSoDinhMuc": "<PERSON><PERSON> số đ<PERSON><PERSON> mức", "vuiLongNhapHeSoDinhMuc": "<PERSON><PERSON> lòng nhập hệ số định mức!", "vuiLongNhapHeSoDinhMucLonHon0": "<PERSON><PERSON> lò<PERSON> nh<PERSON><PERSON> hệ số định mức lớn hơn 0", "nhapHeSoDinhMuc": "<PERSON><PERSON><PERSON><PERSON> hệ số định mức", "dungTich": "Dung tích (ml)/1 ĐVT sơ cấp", "nhapDungTich": "<PERSON><PERSON><PERSON><PERSON> dung tích (ml)/1 ĐVT sơ cấp", "quyCach": "<PERSON><PERSON>", "nhapQuyCach": "<PERSON><PERSON><PERSON><PERSON> quy c<PERSON>ch", "nuocSanXuat": "<PERSON><PERSON><PERSON><PERSON> sản xu<PERSON>", "chonNuocSanXuat": "<PERSON><PERSON><PERSON> sản xuất", "chonNhaSanXuat": "<PERSON><PERSON><PERSON> nhà sản xuất", "maThietBi": "<PERSON><PERSON> thiết bị", "vuiLongNhapMaThietBi": "<PERSON><PERSON> lòng nhập mã thiết bị", "nhapMaThietBi": "<PERSON><PERSON><PERSON><PERSON> mã thiết bị", "hoiSucTichCuc": "<PERSON><PERSON><PERSON> s<PERSON>c tích c<PERSON>c", "chonHoiSucTichCuc": "<PERSON><PERSON> h<PERSON><PERSON> sức tích c<PERSON>c", "nhaCungCap": "<PERSON><PERSON><PERSON> cung cấp", "chonNhaCungCap": "<PERSON><PERSON><PERSON> nhà cung cấp", "giaSauVAT1DvtSoCap": "Giá sau VAT/ 1 ĐVT sơ cấp", "nhapGiaSauVAT1DvtSoCap": "Nhập giá sau VAT/ 1 ĐVT sơ cấp", "giaTran": "<PERSON><PERSON><PERSON> t<PERSON>", "nhapGiaTran": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> trần", "tranBaoHiem": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "nhapTranBaoHiem": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> b<PERSON><PERSON>", "tyLeBhThanhToan": "Tỷ lệ BH thanh toán", "nhapTyLeBhThanhToan": "<PERSON>hập tỷ lệ BH thanh toán", "vuiLongNhapTyLeBhThanhToanKhongQua3KyTu": "<PERSON><PERSON> lòng nhập tỷ lệ BH thanh toán không quá 3 ký tự!", "tyLeThanhToanDichVu": "Tỷ lệ thanh to<PERSON> d<PERSON>ch vụ", "nhapTyLeThanhToanDichVu": "<PERSON><PERSON><PERSON><PERSON> tỷ lệ thanh toán dịch vụ", "vuiLongNhapTyLeThanhToanDichVu": "<PERSON><PERSON> lòng nhập tỷ lệ thanh toán dịch vụ!", "vuiLongNhapTyLeDvThanhToanKhongQua3KyTu": "<PERSON><PERSON> lòng nhập tỷ lệ DV thanh toán không quá 3 ký tự!", "nhomDichVuCap1": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON> cấp I", "vuiLongChonNhomDichVuCap1": "<PERSON><PERSON> lòng chọn nhóm dịch vụ cấp I", "chonNhomdichVuCap1": "<PERSON><PERSON><PERSON> nh<PERSON> dị<PERSON> v<PERSON> cấp I", "nhomDichVuCap2": "Nhóm d<PERSON><PERSON> v<PERSON> cấp II", "vuiLongChonNhomDichVuCap2": "<PERSON><PERSON> lòng chọn nhóm dịch vụ cấp II", "chonNhomdichVuCap2": "Ch<PERSON>n n<PERSON> dị<PERSON> v<PERSON> cấp II", "nhomDichVuCap3": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON> cấp III", "chonNhomdichVuCap3": "Chọn nhó<PERSON> dịch vụ cấp IIII", "maTuongDuong": "<PERSON><PERSON> tương đ<PERSON>", "nhapMaTuongDuong": "<PERSON><PERSON><PERSON><PERSON> mã tương đương", "tenTuongDuong": "<PERSON><PERSON><PERSON> t<PERSON>", "nhapTenTuongDuong": "<PERSON><PERSON><PERSON><PERSON> tên tươ<PERSON> đ<PERSON>", "soVisa": "Số visa", "nhapSoVisa": "<PERSON><PERSON><PERSON><PERSON> số visa", "maLienThongDuocQuocGia": "<PERSON><PERSON> liên thông dư<PERSON> quốc gia", "nhapMaLienThongDuocQuocGia": "<PERSON><PERSON><PERSON><PERSON> mã liên thông dư<PERSON><PERSON> quốc gia", "nguonChiTraKhac": "<PERSON><PERSON><PERSON><PERSON> chi tr<PERSON> kh<PERSON>c", "hienThiLenKiosk": "<PERSON><PERSON><PERSON> thị lên <PERSON>", "khoaChiDinhTheoDvtSoCap": "<PERSON><PERSON><PERSON> chỉ định theo ĐVT sơ cấp", "chonKhoaChiDinhTheoDvtSoCap": "<PERSON><PERSON><PERSON> khoa chỉ định theo ĐVT sơ cấp", "duongDung": "<PERSON><PERSON><PERSON><PERSON> dùng", "chonDuongDung": "<PERSON><PERSON><PERSON> đư<PERSON> dùng", "nhomThau": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>u", "goiThau": "<PERSON><PERSON><PERSON>", "quyetDinhThau": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> thầu", "khongDuocNhapQua25KyTu": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> nhập quá 25 ký tự", "khongDuocNhapQua50KyTu": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> nhập quá 50 ký tự", "khongDuocNhapQuaNumKyTu": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> nhập quá {{ num }} ký tự", "choPhepKeSlLe": "Cho phép kê SL lẻ", "theoDoiNgaySd": "<PERSON>", "khongTinhTien": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h tiền", "timMaThuoc": "<PERSON><PERSON><PERSON> mã thuốc", "timTenHoatChat": "<PERSON><PERSON><PERSON> tên ho<PERSON>t chất", "timTenThuoc": "<PERSON><PERSON><PERSON> tên thu<PERSON>c", "chonNhomThuoc": "<PERSON><PERSON><PERSON> n<PERSON> thu<PERSON>c", "timHamLuong": "<PERSON><PERSON><PERSON> h<PERSON>", "timQuyCach": "<PERSON><PERSON><PERSON> quy cách", "timHeSoDinhMuc": "<PERSON><PERSON><PERSON> hệ số định mức", "giaNhap": "<PERSON><PERSON><PERSON>", "timGiaNhap": "<PERSON><PERSON><PERSON> g<PERSON>", "loaiKhuVuc": "Loại khu vực", "vuiLongChonLoaiKhuVuc": "<PERSON><PERSON> lòng chọn lo<PERSON>i khu vực", "chonNhomDvCap1": "Chọn nhóm DV cấp 1", "nhomChiPhi": "Nhóm chi phí", "chonNhomChiPhi": "<PERSON><PERSON>n nhóm chi phí", "timTyLeBhtt": "Tìm tỷ lệ BHTT", "timTenTuongDuong": "<PERSON><PERSON><PERSON> tên tươ<PERSON> đ<PERSON>ng", "timSoVisa": "Tìm số visa", "timMaLienThongDuocQuocGia": "<PERSON><PERSON><PERSON> mã liên thông d<PERSON><PERSON><PERSON> quốc gia", "timNguonChiTraKhac": "<PERSON><PERSON><PERSON> nguồn chi tr<PERSON> kh<PERSON>c", "timKhoaChiDinhTheoDvtSoCap": "<PERSON><PERSON><PERSON> khoa chỉ định theo ĐVT sơ cấp", "chonNhomThau": "<PERSON><PERSON><PERSON> nh<PERSON> thầu", "chonTinhTien": "<PERSON><PERSON><PERSON> t<PERSON> ti<PERSON>n", "chonTheoDoiNgaySuDung": "<PERSON><PERSON><PERSON> theo dõi ng<PERSON>", "chonKeSlLe": "<PERSON><PERSON><PERSON> kê SL lẻ", "timDuongDung": "<PERSON><PERSON><PERSON> dùng", "timDungTich": "Tìm dung tích (ml)/1 ĐVT sơ cấp", "nhapQuyetDinhThau": "<PERSON><PERSON><PERSON><PERSON> quyế<PERSON> đ<PERSON>nh thầu", "chonGoiThau": "<PERSON><PERSON><PERSON> g<PERSON>i thầu", "maLoaiHinhThanhToan": "<PERSON>ã loại hình thanh toán", "tenLoaiHinhThanhToan": "<PERSON><PERSON><PERSON> lo<PERSON>i hình <PERSON>h toán", "vuiLongNhapMaLoaiHinhThanhToan": "<PERSON><PERSON> lòng nhập mã loại hình thanh toán", "danhMucHauQuaTuongTac": "<PERSON><PERSON> m<PERSON> quả tương tác", "vuiLongNhapMaGiaTri": "<PERSON><PERSON> lòng nhập mã {0}", "vuiLongNhapTenGiaTri": "<PERSON><PERSON> lòng nhập tên {0}", "vuiLongNhapTenGiaTriKhongQuaKyTu": "<PERSON><PERSON> lòng nhập tên {0} không quá {1} ký tự", "maGiaTri": "Mã {0}", "tenGiaTri": "<PERSON><PERSON><PERSON> {0}", "mauMucDo": "<PERSON><PERSON><PERSON> mức độ tương tác", "nhapMauMucDo": "<PERSON><PERSON><PERSON><PERSON> màu mức độ tương tác", "danhMucDacTinhDuocLy": "<PERSON><PERSON> mục Đặc t<PERSON>h dược lý", "danhMucMucDoTuongTac": "<PERSON><PERSON> <PERSON><PERSON> độ tương tác", "khaiBaoTuongTacThuoc": "<PERSON><PERSON> b<PERSON><PERSON> t<PERSON> tác thuốc", "dacTinhDuocLy": "Đặc t<PERSON>h d<PERSON> lý", "hoatChat": "<PERSON><PERSON><PERSON> ch<PERSON>", "timHoatChat": "<PERSON><PERSON><PERSON> chất", "bietDuoc": "<PERSON><PERSON><PERSON><PERSON>", "mucDoTuongTac": "<PERSON><PERSON><PERSON> độ tương tác", "coChe": "<PERSON><PERSON> chế", "hauQua": "<PERSON><PERSON><PERSON> quả", "xuTri": "<PERSON>ử trí", "canhBaoIcd": "Cảnh báo ICD", "chanDoanBenh": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> b<PERSON>nh", "chanKeThuoc": "Chặn kê thuốc", "xetNghiem": "<PERSON><PERSON><PERSON>", "ngoaiTruICD": "Ngoại trừ ICD", "canhBaoXetNghiem": "<PERSON><PERSON><PERSON> b<PERSON>o x<PERSON> ng<PERSON>", "vuiLongNhapGiaTriKhongQuaKyTu": "<PERSON><PERSON> lòng nhập {0} không quá {1} ký tự", "vuiLongNhapMucDoTuongTac": "<PERSON><PERSON> lòng nhập mức độ tương tác", "vuiLongNhapHauQua": "<PERSON><PERSON> lòng nhập hậu quả", "vuiLongNhapChanDoanBenh": "<PERSON><PERSON> lòng nh<PERSON>p chẩn đo<PERSON> b<PERSON>nh", "apDungTt35": "Á<PERSON> dụng TT35", "taoChiSoCon": "<PERSON><PERSON><PERSON><PERSON> tạo mới chỉ số con", "maMucDich": "<PERSON><PERSON> mục đ<PERSON>ch", "tenMucDich": "<PERSON><PERSON><PERSON> m<PERSON>", "donGiaKhongBh": "Đơn gi<PERSON> không BH", "nhapDonGiaKhongBh": "<PERSON><PERSON><PERSON><PERSON> đơn gi<PERSON> không BH", "vuiLongNhapSoDuong": "<PERSON><PERSON> lòng nhập số dương", "donGiaBh": "Đơn giá BH", "nhapDonGiaBh": "<PERSON><PERSON><PERSON><PERSON> đơn giá BH", "phuThu": "<PERSON><PERSON> thu", "nhapPhuThu": "<PERSON><PERSON><PERSON><PERSON> phụ thu", "apDungTheoThoiGianThucHienDv": "<PERSON><PERSON> dụng theo ngày thực hiện DV", "ghiChu": "<PERSON><PERSON><PERSON>", "nhapGiaPhuThu": "<PERSON><PERSON><PERSON><PERSON> giá phụ thu", "nhapGiaBaoHiem": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> b<PERSON><PERSON>", "nhapGiaKhongBaoHiem": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> không b<PERSON><PERSON> hi<PERSON>m", "vuiLongNhapGiaKhongBaoHiem": "<PERSON><PERSON> lòng nh<PERSON>p gi<PERSON> không b<PERSON><PERSON> hiểm", "apDungTt30": "<PERSON><PERSON> dụng TT30", "tyLeThanhToanBh": "Tỷ lệ thanh toán BH", "phienBanBieuMau": "<PERSON><PERSON><PERSON> bản biểu mẫu", "taoPhienBanBieuMau": "<PERSON><PERSON><PERSON> phiên bản biểu mẫu", "vuiLongChonBieuMau": "<PERSON><PERSON> lòng chọn biểu mẫu", "tenPhienBan": "<PERSON><PERSON><PERSON> p<PERSON>ê<PERSON> bản", "loaiPhienBan": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> bản", "phienBanGoc": "<PERSON><PERSON><PERSON> bả<PERSON>c", "phienBanTaiLen": "<PERSON><PERSON><PERSON> bản tải lên", "tepBieuMau": "<PERSON><PERSON><PERSON> biểu mẫu", "ngayTao": "<PERSON><PERSON><PERSON>", "nguoiTao": "<PERSON><PERSON><PERSON><PERSON> tạo", "kichThuocAnhPhaiNhoHon": "<PERSON><PERSON><PERSON> th<PERSON><PERSON>nh phải nhỏ hơn", "kichThuocAnhBanQuyenThuongHieuPhaiNhoHon": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> ảnh bản quyền thương hiệu phải nhỏ hơn", "vuiLongNhapHoatChat": "<PERSON><PERSON> lòng nh<PERSON><PERSON> ho<PERSON> chất", "batBuocNhapHoatChat1HoacHoatChat2": "<PERSON>ắt bu<PERSON><PERSON> nh<PERSON>p ho<PERSON> chất 1 hoặc hoạt chất 2", "chonTruongTrenKiosk": "<PERSON><PERSON> lòng chọn trường hiển thị trên màn tiếp đón kiosk", "maVatTu": "<PERSON><PERSON> vật tư", "tenVatTu": "<PERSON><PERSON><PERSON> v<PERSON>t tư", "timTenVatTu": "<PERSON><PERSON><PERSON> tên vật tư", "maGoiThau": "<PERSON><PERSON> gói thầu", "timMaGoiThau": "<PERSON><PERSON>m mã gói thầu", "tenGoiThau": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> thầu", "timTenGoiThau": "<PERSON><PERSON><PERSON> tên gói thầu", "timMaVatTu": "<PERSON><PERSON><PERSON> mã vật tư", "dmChiPhiHapSayVTYTTaiSuDung": "<PERSON><PERSON> mục chi phí hấp sấy VTYT tái sử dụng", "chiPhiHapSay": "<PERSON> phí hấp s<PERSON>y", "nhapChiPhiHapSay": "<PERSON><PERSON><PERSON><PERSON> chi phí hấp sấy", "apDungTuNgay": "<PERSON><PERSON> dụng từ ngày", "apDungDenNgay": "<PERSON><PERSON> dụng đến ng<PERSON>y", "thongTinDichVuTrongBo": "Thông tin dịch vụ trong bộ", "tenBo": "<PERSON><PERSON><PERSON> bộ", "dichVuTrongBo": "<PERSON><PERSON><PERSON> vụ trong bộ", "phongThucHien": "<PERSON><PERSON><PERSON> thự<PERSON> hi<PERSON>n", "bacSiNgoaiVien": "<PERSON><PERSON><PERSON> s<PERSON> ngo<PERSON>i viện", "title": "<PERSON><PERSON>", "hangThe": "Hạng thẻ", "cheDoChamSoc": "<PERSON><PERSON> độ chăm s<PERSON>c", "chiSoSong": "Chỉ số sống", "chucVu": "<PERSON><PERSON><PERSON> v<PERSON>", "chuyenKhoa": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>a", "vuiLongChonChuyenKhoa": "<PERSON><PERSON> lòng chọn chuyên khoa", "chonChuyenKhoa": "<PERSON><PERSON><PERSON> chuyên khoa", "danToc": {"title": "<PERSON><PERSON> t<PERSON>c", "maDanToc": "<PERSON>ã dân tộc", "nhapMaDanToc": "<PERSON>ui lòng nhập mã dân tộc!", "nhapMaDanTocKhongQua20KyTu": "<PERSON>ui lòng nhập mã dân tộc không quá 20 ký tự!", "tenDanToc": "<PERSON><PERSON><PERSON> dân tộc", "nhapTenDanToc": "<PERSON>ui lòng nhập tên dân tộc!", "nhapTenDanTocKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên dân tộc không quá 1000 ký tự!", "maDongBoTCQG": "Mã đồng bộ TCQG", "nhapMaTiemChungQuocGia": "<PERSON><PERSON> lòng nhập mã tiêm chủng quốc gia", "maTuongDuong": "<PERSON><PERSON> tương đ<PERSON>", "nhapMaTuongDuong": "<PERSON>ui lòng nhập mã tương đương!"}, "quocGia": {"title": "Quốc gia", "maQuocGia": "Mã quốc gia", "nhapMaQuocGia": "<PERSON>ui lòng nhập mã quốc gia!", "nhapMaQuocGiaKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã quốc gia không quá 20 ký tự!", "tenQuocGia": "<PERSON><PERSON><PERSON> quốc gia", "nhapTenQuocGia": "<PERSON>ui lòng nhập tên quốc gia!", "nhapTenQuocGiaKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên quốc gia không quá 1000 ký tự!", "maDongBoTCQG": "Mã đồng bộ TCQG", "nhapMaTiemChungQuocGia": "<PERSON><PERSON> lòng nhập mã tiêm chủng quốc gia", "maTuongDuong": "<PERSON><PERSON> tương đ<PERSON>", "nhapMaTuongDuong": "<PERSON>ui lòng nhập mã tương đương!"}, "dichVuAn": "<PERSON><PERSON><PERSON> v<PERSON>", "coQuanDonVi": "<PERSON><PERSON> quan đơn vị", "hauQuaTuongTac": "<PERSON><PERSON><PERSON> qu<PERSON> tư<PERSON> tác", "hinhThucNhapXuat": "<PERSON><PERSON><PERSON> thứ<PERSON> nh<PERSON> xu<PERSON>t", "hocViHamVi": "<PERSON><PERSON><PERSON> hàm học vị", "khoa": "<PERSON><PERSON><PERSON>", "huongDanSuDung": "Hướng dẫn sử dụng", "timHDSD": "<PERSON><PERSON><PERSON> h<PERSON> dẫn sử dụng", "lieuDung": "<PERSON><PERSON><PERSON> d<PERSON>", "chonLieuDung": "<PERSON><PERSON><PERSON> li<PERSON> dùng", "loaGoiSo": "Loa gọi số", "timLoaGoiSo": "T<PERSON>m loa gọi số", "loaiBenhAn": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "chonLoaiBenhAn": "<PERSON><PERSON><PERSON> lo<PERSON> b<PERSON><PERSON>", "vuiLongChonLoaiBenhAn": "<PERSON><PERSON> lòng chọn lo<PERSON>i b<PERSON><PERSON>n", "loaiBuaAn": "<PERSON><PERSON><PERSON> b<PERSON>a ăn", "khangNguyen": "<PERSON><PERSON><PERSON><PERSON>", "luocDo": "<PERSON><PERSON><PERSON><PERSON>", "anhLuocDoPhauThuat": "Ảnh l<PERSON><PERSON><PERSON> đồ Phẫu thuật", "anhLuocDoPhauThuatThuThuat": "Ảnh l<PERSON><PERSON><PERSON> đồ PTTT", "phuongPhapCheBien": "Phư<PERSON><PERSON> ph<PERSON>p chế biến", "chonPhuongPhapCheBien": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> pháp chế biến", "maPhuongPhap": "Mã phư<PERSON> pháp", "tenPhuongPhap": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> pháp", "vuiLongNhapMaPhuongPhap": "<PERSON><PERSON> lòng nhập mã phương pháp", "vuiLongNhapMaPhuongPhapKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập mã phương pháp không quá {{ num }} ký tự!", "vuiLongNhapTenPhuongPhap": "<PERSON><PERSON> lòng nhập tên phư<PERSON>ng pháp", "vuiLongNhapTenPhuongPhapKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập tên phương pháp không quá {{ num }} ký tự!", "dangBaoChe": "<PERSON><PERSON><PERSON> b<PERSON>o chế", "timDangBaoChe": "<PERSON><PERSON><PERSON> dạng b<PERSON>o chế", "phanLoaiNb": "<PERSON><PERSON> lo<PERSON>i ng<PERSON><PERSON> b<PERSON>nh", "phanLoaiPHCN": "Phân loại PHCN", "loaiCapCuu": "<PERSON><PERSON> c<PERSON><PERSON> c<PERSON>u", "loaiGiuong": "Loại gi<PERSON>", "loaiPhieu": "<PERSON><PERSON><PERSON>", "loiDan": "Lời dặn", "lyDoDoiTraDv": "Lý do đổi trả DV", "lyDoTamUng": "Lý do tạm ứng", "maMay": "M<PERSON> m<PERSON>", "mauDienBien": "Mẫu diễn biến", "mauKetQuaPhauThuatThuThuat": "Mẫu kết quả phẫu thuật - thủ thuật", "mauQms": "mẫu QMS", "moiQuanHe": "<PERSON><PERSON><PERSON> quan hệ", "ngayNghi": "ngày nghỉ", "ngheNghiep": {"title": "<PERSON><PERSON><PERSON>", "maNgheNghiep": "<PERSON><PERSON> nghề nghiệp", "nhapMaNgheNghiep": "<PERSON><PERSON> lòng nhập mã nghề nghiệp!", "nhapMaNgheNghiepKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã nghề nghiệp không quá 20 ký tự!", "tenNgheNghiep": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> nghi<PERSON>", "nhapTenNgheNghiep": "<PERSON>ui lòng nhập tên nghề nghiệp!", "nhapTenNgheNghiepKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên nghề nghiệp không quá 1000 ký tự!", "maTuongDuong": "<PERSON><PERSON> tương đ<PERSON>", "nhapMaTuongDuong": "<PERSON>ui lòng nhập mã tương đương!"}, "nguoiDaiDien": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "timNguoiDaiDien": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> đ<PERSON>", "nguonNhapKho": "<PERSON><PERSON><PERSON><PERSON><PERSON> kho", "nguyenNhanNhapVien": "<PERSON><PERSON><PERSON><PERSON> nhân nhập viện", "nhomChiSo": "Nhóm chỉ số", "nhomHoaChat": "<PERSON><PERSON><PERSON><PERSON> hóa chất", "nhomHoaChatCap1": "Nhóm hóa chất cấp 1", "chonNhomHoaChatCap1": "Chọn nhóm hóa chất cấp 1", "nhomHoaChatCap2": "Nhóm hóa chất cấp 2", "chonNhomHoaChatCap2": "Chọn n<PERSON>ó<PERSON> hóa chất cấp 2", "nhomVatTu": "<PERSON><PERSON><PERSON><PERSON> vật tư", "chonNhomVatTuCap1": "Chọn n<PERSON> v<PERSON><PERSON> tư cấp 1", "nhomVatTuCap1": "Nhóm v<PERSON><PERSON> tư cấp 1", "nhomVatTuCap2": "<PERSON>hóm v<PERSON><PERSON> tư cấp 2", "nhomVatTuCap3": "<PERSON>hóm v<PERSON><PERSON> tư cấp 3", "nhomVatTuCap4": "<PERSON>hóm v<PERSON><PERSON> tư cấp 4", "chonNhomVatTuCap4": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> tư cấp 4", "noiLayBenhPham": "<PERSON><PERSON><PERSON> l<PERSON> b<PERSON><PERSON> phẩm", "phanLoaiBmi": "Phân loại BMI", "phuongThucThanhToan": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "phuongPhapNhuom": "Phương p<PERSON><PERSON><PERSON>", "phuongPhapVoCam": "Phương ph<PERSON><PERSON> vô cảm", "phong": "Phòng", "chonPhong": "<PERSON><PERSON><PERSON> phòng", "quanHam": "<PERSON><PERSON><PERSON> h<PERSON>m", "quayTiepDon": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON><PERSON> đ<PERSON>", "soHieuGiuong": "<PERSON><PERSON> hiệu g<PERSON>", "thangSoBanLe": "Thặng số bán lẻ", "theBaoHiem": "Thẻ b<PERSON>o hiểm", "thoiGianCapCuu": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> c<PERSON>u", "toaNha": "Tòa nhà", "timToaNha": "<PERSON><PERSON>m tòa nhà", "vanBangChuyenMon": "V<PERSON>n bằng chuyên môn", "viTriChanThuong": "<PERSON><PERSON> trí chấn th<PERSON>", "viTriSinhThiet": "<PERSON><PERSON> trí sinh thiết", "xuatXu": "<PERSON><PERSON><PERSON>", "tuongTacThuoc": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c thu<PERSON>c", "hoaChat": "<PERSON><PERSON><PERSON>", "khuVuc": "<PERSON><PERSON> v<PERSON>", "chonKhuVuc": "<PERSON><PERSON><PERSON> khu vực", "vuiLongChonKhuVuc": "<PERSON><PERSON> lòng chọn khu vực", "vuiLongChonLoaGoiSo": "Vui lòng chọn loa gọi số!", "vuiLongChonToaNha": "Vui lòng chọn tòa nhà!", "vuiLongChonKhoa": "Vui lòng chọn khoa!", "chonKhoa": "<PERSON><PERSON><PERSON> k<PERSON>a", "soLuongTiepTheo": "<PERSON><PERSON> l<PERSON><PERSON> tiếp theo", "vuiLongNhapSoLuongTiepTheo": "<PERSON>ui lòng nhập số lượng tiếp theo!", "vuiLongNhapSoLuongTiepTheoKhongQua2KyTu": "<PERSON><PERSON> lòng nhập số lượng tiếp theo không quá 2 ký tự!", "soLuongHangDoi": "S<PERSON> lư<PERSON>ng hàng đợi", "vuiLongNhapSoLuongHangDoi": "<PERSON><PERSON> lòng nhập số lượng hàng đợi!", "vuiLongNhapSoLuongHangDoiKhongQua2KyTu": "<PERSON><PERSON> lòng nhập số lượng hàng đợi không quá 2 ký tự!", "mucDoUuTien": "<PERSON><PERSON><PERSON> đ<PERSON>u tiên", "nhanTheoDoi": "<PERSON><PERSON><PERSON><PERSON> theo dõi", "dongBoIward": "Đồng b<PERSON> Iward", "chonSoHieuGiuong": "<PERSON><PERSON><PERSON> số hiệu g<PERSON>", "chonTaiKhoan": "<PERSON><PERSON><PERSON> tà<PERSON>", "maPhieuLinh": "<PERSON><PERSON> phi<PERSON> lĩnh", "loaiDonThuoc": "<PERSON><PERSON><PERSON> đ<PERSON>n thu<PERSON>c", "vuiLongNhapMa": "<PERSON><PERSON> lòng nhập mã", "vuiLongNhapMaTitleKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã {{ title }} không quá 20 ký tự", "thuocPhauThuat": "<PERSON><PERSON><PERSON><PERSON> phẫu thuật", "dichVuGiuong": "<PERSON><PERSON><PERSON> v<PERSON> g<PERSON>", "truongHopApDung": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON> d<PERSON>", "phanLoaiPttt": "Phân loại PTTT", "chonPhanLoaiPttt": "Chọn phân loại PTTT", "soNgaySauMo": "Số ngày sau mổ", "vuiLongChonKhoaChiDinh": "<PERSON><PERSON> lòng chọn khoa chỉ định", "thietLapChonGiuong": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> chọn g<PERSON>", "khoaChiDinh": "Khoa chỉ định", "danhMuc": "<PERSON><PERSON>", "maAtc": "Mã ATC", "vacXin": "<PERSON><PERSON><PERSON> xin", "tenKhangNguyen": "<PERSON><PERSON><PERSON> k<PERSON>g nguyên", "maQuanLyTiemChung": "<PERSON><PERSON> quản lý tiêm chủng", "soMuiCanTiem": "<PERSON><PERSON> mũi cần tiêm", "lieuLuong": "<PERSON><PERSON><PERSON>", "donViTinh": "Đơn vị t<PERSON>h", "chonDonViTinh": "<PERSON><PERSON><PERSON> đơn vị t<PERSON>h", "nhapDonViTinh": "<PERSON><PERSON><PERSON><PERSON> đơn vị t<PERSON>h", "giaNhapSauVat": "<PERSON><PERSON><PERSON> sau VAT", "gioiTinhSuDung": "<PERSON><PERSON><PERSON>i t<PERSON>h sử dụng", "doTuoiSuDung": "<PERSON><PERSON> tuổi sử dụng", "tenLoaiBenhDuPhong": "<PERSON><PERSON><PERSON> lo<PERSON> b<PERSON>nh dự phòng", "thoiGianTiemCachNhau": "<PERSON><PERSON><PERSON><PERSON> gian tiêm c<PERSON>ch nhau", "maVacXin": "Mã vắc xin", "tenVacXin": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> xin", "tuTuoi": "<PERSON>ừ tuổi", "denTuoi": "<PERSON><PERSON><PERSON> tu<PERSON>i", "quyenSoPhieuThu": "<PERSON><PERSON><PERSON>n sổ phiếu thu", "caLamViec": "<PERSON>a làm việc", "phieuIn": "<PERSON><PERSON><PERSON> in", "viTriPhieuIn": "<PERSON><PERSON> trí phi<PERSON>u in", "manHinhPhieuIn": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> in", "maManHinh": "<PERSON><PERSON> màn hình", "timTheoMaManHinh": "<PERSON><PERSON><PERSON> theo mã màn hình", "tenManHinh": "<PERSON><PERSON><PERSON> màn hình", "timTheoTenManHinh": "<PERSON><PERSON><PERSON> theo tên màn hình", "maViTri": "Mã vị trí", "timTheoMaViTri": "<PERSON><PERSON><PERSON> theo mã vị trí", "tenViTri": "<PERSON><PERSON><PERSON> vị trí", "timTheoTenViTri": "<PERSON><PERSON><PERSON> theo tên vị trí", "manHinh": "<PERSON><PERSON><PERSON>", "viTri": "<PERSON><PERSON> trí", "maPhieu": "<PERSON><PERSON>", "timTheoMaPhieu": "<PERSON><PERSON><PERSON> theo mã phiếu", "tenPhieu": "<PERSON><PERSON><PERSON>", "timTheoTenPhieu": "<PERSON><PERSON><PERSON> theo tên p<PERSON>u", "chonLoaiManHinh": "<PERSON><PERSON><PERSON> lo<PERSON>i màn hình", "chonLoaiViTri": "<PERSON><PERSON><PERSON> lo<PERSON>i vị trí", "mauKqXnDotBien": "Mẫu KQ XN đột biến", "khacNgayLamViec": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> làm vi<PERSON>c", "vuiLongNhapMaHex": "<PERSON>ã màu phải bắt đầu bằng # và gồm 6 ký tự hợp lệ (0-9, A-F)", "hinhThucPhatLoa": "<PERSON><PERSON><PERSON> thức ph<PERSON>t loa", "sttChonNhanh": "STT chọn nhanh", "nhapSttChonNhanh": "Nhập STT chọn nhanh", "donViSuDung": "Đơn vị sử dụng", "chonDonViSuDung": "<PERSON><PERSON><PERSON> đơn vị sử dụng", "vuiLongChonDonViSuDung": "<PERSON><PERSON> lòng chọn đơn vị sử dụng", "phanTuyenPttt": "<PERSON>ân tuyến PTTT", "maPtttQuocTe": {"title": "<PERSON><PERSON> mục mã phẫu thuật, thủ thuật quốc tế (ICD-9)", "tatCa": "<PERSON><PERSON><PERSON> c<PERSON>", "chuongPttt": "Chương PTTT", "nhomPttt": "Nhóm PTTT", "loaiPttt": "Loại PTTT", "tenPttt": "Tên PTTT"}, "maQuyTrinh": "<PERSON><PERSON> quy trình", "vuiLongNhapMaQuyTrinh": "<PERSON><PERSON> lòng nhập mã quy trình", "tenQuyTrinh": "<PERSON>ên quy trình", "vuiLongNhapTenQuyTrinh": "<PERSON><PERSON> lòng nhập tên quy trình", "quyTrinhXetNghiem": "<PERSON><PERSON> tr<PERSON>nh x<PERSON> ng<PERSON>", "theoIso": "Theo <PERSON>", "tuNgay": "<PERSON><PERSON> ngày", "chonNgay": "<PERSON><PERSON><PERSON>", "denNgay": "<PERSON><PERSON><PERSON>", "tenTaiKhoanThayDoi": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thay đổi", "chonTenTaiKhoanThayDoi": "<PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i thay đổi", "tenTruong": "<PERSON><PERSON><PERSON> tr<PERSON>", "chonTenTruong": "<PERSON><PERSON><PERSON> tên trư<PERSON>", "loaiLamTron": "<PERSON><PERSON><PERSON> làm tròn", "daiMaNbRieng": "Dải mã NB riêng", "sinhDaiMaNbRieng": "<PERSON><PERSON> dải mã ng<PERSON><PERSON>i bệnh riêng", "chonLoaiGiuong": "<PERSON><PERSON><PERSON> lo<PERSON>i gi<PERSON>", "chonMaViTri": "<PERSON><PERSON>n mã vị trí", "choSuaDoi": "<PERSON> sửa đổi DV", "tuyChonGia": "<PERSON>ỳ chọn giá", "khoaChiDinhDv": "Khoa chỉ định dịch vụ", "dkThanhToanBh": "ĐK thanh toán BH", "giaTriCu": "<PERSON><PERSON><PERSON> trị cũ", "giaTriMoi": "<PERSON><PERSON><PERSON> trị mới", "nguoiThayDoi": "<PERSON><PERSON><PERSON><PERSON> thay đổi", "thoiGianThayDoi": "<PERSON>h<PERSON><PERSON> gian thay đổi", "thongTinDichVu": "Thông tin dịch vụ", "dichVuKemTheo": "<PERSON><PERSON><PERSON> v<PERSON> k<PERSON>m theo", "chiSoCon": "Chỉ số con", "lichSuChinhSua": "<PERSON><PERSON><PERSON> sử chỉnh sửa", "vuiLongChonLoaiKetQua": "<PERSON>ui lòng nhập <PERSON> kết quả!", "thuTuHienThi": "<PERSON><PERSON><PERSON> tự hiển thị", "timThuTuHienThi": "<PERSON><PERSON><PERSON> thứ tự hiển thị", "maKyHieu": "<PERSON><PERSON> ký hiệu", "maNguonNuoiBenh": "<PERSON><PERSON> nguồn ng<PERSON><PERSON><PERSON> b<PERSON>nh", "vuiLongNhapMaNguonNguoiBenh": "<PERSON><PERSON> lòng nhập mã nguồn ng<PERSON><PERSON>i bệnh", "vuiLongNhapMaNguonNguoiBenhKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã nguồn người bệnh không quá 20 ký tự!", "tenNguonNguoiBenh": "<PERSON><PERSON><PERSON> nguồn ng<PERSON><PERSON> b<PERSON>nh", "vuiLongNhapTenNguonNguoiBenh": "<PERSON><PERSON> lòng nhập tên nguồn ng<PERSON><PERSON>i bệnh", "nhomNguonNguoiBenh": "<PERSON><PERSON><PERSON><PERSON> nguồn ng<PERSON><PERSON> b<PERSON>nh", "vuiLongNhapNhomNguonNguoiBenh": "<PERSON><PERSON> lòng nhập nhóm nguồn ng<PERSON><PERSON><PERSON> bệnh", "chonNhomNguonNguoiBenh": "<PERSON><PERSON><PERSON> nhóm nguồn ng<PERSON><PERSON> b<PERSON>nh", "nguoiGioiThieu": "<PERSON><PERSON><PERSON><PERSON> giới thiệu", "thanhToanSau": "<PERSON>h toán sau", "danhMucVeCapCuu": "<PERSON><PERSON> về cấp c<PERSON>u", "danhMucVeKho": "<PERSON><PERSON> m<PERSON> về kho", "danhMucVeKyInPhieu": "<PERSON><PERSON> mục về ký, in, phi<PERSON>u", "danhMucVeDichVu": "<PERSON><PERSON> về dịch vụ", "danhMucVeTTHanhChinh": "<PERSON><PERSON> về TT hành chính", "danhMucChung": "<PERSON><PERSON> chung", "danhMucChamSocKhachHang": "<PERSON><PERSON> mục ch<PERSON>m sóc kh<PERSON>ch hàng", "danhMucCOM": "<PERSON><PERSON>", "thoiGianDangXuat": "<PERSON>h<PERSON><PERSON> gian đ<PERSON>ng xu<PERSON> (phút)", "vaiTro": "<PERSON>ai trò", "resetMatKhau": "Reset mật kh<PERSON>u", "tenTaiKhoan": "<PERSON><PERSON><PERSON> tà<PERSON>", "chonTenTaiKhoan": "<PERSON><PERSON><PERSON> tên tài <PERSON>n", "nhapTenTaiKhoan": "<PERSON><PERSON><PERSON><PERSON> tên tà<PERSON>n", "hoTen": "<PERSON><PERSON> tên", "vuiLongNhapHoTen": "<PERSON><PERSON> lòng nhập họ tên", "maNhanVien": "Mã nhân viên", "vuiLongNhapTenTaiKhoan": "<PERSON><PERSON> lòng nhập tên tài <PERSON>n", "vuiLongNhapMaNhanVien": "<PERSON><PERSON> lòng nhập mã nhân viên", "vuiLongNhapTenTaiKhoanKhongQua255KyTu": "<PERSON><PERSON> lòng nhập tên tài khoản không quá 255 ký tự", "vuiLongNhapHoTenKhongQua255KyTu": "<PERSON><PERSON> lòng nhập họ tên không quá 255 ký tự", "tenDonViCongTac": "<PERSON><PERSON><PERSON> đơn vị công tác", "diaChiDonViCongTac": "Địa chỉ đơn vị công tác", "ganVaiTro": "<PERSON><PERSON> vai trò", "timKiemVaiTro": "<PERSON><PERSON><PERSON> kiếm vai trò", "moRong": "Mở rộng", "thuNho": "<PERSON>hu nhỏ", "xacNhanResetMatKhauChoTaiKhoan": "<PERSON><PERSON><PERSON> reset mật khẩu cho tài k<PERSON>n", "danhMucLoaiCapCuu": "<PERSON><PERSON> <PERSON><PERSON> cấ<PERSON> c<PERSON>u", "danhMucThoiGianCapCuu": "<PERSON><PERSON> m<PERSON> gian cấ<PERSON> c<PERSON>u", "danhMucViTriChanThuong": "<PERSON><PERSON> m<PERSON>c <PERSON> trí chấn thương", "danhMucChePhamMau": "<PERSON><PERSON> m<PERSON><PERSON> phẩm máu", "danhMucDoiTac": "<PERSON><PERSON>c", "danhMucDuongDung": "<PERSON><PERSON> m<PERSON>c <PERSON> dùng", "danhMucHinhThucLoaiNhapXuat": "<PERSON><PERSON> mụ<PERSON> thức nhập/Loại xuất", "danhMucNhomHoaChat": "<PERSON><PERSON> hóa chất", "danhMucHoatChat": "<PERSON><PERSON><PERSON> chất", "danhMucKhangNguyen": "<PERSON><PERSON>", "danhMucPhuongPhapCheBien": "<PERSON><PERSON> ph<PERSON>p chế biến", "danhMucLieuDung": "<PERSON><PERSON><PERSON> d<PERSON>ng", "danhMucLieuDungBacSi": "<PERSON><PERSON> <PERSON><PERSON><PERSON> d<PERSON> - <PERSON><PERSON><PERSON>", "danhMucLoiDan": "<PERSON><PERSON> dặn", "danhMucNguonNhapKho": "<PERSON><PERSON> <PERSON><PERSON> kho", "danhMucNhomThuoc": "<PERSON><PERSON>c", "danhMucNhomVatTu": "<PERSON><PERSON> vật tư", "danhMucPhanLoaiThuoc": "<PERSON><PERSON> lo<PERSON>i thuốc", "danhMucPhanNhomThuoc": "<PERSON><PERSON> nhóm thuốc", "danhMucThangSoBanLe": "<PERSON><PERSON> mục Thặng số bán lẻ", "danhMucThuoc": "<PERSON><PERSON>", "danhMucThuocKeNgoai": "<PERSON><PERSON> kê ngoài", "danhMucVatTu": "<PERSON><PERSON>", "danhMucXuatXu": "<PERSON><PERSON> <PERSON><PERSON>", "danhMucHoaChat": "<PERSON><PERSON><PERSON> chất", "danhMucNguyenNhanNhapVien": "<PERSON><PERSON> <PERSON><PERSON><PERSON> nhân nhập viện", "danhMucTaiNanThuongTich": "<PERSON><PERSON> m<PERSON><PERSON> nạn thư<PERSON>ng tích", "danhMucLoaiPhieu": "<PERSON><PERSON><PERSON>", "danhMucMayIn": "<PERSON><PERSON> in", "danhMucQuyenKy": "<PERSON><PERSON> ký", "danhMucQuyen": "<PERSON><PERSON>", "danhMucNhomTinhNang": "<PERSON><PERSON> mục nhóm t<PERSON>h n<PERSON>ng", "danhMucBenhPham": "<PERSON><PERSON> <PERSON><PERSON> phẩm", "danhMucBoChiDinh": "<PERSON><PERSON> m<PERSON> Bộ chỉ định", "danhMucChuyenKhoa": "<PERSON><PERSON>a", "danhMucDichVuAn": "<PERSON><PERSON> v<PERSON>", "danhMucSuatAn": "<PERSON><PERSON>n", "danhMucDichVuCDHAVaTDCN": "<PERSON><PERSON> m<PERSON> v<PERSON>n đoán hình <PERSON>nh và thăm dò chức năng", "danhMucDichVuGiuong": "<PERSON><PERSON> vụ g<PERSON>", "danhMucDichVuKhamBenh": "<PERSON><PERSON> v<PERSON> b<PERSON>nh", "danhMucDichVuNgoaiDieuTri": "<PERSON><PERSON> <PERSON><PERSON> vụ ngoài điều trị", "danhMucDichVuPTTT": "<PERSON><PERSON> m<PERSON><PERSON> vụ Phẫu thuật - <PERSON><PERSON><PERSON> thuật", "danhMucDichVuXetNghiem": "<PERSON><PERSON> v<PERSON>", "danhMucDichVuCDHA": "<PERSON><PERSON> vụ CĐHA-TDCN", "danhMucDonViTinh": "<PERSON><PERSON> m<PERSON>ơn vị t<PERSON>h", "danhMucLyDoDoiTraDichVu": "<PERSON><PERSON> m<PERSON><PERSON> do đổi trả dịch vụ", "danhMucMauKetQuaCDHAVaTDCN": "<PERSON><PERSON> mục Mẫu kết quả chẩn đoán hình ảnh và thăm dò chức năng", "danhMucMauKetQuaXetNghiem": "<PERSON><PERSON> mục Mẫu kết quả xét nghiệm", "danhMucMauKetQuaXNCoDotBien": "<PERSON><PERSON> mục Mẫu kết quả XN có đột biến", "danhMucNhomChiSo": "<PERSON><PERSON> chỉ số", "danhMucNhomDichVu": "<PERSON><PERSON> d<PERSON> vụ", "noiLayMauBenhPham": "<PERSON><PERSON><PERSON> lấy mẫu b<PERSON>nh phẩm", "danhMucNoiLayMauBenhPham": "<PERSON><PERSON> m<PERSON><PERSON> lấy mẫu bệnh phẩm", "danhMucPhuongPhapVoCam": "<PERSON><PERSON> ph<PERSON>p vô cảm", "danhMucPhuongPhapNhuom": "<PERSON><PERSON> p<PERSON><PERSON>", "danhMucViTriSinhThiet": "<PERSON><PERSON><PERSON> trí sinh thiết", "danhMucBenhVien": "<PERSON><PERSON> viện", "danhMucChucVu": "<PERSON><PERSON> v<PERSON>", "danhMucCoQuanDonVi": "<PERSON><PERSON> <PERSON><PERSON> quan đơn vị", "danhMucDanToc": "<PERSON><PERSON>c", "danhMucDiaChiHanhChinh": "<PERSON><PERSON> mục <PERSON> chỉ hành ch<PERSON>h", "danhMucDonViChiNhanh": "<PERSON><PERSON> m<PERSON>ơ<PERSON> vị chi nh<PERSON>h", "danhMucHocHamHocVi": "<PERSON><PERSON> <PERSON><PERSON> hàm học vị", "danhMucKhuVuc": "<PERSON><PERSON> v<PERSON>c", "danhMucMoiQuanHe": "<PERSON><PERSON> <PERSON><PERSON> quan hệ", "danhMucNgheNghiep": "<PERSON><PERSON> <PERSON><PERSON>", "danhMucNguoiDaiDien": "<PERSON><PERSON> <PERSON><PERSON> đ<PERSON>n", "danhMucNhanTheoDoi": "<PERSON><PERSON> theo dõi", "danhMucQuanHam": "<PERSON><PERSON><PERSON> hàm", "danhMucVanBangChuyenMon": "<PERSON><PERSON> mục <PERSON>ăn bằng chuyên môn", "danhMucChiPhiHapSayVTYTTaiSuDung": "<PERSON><PERSON> mục <PERSON> phí hấp sấy VTYT tái sử dụng", "danhMucBacSiNgoaiVien": "<PERSON><PERSON><PERSON> sĩ ngo<PERSON>i viện", "danhMucCaLamViec": "<PERSON><PERSON> làm vi<PERSON>c", "danhMucAnhLuocDoPhauThuat": "<PERSON><PERSON> mục Ảnh lược đồ Phẫu thuật", "danhMucCauHoiKhamSangLoc": "<PERSON><PERSON> mụ<PERSON>u hỏi khám sàng lọc", "cauHoiKhamSangLoc": "Câu hỏi khám sàng lọc", "danhMucCheDoChamSoc": "<PERSON><PERSON> m<PERSON>c <PERSON> độ chăm sóc", "danhMucChiSoSong": "<PERSON><PERSON> mục Chỉ số sống", "danhMucGoiMo10Ngay": "<PERSON><PERSON> m<PERSON><PERSON> mổ 10 ngày", "danhMucHoiDong": "<PERSON><PERSON> <PERSON><PERSON> đồng", "danhMucKhoa": "<PERSON><PERSON>", "danhMucKiosk": "<PERSON><PERSON>", "danhMucLoaGoiSo": "<PERSON><PERSON> m<PERSON> g<PERSON> số", "danhMucLoaiBenhAn": "<PERSON><PERSON> <PERSON><PERSON> b<PERSON><PERSON>", "danhMucLoaiBuaAn": "<PERSON><PERSON> m<PERSON><PERSON> b<PERSON><PERSON> ăn", "danhMucLoaiGiuong": "<PERSON><PERSON> g<PERSON>", "danhMucLyDoTamUng": "<PERSON><PERSON> do tạm <PERSON>ng", "danhMucLyDoDenKham": "<PERSON><PERSON><PERSON> do đến kh<PERSON>m", "danhMucMaMay": "<PERSON><PERSON> m<PERSON>y", "danhMucMaPTTTQuocTe": "<PERSON><PERSON> m<PERSON>c <PERSON>ã phẫu thuật, thủ thuật quốc tế (ICD-9)", "danhMucMaPhieuLinh": "<PERSON><PERSON> m<PERSON><PERSON> phi<PERSON>u lĩnh", "danhMucMauDienBien": "<PERSON><PERSON> mục Mẫu diễn biến", "danhMucMauKetQuaPTTT": "<PERSON><PERSON> mục Mẫu kết quả phẫu thuật - thủ thuật", "danhMucMauQMS": "<PERSON><PERSON> m<PERSON>c Mẫu QMS", "danhMucNgayNghiLe": "<PERSON><PERSON> <PERSON> nghỉ lễ", "danhMucNhomBenhTat": "<PERSON><PERSON> b<PERSON><PERSON> tật", "danhMucNhomChiPhiCoSo": "<PERSON><PERSON>ó<PERSON> chi phí", "danhMucPhanLoaiDanhGiaBMI": "<PERSON><PERSON> m<PERSON> loại đ<PERSON>h giá BMI", "danhMucPhanLoaiNguoiBenh": "<PERSON><PERSON> lo<PERSON>i ng<PERSON><PERSON> b<PERSON>nh", "danhMucPhanLoaiPHCN": "<PERSON><PERSON> loại PHCN", "danhMucPhieuIn": "<PERSON><PERSON> in", "danhMucPhong": "<PERSON><PERSON>", "danhMucPhuongThucThanhToan": "<PERSON><PERSON> mụ<PERSON> thức thanh toán", "danhMucQuyTrinhXetNghiem": "<PERSON><PERSON> m<PERSON><PERSON> trình x<PERSON>t nghi<PERSON>m", "danhMucSoHieuGiuong": "<PERSON><PERSON> m<PERSON> hiệu g<PERSON>", "danhMucTaiLieuHDSD": "<PERSON><PERSON> mục Tài liệu hướng dẫn sử dụng", "danhMucTaiSanKhac": "<PERSON><PERSON> sản kh<PERSON>c", "danhMucTheBaoHiem": "<PERSON><PERSON> m<PERSON>c Thẻ b<PERSON><PERSON> hiểm", "danhMucToaNha": "<PERSON><PERSON> m<PERSON> nh<PERSON>", "danhMucVacXin": "<PERSON><PERSON> xin", "danhMucKhaiBaoTuongTacThuoc": "<PERSON><PERSON> m<PERSON><PERSON> b<PERSON>o tư<PERSON> tác thuốc", "danhMucChuongTrinhGiamGia": "<PERSON><PERSON> trình giảm giá", "danhMucHangThe": "<PERSON><PERSON> Thẻ", "danhMucNguonNguoiBenh": "<PERSON><PERSON> <PERSON><PERSON> ng<PERSON><PERSON> b<PERSON>nh", "danhMucBaoCao": "<PERSON><PERSON>o", "danhMucBenhYHocCoTruyen": "<PERSON><PERSON> m<PERSON> y học cổ truyền", "danhMucNhanVien": "<PERSON><PERSON> mục nhân viên", "danhMucMauBenhAnVaoVien": "<PERSON><PERSON> mục Mẫu bệnh án vào viện", "danhMucDieuTriKetHop": "<PERSON><PERSON> mục điều tr<PERSON> kết hợp", "mauBenhAnVaoVien": "Mẫu bệnh án vào viện", "maNV": "Mã NV", "bangChuyenMon": "Bằng chuyên môn", "chungChi": "Chứng chỉ", "thongTinKhoaPhong": "Thông tin khoa phòng", "ngaySinh": "<PERSON><PERSON><PERSON>", "soBHXH": "Số BHXH", "mstTenTKKy": "MST/ Tên tài k<PERSON>n ký", "matKhauHDDT": "M<PERSON>t <PERSON>u HĐĐT", "hienThiThongTin": "<PERSON><PERSON><PERSON> thị thông tin", "anThongTin": "Ẩn thông tin", "anhChuKy": "Ảnh chữ ký", "datKhamOnline": "Đặt khám online", "chungThuSoMkKy": "<PERSON><PERSON><PERSON> thư số/ <PERSON><PERSON> ký", "danhHieu": "<PERSON><PERSON>", "nha": "Nhà", "khoaQuanLy": "<PERSON><PERSON><PERSON> qu<PERSON>n lý", "tenVietTat": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> t<PERSON>", "nhapTenVietTat": "<PERSON><PERSON><PERSON><PERSON> tên viết tắt", "loaiKetQua": "<PERSON><PERSON><PERSON> kết quả", "nhapLoaiKetQua": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON> kết quả", "ketQuaThamChieu": "<PERSON><PERSON><PERSON> quả tham chiếu", "chonKetQuaThamChieu": "<PERSON><PERSON><PERSON> kết quả tham chiếu", "tyLeThanhToanDV": "Tỷ lệ thanh toán DV", "vuiLongNhapTyLeThanhToanDV": "<PERSON><PERSON> lòng nhập tỷ lệ thanh toán DV", "vuiLongNhapTyLeThanhToanDVKhongQua3KyTu": "<PERSON><PERSON> lòng nhập tỷ lệ thanh toán DV không quá 3 ký tự", "nhapTyLeThanhToanDV": "Nhập tỷ lệ thanh toán DV", "phieuChiDinh": "<PERSON><PERSON>u chỉ định", "chonBaoCao": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "chiSoNuThap": "Chỉ số nữ thấp", "nhapChiSoNuThap": "<PERSON><PERSON><PERSON><PERSON> chỉ số nữ thấp", "chiSoNuCao": "Chỉ số nữ cao", "nhapChiSoNuCao": "<PERSON><PERSON><PERSON><PERSON> chỉ số nữ cao", "chiSoNamThap": "Chỉ số nam thấp", "nhapChiSoNamThap": "<PERSON><PERSON><PERSON><PERSON> chỉ số nam thấp", "chiSoNamCao": "Chỉ số nam cao", "nhapChiSoNamCao": "<PERSON><PERSON><PERSON><PERSON> chỉ số nam cao", "loaiMau": "<PERSON><PERSON><PERSON> máu", "chonLoaiMau": "<PERSON><PERSON><PERSON> lo<PERSON>i máu", "theTich": "<PERSON><PERSON><PERSON> tích", "nhapTheTich": "<PERSON><PERSON><PERSON><PERSON> thể tích", "soNgaySuDung": "<PERSON><PERSON> ngày sử dụng", "nhapSoNgaySuDung": "<PERSON><PERSON><PERSON><PERSON> số ngày sử dụng", "dvt": "ĐVT", "chonDvt": "Chọn ĐVT", "nhomDVCap1": "Nhóm dịch vụ cấp 1", "chonNhomDVCap1": "<PERSON><PERSON><PERSON> v<PERSON> cấp 1", "nhomDVCap2": "Nhóm d<PERSON><PERSON> v<PERSON> cấp 2", "chonNhomDVCap2": "<PERSON><PERSON><PERSON> v<PERSON> cấ<PERSON> 2", "nhomDVCap3": "Nhóm d<PERSON><PERSON> v<PERSON> cấp 3", "chonNhomDVCap3": "<PERSON><PERSON><PERSON> v<PERSON> cấ<PERSON> 3", "truongHopKeDv": "<PERSON>rư<PERSON><PERSON> hợp kê DV", "chonTruongHopKeDv": "Chọn trư<PERSON><PERSON> hợp kê DV", "tiepDonCLS": "<PERSON><PERSON><PERSON><PERSON>", "chonTiepDonCLS": "<PERSON><PERSON><PERSON> ti<PERSON> đ<PERSON>", "maSoQuyetDinh": "<PERSON>ã số quyết định", "nhapMaSoQuyetDinh": "<PERSON><PERSON><PERSON><PERSON> mã số quyết định", "ngayQuyetDinh": "<PERSON><PERSON><PERSON> quy<PERSON> đ<PERSON>", "chonNgayQuyetDinh": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> quyế<PERSON> đ<PERSON>nh", "nguonKhacChiTra": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>c chi trả", "chonNguonKhacChiTra": "<PERSON><PERSON><PERSON> nguồn khác chi trả", "chiPhiVanChuyen": "<PERSON> phí vận chuyển", "chonChiPhiVanChuyen": "<PERSON><PERSON>n chi phí vận chuyển", "maGuiLISPACS": "Mã gửi LIS/PACS", "nhapMaGuiLISPACS": "Nhập mã gửi LIS/PACS", "donViKetNoi": "Đơn vị kết n<PERSON>i", "chonDonViKetNoi": "<PERSON><PERSON><PERSON> đơn vị kết nối", "yeuCauBenhPham": "<PERSON><PERSON><PERSON> c<PERSON>u b<PERSON>nh phẩm", "chonYeuCauBenhPham": "<PERSON><PERSON><PERSON> yêu cầu bệnh phẩm", "dvCoKetQuaLau": "DV có kết quả lâu", "chonDvCoKetQuaLau": "Chọn DV có kết quả lâu", "coThuNgoai": "<PERSON><PERSON> thu ngoài", "chonThuNgoai": "<PERSON><PERSON><PERSON> thu ngoài", "chonYeuCau": "<PERSON><PERSON><PERSON> yêu cầu", "theoYeuCau": "<PERSON>", "tuDongTaoChiSoCon": "Tự động tạo chỉ số con", "chonTuDongTaoChiSoCon": "<PERSON><PERSON>n tự động tạo chỉ số con", "chonLoaiHinhThuc": "<PERSON><PERSON><PERSON> lo<PERSON>i hình thức", "vuiLongChonApDungTuNgay": "<PERSON><PERSON> lòng chọn áp dụng từ ngày", "tenPhong": "<PERSON><PERSON><PERSON> ph<PERSON>ng", "diaChiPhong": "Địa chỉ phòng", "nhapDiaChiPhong": "<PERSON><PERSON><PERSON><PERSON> địa chỉ phòng", "doiTuongKhamChuaBenh": "<PERSON><PERSON><PERSON> t<PERSON> kh<PERSON>m chữa b<PERSON>nh", "chonDoiTuongKhamChuaBenh": "<PERSON><PERSON><PERSON> đ<PERSON>i tư<PERSON> khám chữa b<PERSON>nh", "soLuongToiDaSuDungTrong1MHS": "Số lượng tối đa sử dụng trong 1 MHS", "nhapSoLuongToiDaSuDungTrong1MHS": "<PERSON><PERSON><PERSON><PERSON> số lượng tối đa sử dụng trong 1 MHS", "soLuongToiDaSuDungTrong1Ngay": "Số lượng tối đa sử dụng trong 1 ngày", "nhapSoLuongToiDaSuDungTrong1Ngay": "<PERSON><PERSON><PERSON><PERSON> số lượng tối đa sử dụng trong 1 ngày", "soNgayDuocThucHienLanTiepTheo": "<PERSON><PERSON> ng<PERSON><PERSON> đ<PERSON><PERSON><PERSON> thực hiện lần tiếp theo", "nhapSoNgayDuocThucHienLanTiepTheo": "<PERSON><PERSON><PERSON><PERSON> số ngày đ<PERSON><PERSON><PERSON> thực hiện lần tiếp theo", "dieuKienThanhToanBaoHiem": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>n <PERSON>h to<PERSON> b<PERSON>o hi<PERSON>m", "chonNhom": "<PERSON><PERSON><PERSON>", "loaiThoiGian": "<PERSON><PERSON><PERSON> thời gian", "chonLoaiThoiGian": "<PERSON><PERSON><PERSON> lo<PERSON>i thời gian", "thoiDiemChiDinh": "Th<PERSON>i điểm chỉ định", "chonThoiDiemChiDinh": "<PERSON><PERSON><PERSON> thời điểm chỉ định", "nhapMaDichVu": "<PERSON><PERSON><PERSON><PERSON> mã dịch vụ", "chonLoaiDoiTuong": "<PERSON><PERSON><PERSON> lo<PERSON>i đối tư<PERSON>", "soLuong": "Số lượng", "timSoLuong": "<PERSON><PERSON><PERSON> l<PERSON>", "nhapSoLuong": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "vuiLongNhapSoLuong": "<PERSON><PERSON> lòng nhập số lượng", "thoiGianCoGiaTri": "<PERSON>hời gian có giá trị", "doiTuongApDung": "<PERSON><PERSON><PERSON> t<PERSON> dụng", "chuyenKhoaKSK": "Chuyên khoa KSK", "chonChuyenKhoaKSK": "<PERSON><PERSON><PERSON> chuy<PERSON>n k<PERSON>a KSK", "soNgayCanhBaoKeDichVuBHYT": "Số ngày cảnh báo kê dịch vụ BHYT", "nhapSoNgayCanhBaoKeDichVuBHYT": "<PERSON>hập số ngày cảnh báo kê dịch vụ BHYT", "lenTTBA": "Lên TTBA", "tachSoLuongKhiKe": "<PERSON><PERSON><PERSON> số lượng khi kê", "thuNgoai": "<PERSON><PERSON> ng<PERSON>i", "tachPhieuChiDinhKhiTrungDV": "<PERSON><PERSON><PERSON> phiếu chỉ định khi trùng DV", "dvTheoYeuCau": "DV theo yêu c<PERSON>u", "dungChoCovid": "<PERSON><PERSON><PERSON> cho <PERSON>", "duongTinh": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "amTinh": "<PERSON><PERSON>", "thiLuc": "<PERSON><PERSON><PERSON> l<PERSON>c", "danhMucThiLuc": "<PERSON><PERSON> lực", "maThiLuc": "<PERSON><PERSON> thị lực", "tenThiLuc": "<PERSON><PERSON><PERSON> thị lực", "vuiLongNhapMaThiLuc": "<PERSON><PERSON> lòng nhập mã thị lực", "vuiLongNhapTenThiLuc": "<PERSON><PERSON> lòng nhập tên thị lực", "maDoiTac": "<PERSON><PERSON> đối tác", "timMaDoiTac": "<PERSON><PERSON><PERSON> đối tác", "maDoiTacCon": "<PERSON>ã đối tác con", "nhapMaDoiTacCon": "<PERSON><PERSON><PERSON><PERSON> mã đối tác con", "tenDoiTacCon": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> tác con", "nhapTenDoiTacCon": "<PERSON><PERSON><PERSON><PERSON> tên đối tác con", "vuiLongNhapMaDoiTac": "<PERSON><PERSON> lòng nhập <PERSON>ã đối tác", "vuiLongNhapMaDoiTacKhongQua20KyTu": "<PERSON><PERSON> lòng nhập Mã đối tác không quá 20 ký tự", "tenDoiTac": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON>c", "timTenDoiTac": "<PERSON><PERSON><PERSON> đ<PERSON> tác", "vuiLongNhapTenDoiTac": "<PERSON><PERSON> lòng nhập Tên đ<PERSON>i tác", "vuiLongNhapTenDoiTacKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập Tên đối tác không quá 1000 ký tự", "nhomDv": "Nhóm DV", "vuiLongChonNhomDv": "<PERSON>ui lòng chọn nhóm DV", "loaiDoiTac": "<PERSON><PERSON><PERSON> đối tác", "chonLoaiDoiTac": "<PERSON><PERSON><PERSON> lo<PERSON>i đối tác", "vuiLongChonLoaiDoiTac": "<PERSON><PERSON> lòng chọn loại đối tác", "loaiDichVu": "<PERSON><PERSON><PERSON> d<PERSON> vụ", "vuiLongChonLoaiDichVu": "<PERSON><PERSON> lòng chọn lo<PERSON>i dịch vụ", "maSoThue": "<PERSON><PERSON> số thuế", "timMaSoThue": "<PERSON><PERSON><PERSON> mã số thuế", "nhapMaSoThue": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "soTaiKhoan": "Số tài <PERSON>n", "timSoTaiKhoan": "<PERSON><PERSON><PERSON> số tài <PERSON>n", "nhapSoTaiKhoan": "<PERSON><PERSON><PERSON><PERSON> số tài <PERSON>n", "vuiLongNhapTenNguoiDaiDien": "<PERSON><PERSON> lòng nhập tên người đại di<PERSON>n", "chucVuNguoiDaiDien": "<PERSON><PERSON><PERSON> vụ ng<PERSON><PERSON>i đạ<PERSON>n", "timChucVuNguoiDaiDien": "<PERSON><PERSON><PERSON> ch<PERSON> vụ ng<PERSON>ời đại <PERSON>n", "vuiLongNhapChucVuNguoiDaiDien": "<PERSON><PERSON> lòng nh<PERSON>p chức vụ người đại di<PERSON>n", "sdtNguoiDaiDien": "SĐT người đạ<PERSON>n", "timSdtNguoiDaiDien": "Tìm SĐT người đại di<PERSON>n", "vuiLongNhapSdtNguoiDaiDien": "<PERSON><PERSON> lò<PERSON> nhập SĐT người đại diện", "nguoiDauMoi": "<PERSON><PERSON><PERSON><PERSON> đầu mối", "tenNguoiDauMoi": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON><PERSON> đầu mối", "timNguoiDauMoi": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i đầu mối", "vuiLongNhapTenNguoiDauMoi": "<PERSON><PERSON> lòng nhập tên người đầu mối", "sdtNguoiDauMoi": "SĐT ngư<PERSON>i đầu mối", "timSdtNguoiDauMoi": "Tìm SĐT người đầu mối", "vuiLongNhapSdtNguoiDauMoi": "<PERSON><PERSON> lò<PERSON> nhập SĐT người đầu mối", "emailNguoiDauMoi": "<PERSON><PERSON> đầu mối", "timEmailNguoiDauMoi": "Tìm email ngư<PERSON>i đầu mối", "vuiLongNhapEmailNguoiDauMoi": "<PERSON><PERSON> lò<PERSON> nh<PERSON>p email ngư<PERSON>i đầu mối", "tenNganHang": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "chonTenNganHang": "<PERSON><PERSON><PERSON> tên ngân hàng", "timTenNganHang": "<PERSON><PERSON><PERSON> tên ngân hàng", "vuiLongNhapTenNganHang": "<PERSON><PERSON> lòng nhập tên ngân hàng", "chuTaiKhoanNganHang": "<PERSON><PERSON> tài khoản ngân hàng", "timChuTaiKhoanNganHang": "T<PERSON>m chủ tài khoản ngân hàng", "vuiLongNhapChuTaiKhoanNganHang": "<PERSON><PERSON> lòng nhập <PERSON> tài khoản ngân hàng", "nguoiChiCongTac": "<PERSON>ư<PERSON><PERSON> chi cộng tác", "timNguoiChiCongTac": "<PERSON><PERSON><PERSON> ng<PERSON> chi cộng tác", "vuiLongNhapNguoiChiCongTac": "<PERSON><PERSON> lòng nhập người chi cộng tác", "sdtNguoiChiCongTac": "SĐT người chi cộng tác", "timSdtNguoiChiCongTac": "Tìm SĐT người chi cộng tác", "vuiLongNhapSdtNguoiCongTac": "<PERSON><PERSON> lò<PERSON> nhập SĐT người cộng tác", "diaChi": "Địa chỉ", "nhapDiaChi": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "khongGuiTenDonVi": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i tên đơn vị", "hanMucQuy": "<PERSON><PERSON><PERSON> mức quỹ", "soTienDaSuDung": "Số tiền đã sử dụng", "vuiLongNhapSoTienHanMucQuy": "<PERSON><PERSON> lòng nhập số tiền Hạn mức quỹ", "khongDuocThucHienSlNhieuCungLuc": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> thực hiện SL nhiều cùng lúc", "chonKhongDuocThucHienSlNhieuCungLuc": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> thực hiện SL nhiều cùng lúc", "nhomBenhTat": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> tật", "tatCaNhomBenhTat": "<PERSON><PERSON><PERSON> cả nhóm b<PERSON>nh tật", "chonNhomBenhTat": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> b<PERSON><PERSON> tật", "maNhom": "Mã nhóm", "timTheoMa": "<PERSON><PERSON><PERSON> theo mã", "tenNhom": "<PERSON><PERSON><PERSON>", "timTheoTenNhom": "<PERSON><PERSON><PERSON> theo tên nh<PERSON>m", "tenChuong": "<PERSON><PERSON><PERSON>", "chonTenChuong": "<PERSON><PERSON><PERSON> tên ch<PERSON>", "tatCaChuongBenh": "<PERSON><PERSON><PERSON> c<PERSON> ch<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "chonChuongBenh": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "chuongBenh": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "nhomBenhChinh": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>h", "nhomBenhPhuI": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> phụ I", "nhomBenhPhuII": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> phụ II", "loaiBenh": "<PERSON><PERSON><PERSON> b<PERSON>", "tenBenh": "<PERSON><PERSON><PERSON> b<PERSON>", "vuiLongNhapMaNhom": "<PERSON><PERSON> lòng nhập mã nhóm", "vuiLongNhapTenNhom": "<PERSON><PERSON> lòng nhập tên nhóm", "vuiLongNhapMaNhomKhongQuaNumKyTu": "<PERSON>ui lòng nhập mã nhóm không quá {{num}} ký tự!", "vuiLongNhapTenNhomKhongQuaNumKyTu": "<PERSON>ui lòng nhập tên nhóm không quá {{num}} ký tự!", "vuiLongChonChuongBenh": "<PERSON><PERSON> lòng chọn ch<PERSON> b<PERSON>nh", "nguongTamUng": "Ngưỡng tạm <PERSON>ng", "vuiLongNhapSttNhomBenh": "<PERSON><PERSON> lòng nhập STT nhóm bệnh", "sttNhomBenh": "STT nhóm bệnh", "chonNhomBenh": "<PERSON><PERSON><PERSON> b<PERSON>nh", "capNhatThanhCongDuLieuNhomBenhTat": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu nhóm bệnh tật!", "themMoiThanhCongDuLieuNhomBenhTat": "Thêm mới thành công dữ liệu nhóm bệnh tật!", "capNhatThanhCongDuLieuTuongTacThuoc": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu tương tác thuốc!", "themMoiThanhCongDuLieuTuongTacThuoc": "Thêm mới thành công dữ liệu tương tác thuốc!", "capNhatThanhCongDuLieuTaiNanThuongTich": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu tai nạn thương tích!", "themMoiThanhCongDuLieuTaiNanThuongTich": "Thêm mới thành công dữ liệu tai nạn thương tích!", "batHuongBh": "Bật hưởng BH", "tatHuongBh": "Tắt hưởng BH", "banCoChacChanBatXacNhanHuongBaoHiemKhong": "Bạn có chắc chắn bật xác nhận hưởng bảo hiểm không?", "banCoChacChanTatXacNhanHuongBaoHiemKhong": "Bạn có chắc chắn tắt xác nhận hưởng bảo hiểm không", "thongTinVatTu": "<PERSON>h<PERSON>ng tin vật tư", "xangDau": "<PERSON><PERSON><PERSON>", "danhMucXangDau": "<PERSON><PERSON>", "maXangDau": "<PERSON><PERSON> x<PERSON>ng dầu", "vuiLongNhapMaXangDau": "<PERSON><PERSON> lòng nhập mã xăng dầu", "loaiXangDau": "<PERSON><PERSON><PERSON> x<PERSON>ng d<PERSON>u", "chonLoaiXangDau": "<PERSON><PERSON><PERSON> lo<PERSON>i xăng dầu", "vuiLongNhapLoaiXangDau": "<PERSON><PERSON> lòng nhập loại xăng dầu", "danhMucPhanLoaiPhuongPhapVoCam": "<PERSON><PERSON> m<PERSON> lo<PERSON>i ph<PERSON><PERSON><PERSON> pháp vô cảm", "maPhanLoai": "Mã phân loại", "tenPhanLoai": "<PERSON><PERSON>n ph<PERSON> lo<PERSON>i", "vuiLongNhapMaPhanLoai": "<PERSON><PERSON> lòng nhập mã phân loại", "vuiLongNhapTenPhanLoai": "<PERSON><PERSON> lòng nhập tên phân lo<PERSON>i", "phanLoaiPhuongPhapVoCam": "Phân loại phư<PERSON><PERSON> pháp vô cảm", "vuiLongNhapMaPhuongPhapVoCam": "<PERSON><PERSON> lòng nhập mã phương pháp vô cảm", "vuiLongNhapTenPhuongPhapVoCam": "<PERSON><PERSON> lòng nhập tên phương pháp vô cảm", "vuiLongChonPhanLoaiPhuongPhapVoCam": "<PERSON><PERSON> lòng chọn phân loại phương pháp vô cảm", "nhapKetQuaThamhieu": "<PERSON><PERSON><PERSON><PERSON> kết quả tham chiếu", "maChiSoCon": "Mã chỉ số con", "timMaChiSoCon": "<PERSON><PERSON><PERSON> mã chỉ số con", "nhapMaChiSoCon": "<PERSON><PERSON><PERSON><PERSON> mã chỉ số con", "vuiLongNhapMaChiSoCon": "<PERSON><PERSON> lòng nhập mã chỉ số con", "vuiLongNhapMaChiSoConKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã chỉ số con không quá 20 ký tự", "tenChiSoCon": "Tên chỉ số con", "timTenChiSoCon": "<PERSON><PERSON><PERSON> tên chỉ số con", "nhapTenChiSoCon": "<PERSON><PERSON><PERSON><PERSON> tên chỉ số con", "vuiLongNhapTenChiSoCon": "<PERSON><PERSON> lòng nhập tên chỉ số con", "vuiLongNhapTenChiSoConKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên chỉ số con không quá 1000 ký tự", "timMaTuongDuong": "<PERSON><PERSON><PERSON> mã tương đ<PERSON>ng", "vuiLongNhapMaTuongDuong": "<PERSON><PERSON> lòng nhập mã tương đương", "timTenDuongDuong": "<PERSON><PERSON><PERSON> tên tươ<PERSON> đ<PERSON>ng", "nhapTenDuongDuong": "<PERSON><PERSON><PERSON><PERSON> tên tươ<PERSON> đ<PERSON>", "vuiLongNhapTenTuongDuong": "<PERSON><PERSON> lòng nhập tên tương đương", "maGuiLis": "Mã gửi LIS", "timMaGuiLis": "Tìm mã gửi LIS", "nhapMaGuiLis": "Nhập mã gửi LIS", "vuiLongNhapMaGuiLis": "<PERSON><PERSON> lòng nhập mã gửi LIS", "taiNanThuongTich": "<PERSON> nạn thư<PERSON> tích", "chonLoaiKetQua": "<PERSON><PERSON><PERSON> loai kết quả", "ketQuaBinhThuong": "<PERSON><PERSON><PERSON> qu<PERSON> b<PERSON>nh thư<PERSON>", "timKetQuaBinhThuong": "<PERSON><PERSON><PERSON> kết quả bình thường", "nhapKetQuaBinhThuong": "<PERSON><PERSON><PERSON><PERSON> kết quả bình thường", "nhomChiSoCon": "Nhóm chỉ số con", "chonNhomChiSoCon": "<PERSON><PERSON><PERSON> nhóm chỉ số con", "vuiLongChonChiSoCon": "<PERSON>ui lòng chọn chỉ số con!", "donVi": "Đơn vị", "timDonVi": "<PERSON><PERSON><PERSON> vị", "nhapDonVi": "<PERSON><PERSON><PERSON><PERSON> đơn vị", "vuiLongNhapMaPhieu": "<PERSON><PERSON> lòng nhập mã phiếu", "vuiLongNhapNoiDungPhieu": "<PERSON><PERSON> lòng nh<PERSON>p nội dung phiếu", "vuiLongChonLoaiViTri": "<PERSON><PERSON> lòng chọn vị trí", "vuiLongNhapSttPhieu": "<PERSON><PERSON> lòng nhập STT phiếu", "vuiLongDienDayDuTt": "<PERSON>ui lòng điền đầy đủ thông tin!", "phieuDayISC": "<PERSON><PERSON><PERSON>y ISC", "chonPhieuDayISC": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> đ<PERSON>y ISC", "vuiLongNhapMaPttt": "<PERSON><PERSON> lòng nhập mã PTTT", "vuiLongNhapMaPtttKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã PTTT không quá 20 ký tự", "maDayBHYT": "Mã đẩy BHYT", "vuiLongNhapMaTaiNanThuongTichKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã tai nạn thương tích không quá 20 ký tự!", "vuiLongNhapTenTaiNanThuongTichKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên tai nạn thương tích không quá 1000 ký tự!", "vuiLongNhapMaDayBHYT": "<PERSON>ui lòng nhập mã đẩy BHYT!", "vuiLongNhapMaDayBHYTKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã đẩy BHYT không quá 20 ký tự!", "maPttt": "Mã PTTT", "tenPttt": "Tên PTTT", "vuiLongNhapTenPttt": "<PERSON><PERSON> lòng nhập tên PTTT", "vuiLongNhapTenPtttKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập mã PTTT không quá 1000 ký tự", "maGuiHoaDon": "<PERSON><PERSON> gửi hóa đơn", "vuiLongNhapMucDoUuTien": "<PERSON><PERSON> lòng nhập mức độ ưu tiên", "loaiPhuongThucTt": "Loạ<PERSON> ph<PERSON><PERSON><PERSON> thức TT", "nccKhacBv": "NCC khác BV", "tienMat": "Tiền mặt", "vuiLongNhapUuTienNhoHon": "<PERSON><PERSON> lòng nhập ưu tiên nhỏ hơn {{num}}", "dichVuPttt": "Dịch vụ PTTT", "banCanPhaiXoaHetCacTuongTacThuocChiSoCon": "Bạn cần ph<PERSON>i xoá hết các tương tác thuốc xét nghiệm chỉ số con!", "benhYHocCoTruyen": "<PERSON><PERSON><PERSON> y học cổ truyền", "maBenhYHCY": "<PERSON>ã bệnh YHCT", "tenBenhYHCY": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> YHCT", "maBenhICD10": "Mã bệnh ICD 10", "tenBenhICD10": "<PERSON><PERSON><PERSON> b<PERSON>nh ICD 10", "vuiLongNhapMaBenhYHCY": "<PERSON><PERSON> lòng nhập mã bệnh YHCT", "vuiLongNhapTenBenhYHCY": "<PERSON><PERSON> lòng nhập tên b<PERSON>nh YHCT", "vuiLongChonMaBenhICD10": "<PERSON><PERSON> lòng chọn mã bệnh ICD 10", "giaNhapSauVATNhoNhat": "<PERSON><PERSON><PERSON> nh<PERSON>p sau VAT nhỏ nhất", "nhapGiaNhapSauVATNhoNhat": "<PERSON><PERSON><PERSON><PERSON> giá nhập sau VAT nhỏ nhất", "giaNhapSauVATLonNhat": "<PERSON><PERSON><PERSON> nh<PERSON>p sau VAT lớn nhất", "nhapGiaNhapSauVATLonNhat": "<PERSON>hậ<PERSON> gi<PERSON> nhập sau VAT lớn nhất", "nhapThangSoBanLe": "<PERSON><PERSON><PERSON><PERSON> thặng số bán lẻ", "vuiLongNhapThangSoBanLe": "<PERSON><PERSON> lòng nhập thặng số bán lẻ", "giaTriPhaiLaSoNguyenDuong": "<PERSON><PERSON><PERSON> trị phải là số nguyên dư<PERSON>", "nhomThuocCap1": "Nhóm thu<PERSON> cấp 1", "maNhomThuocCap1": "Mã nhóm thuốc cấp 1", "maNhomThuocCap3": "Mã nhó<PERSON> thu<PERSON> cấp 3", "maNhomHoaChatCap1": "Mã nhóm hóa chất cấp 1", "maNhomThuocCap2": "Mã nhó<PERSON> thu<PERSON> cấ<PERSON> 2", "maNhomHoaChatCap2": "Mã nhóm hóa chất cấp 2", "maNhomVatTuCap1": "Mã nhóm vậ<PERSON> tư cấp 1", "maNhomVatTuCap2": "Mã nhóm v<PERSON><PERSON> tư cấp 2", "maNhomVatTuCap3": "Mã nhóm v<PERSON><PERSON> tư cấp 3", "maNhomVatTuCap4": "Mã nhóm v<PERSON><PERSON> tư cấp 4", "vuiLongNhapMaNhomThuocCap1": "<PERSON><PERSON> lòng nhập mã nhóm thuốc cấp 1!", "vuiLongNhapMaNhomThuocCap2": "<PERSON><PERSON> lòng nhập mã nhóm thuốc cấp 2!", "vuiLongNhapMaNhomThuocCap3": "<PERSON><PERSON> lòng nhập mã nhóm thuốc cấp 3!", "vuiLongNhapTenNhomThuocCap1": "<PERSON><PERSON> lòng nhập tên nhóm thuốc cấp 1!", "vuiLongChonTenNhomThuocCap1": "<PERSON>ui lòng chọn tên nhóm thuốc cấp 1!", "vuiLongNhapTenNhomThuocCap2": "<PERSON><PERSON> lòng nhập tên nhóm thuốc cấp 2!", "vuiLongNhapTenNhomThuocCap3": "<PERSON><PERSON> lòng nhập tên nhóm thuốc cấp 3!", "vuiLongNhapMaNhomHoaChatCap1": "<PERSON><PERSON> lòng nhập mã nhóm hóa chất cấp 1", "vuiLongNhapTenNhomHoaChatCap1": "<PERSON><PERSON> lòng nhập tên nhóm hóa chất cấp 1", "vuiLongChonTenNhomHoaChatCap1": "<PERSON><PERSON> lòng chọn tên nhóm hóa chất cấp 1", "vuiLongNhapMaNhomHoaChatCap2": "<PERSON><PERSON> lòng nhập mã nhóm hóa chất cấp 2", "vuiLongNhapTenNhomHoaChatCap2": "<PERSON><PERSON> lòng nhập tên nhóm hóa chất cấp 2", "vuiLongNhapMaNhomVatTuCap1": "<PERSON><PERSON> lòng nhập mã nhóm vật tư cấp 1", "vuiLongNhapTenNhomVatTuCap1": "<PERSON><PERSON> lòng nhập tên nhóm vật tư cấp 1", "vuiLongNhapMaNhomVatTuCap2": "<PERSON><PERSON> lòng nhập mã nhóm vật tư cấp 2", "vuiLongNhapMaNhomVatTuCap3": "<PERSON><PERSON> lòng nhập mã nhóm vật tư cấp 3", "vuiLongNhapMaNhomVatTuCap4": "<PERSON><PERSON> lòng nhập mã nhóm vật tư cấp 4", "vuiLongChonTenNhomVatTuCap1": "<PERSON><PERSON> lòng chọn tên nhóm vật tư cấp 1", "vuiLongNhapTenNhomVatTuCap2": "<PERSON><PERSON> lòng nhập tên nhóm vật tư cấp 2", "vuiLongChonTenNhomVatTuCap2": "<PERSON><PERSON> lòng chọn tên nhóm vật tư cấp 2", "vuiLongNhapTenNhomVatTuCap3": "<PERSON><PERSON> lòng nhập tên nhóm vật tư cấp 3", "vuiLongNhapTenNhomVatTuCap4": "<PERSON><PERSON> lòng nhập tên nhóm vật tư cấp 4", "timMaNhomThuocCap1": "<PERSON><PERSON><PERSON> mã nhóm thuốc cấp 1", "timMaNhomHoaChatCap1": "<PERSON>ìm mã nhóm hóa chất cấp 1", "timMaNhomHoaChatCap2": "<PERSON><PERSON><PERSON> mã nhóm hóa chất cấp 2", "timMaNhomVatTuCap1": "<PERSON><PERSON><PERSON> mã nhóm vật tư cấp 1", "timMaNhomVatTuCap2": "<PERSON><PERSON><PERSON> mã nhóm vật tư cấp 2", "timMaNhomVatTuCap3": "<PERSON><PERSON><PERSON> mã nhóm vật tư cấp 3", "timMaNhomVatTuCap4": "<PERSON><PERSON><PERSON> mã nhóm vật tư cấp 4", "tenNhomThuocCap1": "<PERSON><PERSON><PERSON> thu<PERSON> cấp 1", "tenNhomThuocCap3": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> cấ<PERSON> 3", "tenNhomHoaChatCap1": "<PERSON>ên <PERSON> hóa chất cấp 1", "tenNhomHoaChatCap2": "<PERSON><PERSON><PERSON> hóa chất cấp 2", "tenNhomVatTuCap1": "<PERSON><PERSON><PERSON> vậ<PERSON> tư cấp 1", "tenNhomVatTuCap2": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> tư cấp 2", "tenNhomVatTuCap3": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> tư cấp 3", "tenNhomVatTuCap4": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> tư cấp 4", "timTenNhomThuocCap1": "<PERSON><PERSON><PERSON> tên nh<PERSON>m thu<PERSON> cấp 1", "timTenNhomHoaChatCap1": "<PERSON><PERSON><PERSON> tên nhóm hóa chất cấp 1", "timTenNhomVatTuCap1": "<PERSON><PERSON><PERSON> tên nhóm vật tư cấp 1", "nhomThuocCap2": "<PERSON><PERSON><PERSON><PERSON> cấ<PERSON> 2", "nhomThuocCap3": "<PERSON><PERSON><PERSON><PERSON> cấ<PERSON> 3", "tenNhomThuocCap2": "<PERSON><PERSON><PERSON> th<PERSON> cấ<PERSON> 2", "timTenNhomThuocCap2": "<PERSON><PERSON><PERSON> tên nh<PERSON> thu<PERSON> cấp 2", "timTenNhomThuocCap3": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> cấ<PERSON> 3", "timTenNhomHoaChatCap2": "<PERSON><PERSON><PERSON> tên nhóm hóa chất cấp 2", "timTenNhomVatTuCap2": "<PERSON><PERSON><PERSON> tên nh<PERSON>m vật tư cấp 2", "timTenNhomVatTuCap3": "<PERSON><PERSON><PERSON> tên nh<PERSON>m vật tư cấp 3", "timTenNhomVatTuCap4": "<PERSON><PERSON><PERSON> tên nh<PERSON>m vật tư cấp 4", "chonNhomThuocCap1": "<PERSON><PERSON><PERSON> thu<PERSON> cấp 1", "chonNhomThuocCap2": "<PERSON><PERSON><PERSON> thu<PERSON> cấp 2", "soLanPerNgay": "Số lần/ngày", "timSoLanPerNgay": "<PERSON><PERSON><PERSON> s<PERSON> lần/ngày", "soVienPerLan": "Số viên/lần", "timSoVienPerLan": "<PERSON><PERSON><PERSON> số viên/ngày", "danhMucLoaiNhiemKhuan": "<PERSON><PERSON><PERSON>", "loaiNhiemKhuan": "<PERSON><PERSON><PERSON>", "maLoaiNhiemKhuan": "<PERSON><PERSON> lo<PERSON>", "vuiLongNhapMaLoaiNhiemKhuan": "<PERSON><PERSON> lòng nhập mã lo<PERSON>i n<PERSON><PERSON><PERSON>", "tenLoaiNhiemKhuan": "<PERSON><PERSON><PERSON> lo<PERSON>", "vuiLongNhapTenLoaiNhiemKhuan": "<PERSON><PERSON> lòng nhập tên lo<PERSON>i nhi<PERSON>n", "logo": "Logo", "maBhyt": "Mã BHYT", "tenBh": "<PERSON><PERSON>n <PERSON>", "nhaThuTien": "<PERSON><PERSON><PERSON> thu tiền", "giuongKeHoach": "Giư<PERSON><PERSON> kế hoạch", "giuongThucKe": "<PERSON><PERSON><PERSON><PERSON><PERSON> thực kê", "tinhChatKhoa": "<PERSON><PERSON><PERSON> chất khoa", "nguongTamUngDieuTri": "Ngưỡng tạm ứng điều trị", "duocPhepChonGiuongTaiKhoa": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>p chọn giư<PERSON>ng tại khoa", "tuDongDuyetMuonNb": "Tự động duyệt mượn NB", "chuyenKhoaPttt": "Chuyển khoa PTTT", "tiepNhanNoiTru": "<PERSON><PERSON><PERSON><PERSON> nhận nội trú", "khongThanhToanSau": "<PERSON><PERSON><PERSON><PERSON> thanh toán sau", "khongTuDongDuyetMuonNb": "Không tự động duyệt mượn NB", "tuDongChuyenKhoaPttt": "Tự chuyển khoa PTTT", "khongChuyenKhoaPttt": "Không chuyển khoa PTTT", "coTiepNhan": "<PERSON><PERSON> t<PERSON> n<PERSON>n", "khongTiepNhan": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON><PERSON> nh<PERSON>n", "maKhoa": "Mã khoa", "vuiLongNhapMaKhoa": "<PERSON>ui lòng nhập mã khoa!", "vuiLongNhapMaKhoaKhongQuaNumKyTu": "<PERSON>ui lòng nhập mã khoa không quá {{num}} ký tự!", "nhapMaKhoa": "<PERSON><PERSON><PERSON><PERSON> mã khoa", "tenKhoa": "<PERSON><PERSON><PERSON> k<PERSON>a", "nhapMaBhyt": "Nhập mã BHYT", "vuiLongNhapNhaThuTien": "<PERSON>ui lòng nhập nhà thu tiền!", "chonNhaThuTien": "<PERSON><PERSON><PERSON> nhà thu tiền", "vuiLongChonNha": "Vui lòng chọn nhà!", "chonNha": "<PERSON><PERSON><PERSON> nh<PERSON>", "nhapGiuongThucKe": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> thực kê", "nhapGiuongKeHoach": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> kế hoạch", "chonTinhChatKhoa": "<PERSON><PERSON><PERSON> t<PERSON>h chất khoa", "nhapNguongTamUngDieuTri": "<PERSON><PERSON><PERSON><PERSON> ngưỡng tạm ứng điều trị", "duocPhepMuonTaiKhoa": "<PERSON><PERSON><PERSON><PERSON> phép mượn giư<PERSON>ng tại khoa", "nhapMoTa": "<PERSON><PERSON><PERSON><PERSON> mô tả", "chonTrangThaiDuyetPhieuTra": "<PERSON><PERSON><PERSON> trạng thái duyệt phiếu trả", "duyetPhieuTra": "<PERSON><PERSON><PERSON><PERSON> phi<PERSON>u trả", "soLuongPhongMoKeHoach": "Số lượng phòng mổ kế hoạch", "vuiLongNhapSoLuongPhongMoKeHoachKhongQua3KyTu": "<PERSON><PERSON> lòng nhập số lượng phòng mổ kế hoạch không quá 3 ký tự!", "nhapSoLuongPhongMoKeHoach": "<PERSON>hập số lượng phòng mổ kế hoạch", "vuiLongNhapTenKhoa": "<PERSON>ui lòng nhập tên khoa!", "vuiLongNhapTenKhoaKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên khoa không quá 1000 ký tự!", "nhapTenKhoa": "<PERSON><PERSON><PERSON><PERSON> tên khoa", "vuiLongNhapTenVietTat": "<PERSON><PERSON> lòng nhập tên viết tắt", "vuiLongNhapMaBhyt": "Vui lòng nhập mã BHYT!", "nhapTenBh": "<PERSON><PERSON><PERSON><PERSON> tên <PERSON>", "tuyChinhGiaoDienPhanMem": "<PERSON><PERSON> chỉnh giao diện phần mềm", "maLoaGoiSo": "Mã loa gọi số", "vuiLongNhapMaLoaGoiSo": "<PERSON><PERSON> lòng nhập mã loa gọi số", "vuiLongNhapMaLoaGoiSoKhongQua20KyTu": "<PERSON>ui lòng nhập mã loa gọi số không quá 20 ký tự!", "tenLoaGoiSo": "<PERSON>ên loa gọi số", "vuiLongNhapTenLoaGoiSo": "<PERSON><PERSON> lòng nhập tên loa gọi số", "vuiLongNhapTenLoaGoiSoKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên loa gọi số không quá 1000 ký tự!", "loaiTiepDon": "<PERSON><PERSON><PERSON> tiếp đ<PERSON>", "chonLoaiTiepDon": "<PERSON><PERSON><PERSON> lo<PERSON>i tiếp đón", "chonHinhThucPhatLoa": "<PERSON><PERSON><PERSON> hình thức phát loa", "loaiLoaGoSo": "Loại loa gọi số", "baDaiHan": "BA dài hạn", "vuiLongNhapMaLoaiBenhAn": "<PERSON><PERSON> lòng nhập mã loại b<PERSON>nh <PERSON>n", "vuiLongNhapTenLoaiBenhAn": "<PERSON><PERSON> lòng nhập tên lo<PERSON>i b<PERSON>nh <PERSON>n", "tenLoaiBenhAn": "<PERSON><PERSON><PERSON> lo<PERSON> b<PERSON><PERSON>", "maLoaiBenhAn": "<PERSON><PERSON> lo<PERSON> b<PERSON><PERSON>", "maLoaiDoiTuong": "<PERSON>ã loại đối tư<PERSON>", "vuiLongNhapMaLoaiDoiTuong": "<PERSON><PERSON> lòng nhập mã loại đối tượng", "vuiLongNhapMaLoaiDoiTuongKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã loại đối tượng không quá 20 ký tự!", "tenLoaiDoiTuong": "<PERSON><PERSON><PERSON> lo<PERSON>i đối tư<PERSON>", "vuiLongNhapTenLoaiDoiTuong": "<PERSON><PERSON> lòng nhập tên loại đối tượng", "vuiLongNhapTenLoaiDoiTuongKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên loại đối tượng không quá 1000 ký tự!", "doiTuong": "<PERSON><PERSON><PERSON>", "vuiLongChonDoiTuong": "<PERSON>ui lòng chọn đối tượ<PERSON>!", "chonDoiTuong": "<PERSON><PERSON><PERSON> đ<PERSON> t<PERSON>", "loaiMienGiam": "<PERSON><PERSON><PERSON> mi<PERSON>", "chonLoaiMienGiam": "<PERSON><PERSON><PERSON> lo<PERSON>i miễn g<PERSON>", "%MienGiam": "% miễn <PERSON>", "nhap%MienGiam": "Nhập % mi<PERSON><PERSON>", "tienMienGiam": "<PERSON><PERSON><PERSON><PERSON>", "nhapTienMienGiam": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON>n mi<PERSON>", "quanNhan": "<PERSON>uân nhân", "yeuCauTamUngNgoaiTru": "<PERSON><PERSON><PERSON> c<PERSON>u tạm <PERSON>ng ngoại trú", "khamSucKhoe": "<PERSON><PERSON><PERSON><PERSON> sức khỏe", "vuiLongNhapGhiChuKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập ghi chú không quá 1000 ký tự!", "nhapGhiChu": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "timMaLoaiDoiTuong": "Tìm mã loại đối tượng", "timTenLoaiDoiTuong": "<PERSON><PERSON><PERSON> tên loại đối tượng", "chonYeuCauTamUngNgoaiTru": "<PERSON><PERSON><PERSON> yêu cầu tạm <PERSON>ng ngoại trú", "chonKhamSucKhoe": "<PERSON><PERSON><PERSON> kh<PERSON>m sức khỏe", "chonQuanNhan": "<PERSON><PERSON><PERSON> quân nhân", "chonThanhToanSau": "<PERSON><PERSON><PERSON>h toán sau", "nguongMienGiam": "Ngưỡng miễn g<PERSON>m", "timGhiChu": "<PERSON><PERSON><PERSON> ghi chú", "coQuanNhan": "<PERSON><PERSON> quân nhân", "khongQuanNhan": "Không quân nhân", "coYeuCau": "<PERSON><PERSON> y<PERSON>u c<PERSON>u", "khongYeuCau": "<PERSON><PERSON><PERSON><PERSON> yêu c<PERSON>u", "tyLeThanhToanBaoHiem": "Tỷ lệ thanh to<PERSON> b<PERSON>o hiểm", "maLoaiGiuong": "Mã loại giường", "tenLoaiGiuong": "<PERSON><PERSON><PERSON> lo<PERSON> g<PERSON>", "vuiLongNhapTenLoaiGiuong": "<PERSON>ui lòng nhập tên loại giường!", "vuiLongNhapTenKhongQua255KyTu": "<PERSON><PERSON> lòng nhập tên không quá 255 ký tự", "vuiLongNhapTyLeThanhToanBaoHiem": "<PERSON><PERSON> lòng nhập tỷ lệ thanh toán bảo hiểm", "lyDoHoan": "Lý do hoàn", "coHoan": "<PERSON><PERSON>", "khongHoan": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>n", "hoanThanhToan": "<PERSON><PERSON><PERSON>h to<PERSON>", "vuiLongNhapMaLyDoTamUng": "<PERSON><PERSON> lòng nhập mã lý do tạm <PERSON>ng", "vuiLongNhapMaLyDoTamUngKhongQua20KyTu": "<PERSON>ui lòng nhập mã lý do tạm ứng không quá 20 ký tự!", "tenLyDoTamUng": "<PERSON>ên lý do tạm <PERSON>ng", "vuiLongNhapTenLyDoTamUng": "<PERSON><PERSON> lòng nhập tên lý do tạm <PERSON>ng", "vuiLongNhapTenLyDoTamUngKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên lý do tạm ứng không quá 1000 ký tự", "maLyDoTamUng": "Mã lý do tạm ứng", "maMayGuiBHYT": "Mã máy gửi BHYT", "timMaMayGuiBHYT": "Tìm mã máy gửi BHYT", "vuiLongNhapMaMay": "<PERSON><PERSON> lòng nhập mã máy", "vuiLongNhapMaMayKhongQua200KyTu": "<PERSON><PERSON> lòng nhập mã máy không quá 200 ký tự", "vuiLongNhapTenMaMay": "<PERSON><PERSON> lòng nhập tên mã máy", "vuiLongNhapMaMayGuiBHYT": "<PERSON><PERSON> lòng nhập mã máy gửi BHYT", "tenMaMay": "<PERSON><PERSON>n mã máy", "sttChuong": "STT chương", "timTheoStt": "<PERSON><PERSON>m theo stt", "maChuong": "Mã ch<PERSON>", "timTheoTen": "<PERSON><PERSON><PERSON> theo tên", "vuiLongNhapSttChuong": "<PERSON><PERSON> lò<PERSON> nhập STT chương", "nhapSttChuong": "<PERSON><PERSON>ậ<PERSON> stt chư<PERSON>ng", "vuiLongNhapMaChuong": "<PERSON><PERSON> lòng nhập mã chương", "vuiLongNhapMaChuongKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã chương không quá 20 ký tự", "vuiLongNhapTenChuong": "<PERSON><PERSON> lòng nhập tên ch<PERSON>", "vuiLongNhapTenChuongKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên chương không quá 1000 ký tự", "maNhomPttt": "Mã nhóm PTTT", "vuiLongNhapMaNhomPttt": "<PERSON><PERSON> lòng nhập mã nhóm PTTT", "vuiLongNhapMaNhomPtttKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã nhóm PTTT không quá 20 ký tự", "vuiLongNhapTenNhomPttt": "<PERSON><PERSON> lòng nhập tên nhóm PTTT", "vuiLongNhapTenNhomPtttKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên nhóm PTTT không quá 1000 ký tự", "chuongPttt": "Chương PTTT", "chonChuongPttt": "<PERSON><PERSON>n ch<PERSON>ng PTTT", "maLoaiPttt": "Mã loại PTTT", "vuiLongNhapMaLoaiPttt": "<PERSON>ui lòng nhập mã loại PTTT!", "vuiLongNhapMaLoaiPtttKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã loại PTTT không quá 20 ký tự!", "tenLoaiPttt": "Tên lo<PERSON>i PTTT", "vuiLongNhapTenLoaiPttt": "<PERSON><PERSON> lòng nhập tên lo<PERSON>i PTTT", "vuiLongNhapTenLoaiPtttKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên lo<PERSON>i PTTT không quá 1000 ký tự", "chonNhomPttt": "Chọn nhóm PTTT", "nhomPttt": "Nhóm PTTT", "chonLoaiPttt": "Chọn loại PTTT", "tenNhomPttt": "Tên nhóm PTTT", "tatCaChuongPttt": "<PERSON><PERSON><PERSON> c<PERSON> chương PTTT", "tatCaNhomPttt": "<PERSON><PERSON><PERSON> cả nhóm PTTT", "tatCaLoaiPttt": "Tất cả loại PTTT", "chonNhomLoaiPttt": "Chọn nhóm loại PTTT", "dienBien": "<PERSON><PERSON><PERSON>", "maMauDienBien": "<PERSON><PERSON> mẫu diễn bi<PERSON>n", "vuiLongMaMauDienBien": "<PERSON><PERSON> lòng nhập mã mẫu diễn biến", "vuiLongNhapMaMauDienBienKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã mẫu diễn biến không quá 20 ký tự", "tenMauDienBien": "<PERSON><PERSON><PERSON> mẫu diễn biến", "vuiLongNhapTenMauDienBien": "<PERSON><PERSON> lòng nhập tên mẫu diễn biến", "vuiLongNhapTenMauDienBienKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên mẫu diễn biến không quá 1000 ký tự", "vuiLongNhapDienBien": "<PERSON><PERSON> lòng nh<PERSON><PERSON> di<PERSON><PERSON>n", "vuiLongChonPhong": "<PERSON>ui lòng chọn phòng", "vuiLongChonBacSi": "<PERSON><PERSON> lòng chọn b<PERSON>c sĩ", "sttHienThi": "STT hiển thị", "nhapSttHienThi": "<PERSON>hập STT hiển thị", "vuiLongNhapMaVatTu": "<PERSON><PERSON> lòng nhập mã vật tư", "vuiLongNhapMaVatTuKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã vật tư không quá 20 ký tự", "vuiLongNhapMaVatTuKhongQua50KyTu": "<PERSON><PERSON> lòng nhập mã vật tư không quá 50 ký tự", "vuiLongNhapMaVatTuKhongQua200KyTu": "<PERSON><PERSON> lòng nhập mã vật tư không quá 200 ký tự", "vuiLongNhapTenVatTu": "<PERSON><PERSON> lòng nhập tên vật tư", "vuiLongNhapTenVatTuKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên vật tư không quá 1000 ký tự", "chonNhomVatTu": "<PERSON><PERSON><PERSON> n<PERSON> vật tư", "chonNhomVatTuCap2": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> tư cấp 2", "chonNhomVatTuCap3": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> tư cấp 3", "maKyHieuTenThuongMai": "<PERSON><PERSON> ký hiệu - <PERSON><PERSON><PERSON> th<PERSON><PERSON> mại", "nhapMaKyHieu": "<PERSON><PERSON><PERSON><PERSON> mã ký hiệu", "thongSoKyThuat": "<PERSON><PERSON><PERSON><PERSON> số kỹ thuật", "nhapThongSoKyThuat": "<PERSON><PERSON><PERSON><PERSON> thông số kỹ thuật", "nhapGiaNhap": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> nhập", "nhapGia": "<PERSON><PERSON><PERSON><PERSON> giá", "vuiLongChonDichVuCap1": "<PERSON><PERSON> lòng chọn d<PERSON>ch v<PERSON> cấp 1", "chonNhomDichVuCap1": "Chọn n<PERSON>ó<PERSON> dị<PERSON> vụ cấp 1", "chonNhomDichVuCap2": "Chọn n<PERSON> d<PERSON><PERSON> v<PERSON> cấp 2", "vuiLongChonDichVuCap2": "<PERSON><PERSON> lòng chọn d<PERSON><PERSON> v<PERSON> cấp 2", "chonNhomDichVuCap3": "Chọn n<PERSON> d<PERSON><PERSON> v<PERSON> cấp 3", "chonNguonChiTraKhac": "<PERSON><PERSON><PERSON> nguồn chi trả kh<PERSON>c", "vatTuBo": "<PERSON><PERSON><PERSON> t<PERSON> bộ", "vatTuTheoKichCo": "<PERSON><PERSON><PERSON> tư theo kích cỡ", "stentPhuThuoc": "<PERSON><PERSON> phủ thuốc", "kyThuatCao": "<PERSON><PERSON> thu<PERSON>t cao", "vatTuTaiSuDung": "<PERSON><PERSON><PERSON> tư tái sử dụng", "vatTuChayMay": "<PERSON><PERSON><PERSON> t<PERSON> ch<PERSON> m<PERSON>y", "giaKhongBaoHiem": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "giaBaoHiem": "<PERSON><PERSON><PERSON> b<PERSON>", "giaPhuThu": "G<PERSON><PERSON> phụ thu", "maBaoCao": "Mã báo cáo", "timMaBaoCao": "<PERSON><PERSON>m mã báo cáo", "timTenBaoCao": "<PERSON><PERSON><PERSON> tên báo c<PERSON>o", "khoGiay": "<PERSON><PERSON><PERSON> gi<PERSON>y", "chonKhoGiay": "<PERSON><PERSON><PERSON> k<PERSON> gi<PERSON>y", "kichThuocChieuDoc": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>", "timTheoKichThuocChieuDoc": "<PERSON><PERSON><PERSON> theo kích thư<PERSON><PERSON> chi<PERSON>u dọc", "kichThuocChieuNgang": "<PERSON><PERSON><PERSON> ch<PERSON> ngang", "timTheoKichThuocChieuNgang": "<PERSON><PERSON><PERSON> theo kích thư<PERSON><PERSON> chi<PERSON>u ngang", "huongGiay": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "chonHuongGiay": "<PERSON><PERSON><PERSON>", "dinhDangXuatFile": "Định dạng xuất file", "chonDinhDang": "<PERSON><PERSON><PERSON> đ<PERSON>nh dạng", "loaiIn": "Loại in", "chonLoaiIn": "<PERSON><PERSON><PERSON> in", "chonKySo": "<PERSON><PERSON><PERSON> k<PERSON>", "thietLapChanKy": "<PERSON><PERSON><PERSON><PERSON> lập chân ký", "vuiLongNhapMaBaoCao": "<PERSON>ui lòng nhập mã báo cáo!", "vuiLongChonMaBaoCaoKhongQuaNumKyTu": "<PERSON>ui lòng nhập mã báo cáo không quá {{num}} ký tự!", "vuiLongChonMaBaoCao": "Vui lòng chọn mã báo cáo!", "xemSuaBieuMau": " <PERSON><PERSON>/Sửa biểu mẫu", "taiLenMauBaoCao": "<PERSON><PERSON><PERSON> lên mẫu báo cáo", "vuiLongTaiLenMauBaoCaoExcel": "<PERSON>ui lòng tải lên mẫu báo cáo excel (xlsx) và word (docx)!", "vuiLongNhapTenBaoCao": "<PERSON><PERSON> lòng nhập tên báo c<PERSON>o", "vuiLongNhapTenBaoCaoKhongQuaNumKyTu": "<PERSON>ui lòng nhập tên báo cáo không quá {{num}} ký tự!", "loaiBieuMau": "<PERSON><PERSON><PERSON> biểu mẫu", "vuiLongChonLoaiBieuMau": "<PERSON><PERSON> lòng chọn loại biểu mẫu", "vuiLongNhapKichThuocChieuDoc": "<PERSON><PERSON> lòng nh<PERSON><PERSON> kích thư<PERSON><PERSON> chi<PERSON>u dọc", "vuiLongNhapKichThuocChieuDocKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập kích thư<PERSON>c chiều dọc không quá {{num}} ký tự!", "vuiLongNhapKichThuocNgang": "<PERSON><PERSON> lòng nh<PERSON><PERSON> kích thư<PERSON><PERSON> chi<PERSON>u ngang", "vuiLongNhapKichThuocNgangKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập kích thư<PERSON><PERSON> chiều ngang không quá {{num}} ký tự!", "choDinhDangXuatFile": "<PERSON><PERSON><PERSON> đ<PERSON>nh dạng xuất file", "vuiLongNhapTenLoaiPhieu": "<PERSON><PERSON> lòng nhập tên lo<PERSON>i phi<PERSON>u", "vuiLongNhapTenLoaiPhieuKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên loại phiếu không quá 1000 ký tự", "vuiLongChonLoaiIn": "<PERSON><PERSON> lòng chọn lo<PERSON> in", "khuVucKy": "<PERSON><PERSON> v<PERSON> ký", "vuiLongChonKhuVucKy": "<PERSON><PERSON> lòng chọn khu vự<PERSON> ký", "soCapKy": "Số cấp ký", "vuiLongNhapSoCapKy": "<PERSON><PERSON> lòng nh<PERSON>p số cấp ký", "loaiKy": "<PERSON><PERSON><PERSON> ký", "quyenKy": "<PERSON><PERSON><PERSON><PERSON> ký", "tenChanKy": "<PERSON><PERSON><PERSON> ch<PERSON> ký", "capKyNum": "<PERSON><PERSON><PERSON> ký {{num}}", "chonQuyenKy": "<PERSON><PERSON><PERSON> quyền ký", "nhapTenChanKy": "<PERSON><PERSON><PERSON><PERSON> tên chân ký", "maXuatXu": "<PERSON><PERSON> xuất xứ", "tenXuatXu": "<PERSON><PERSON><PERSON> xu<PERSON>t xứ", "vuiLongNhapMaXuatXu": "<PERSON><PERSON> long nhập mã xuất xứ", "vuiLongNhapTenXuatXu": "<PERSON><PERSON> long nhập tên xuất xứ", "vuiLongNhapMaLieuDung": "<PERSON><PERSON> lòng nhập mã liều dùng", "maLieuDung": "<PERSON><PERSON> liều dùng", "timMaLieuDung": "T<PERSON>m mã liều dùng", "timTenLieuDung": "<PERSON><PERSON><PERSON> tên li<PERSON>u dùng", "soLuongSang": "SL sáng", "timSoLuongSang": "<PERSON><PERSON><PERSON> s<PERSON> lư<PERSON> sáng", "soLuongChieu": "SL chiều", "timSoLuongChieu": "<PERSON><PERSON><PERSON> s<PERSON> l<PERSON> chi<PERSON>", "soLuongToi": "SL tối", "timSoLuongToi": "<PERSON><PERSON><PERSON> s<PERSON> lư<PERSON> tối", "soLuongDem": "SL đêm", "timSoLuongDem": "<PERSON><PERSON><PERSON> số lư<PERSON> đêm", "thoiDiemDung": "<PERSON>h<PERSON><PERSON> điểm dùng", "timThoiDiemDung": "<PERSON><PERSON>m thời điểm dùng", "tatCa": "<PERSON><PERSON><PERSON> c<PERSON>", "chonInNhanh": "Chọn in nhanh", "danhSachThuoc": "<PERSON><PERSON> s<PERSON> thu<PERSON>c", "soLuongDungSang": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>g", "chiDuocPhepNhapSoNguyenHoacPhanSo": "Chỉ đượ<PERSON> phép nhập số nguyên hoặc phân số (S<PERSON> nguyên / <PERSON><PERSON> nguyên)", "thieuThongTinDuongDung": "<PERSON><PERSON><PERSON><PERSON> thông tin đường dùng", "tenLieuDung": "<PERSON><PERSON><PERSON> li<PERSON> dùng", "vuiLongNhapTenLieuDung": "<PERSON><PERSON> lòng nhập tên liều dùng", "soLuongDungChieu": "<PERSON>l dùng chi<PERSON>u", "vuiLongNhapSoLuongDungChieu": "<PERSON><PERSON> lòng nhập số lượng dùng chiều", "soLuongDungToi": "<PERSON>l dùng tối", "vuiLongNhapSoLuongDungToi": "<PERSON><PERSON> lòng nhập số lượng dùng tối", "soLuongDungDem": "<PERSON>l dùng đêm", "vuiLongNhapSoLuongDungDem": "<PERSON><PERSON> lòng nhập số lượng dùng đêm", "nhapThoiDiemDung": "<PERSON><PERSON><PERSON><PERSON> thời điểm dùng", "vuiLongNhapThoiDiemDung": "<PERSON><PERSON> lòng nhập thời điểm dùng", "vuiLongNhapSoLuongDungSang": "<PERSON><PERSON> lòng nhập số lượng dùng sáng", "vuiLongChonDuongDung": "<PERSON><PERSON> lòng chọn đường dùng", "vuiLongNhapGhiChu": "<PERSON><PERSON> lòng nh<PERSON>p ghi chú", "bacSiChiDinh": "<PERSON><PERSON><PERSON> sĩ chỉ định", "vuiLongChonBacSiChiDinh": "<PERSON><PERSON> lòng chọn bác sĩ chỉ định", "maLoaiPhieu": "Mã loại phiếu", "tenLoaiPhieu": "<PERSON><PERSON><PERSON> lo<PERSON>", "vuiLongNhapMaLoaiPhieu": "<PERSON><PERSON> lòng nhập mã loại phi<PERSON>u", "vuiLongNhapMaLoaiPhieuKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã loại phiếu không quá 20 ký tự", "maLoiDan": "Mã lời dặn", "vuiLongNhapMaLoiDan": "<PERSON><PERSON> lòng nhập mã lời dặn", "tenLoiDan": "<PERSON><PERSON><PERSON> lời dặn", "vuiLongNhapTenLoiDan": "<PERSON><PERSON> lòng nhập tên lời dặn", "vuiLongNhapTenLoiDanKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên lời dặn không quá 1000 ký tự", "vuiLongNhapTenLoiDanKhongQua4000KyTu": "<PERSON><PERSON> lòng nhập tên lời dặn không quá 4000 ký tự", "danhChoThuoc": "<PERSON><PERSON><PERSON> cho thuốc", "vuiLongNhapMaNguonNhap": "<PERSON><PERSON> lòng nhập mã nguồn nhập", "vuiLongNhapMaNguonNhapKhongQua50KyTu": "<PERSON><PERSON> lòng nhập mã nguồn nhập không quá 50 ký tự", "tenNguonNhap": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>", "vuiLongNhapTenNguonNhap": "<PERSON><PERSON> lòng nhập tên nguồn nhập", "vuiLongNhapTenNguonNhapKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên nguồn nhập không quá 1000 ký tự", "thau": "<PERSON><PERSON><PERSON><PERSON>", "khongThau": "<PERSON><PERSON><PERSON><PERSON> thầu", "maNguonNhap": "<PERSON><PERSON> ngu<PERSON>n n<PERSON>p", "coThau": "<PERSON><PERSON> th<PERSON>", "tenChiSoSong": "<PERSON>ên chỉ số sống", "vuiLongNhapTenChiSoSong": "<PERSON><PERSON> lòng nhập tên chỉ số sống!", "vuiLongNhapTenChiSoSongKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên chỉ số sống không quá 1000 ký tự!", "vuiLongNhapDonVi": "<PERSON><PERSON> lòng nh<PERSON>p đơn vị", "giaTriToiThieu": "<PERSON><PERSON><PERSON> trị tối thiểu", "vuiLongNhapGiaTri": "<PERSON><PERSON> lòng nhập giá trị", "giaTriToiDa": "<PERSON><PERSON><PERSON> trị tối đa", "giaTriVuotNguongToiThieu": "<PERSON><PERSON><PERSON> trị vượt ngưỡng tối thiểu", "giaTriVuotNguongToiDa": "<PERSON><PERSON><PERSON> trị vượt ngưỡng tối đa", "vuiLongNhapGiaTriVuotNguongToiThieuNhoHonGiaTriToiThieu": "<PERSON><PERSON> lòng nhập giá trị vượt ngưỡng tối thiểu nhỏ hơn giá trị tối thiểu", "vuiLongNhapGiaTriVuotNguongToiDaLonHonGiaTriToiDa": "<PERSON><PERSON> lòng nhập giá trị vượt ngưỡng tối đa lớn hơn giá trị tối đa", "khoaApDung": "<PERSON><PERSON><PERSON> d<PERSON>", "maChiSoSong": "Mã chỉ số sống", "nhapMaChiSoSong": "<PERSON><PERSON><PERSON><PERSON> mã chỉ số sống", "maNhomChiPhi": "Mã nhóm chi phí", "vuiLongNhapMaNhomChiPhi": "<PERSON><PERSON> lòng nhập mã nhóm chi phí", "vuiLongNhapMaNhomChiPhiKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã nhóm chi phí không quá 20 ký tự", "tenNhomChiPhi": "<PERSON>ên nhóm chi phí", "vuiLongNhapTenNhomChiPhi": "<PERSON><PERSON> lòng nhập nhóm chi phí", "vuiLongNhapTenNhomChiPhiKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập nhóm chi phí không quá 1000 ký tự", "nhomDonViTinh": "Nhóm đơn vị t<PERSON>h", "nhapNhomDonViTinh": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>m đơn vị t<PERSON>h", "maNhomDonViTinh": "Mã nhóm đơn vị tính", "vuiLongNhapMaNhomDonViTinh": "<PERSON><PERSON> lòng nhập mã nhóm đơn vị tính", "maPhaiCoItNhatMotKyTuLaChu": "Mã phải có ít nhất một ký tự là chữ", "nhapMaNhomDonViTinh": "<PERSON><PERSON><PERSON><PERSON> mã nhóm đơn vị tính", "tenNhomDonViTinh": "<PERSON><PERSON><PERSON> nh<PERSON>m đơn vị t<PERSON>h", "chonTenNhomDonViTinh": "<PERSON><PERSON><PERSON> tên nhóm đơn vị tính", "vuiLongNhapTenNhomDonViTinh": "<PERSON><PERSON> lòng nhập tên nhóm đơn vị tính", "vuiLongNhapMaDonViTinh": "<PERSON><PERSON> lòng nhập mã đơn vị tính", "nhapTenNhomDonViTinh": "<PERSON><PERSON><PERSON><PERSON> tên nhóm đơn vị tính", "maDonViTinh": "<PERSON>ã đơn vị t<PERSON>h", "nhapMaDonViTinh": "<PERSON><PERSON><PERSON><PERSON> mã đơn vị tính", "tenDonViTinh": "<PERSON><PERSON><PERSON> đ<PERSON>n vị t<PERSON>h", "nhapTenDonViTinh": "<PERSON><PERSON><PERSON><PERSON> tên đơn vị tính", "vuiLongNhapTenDonViTinh": "<PERSON><PERSON> lòng nhập tên đơn vị tính", "chonNhomDonViTinh": "<PERSON><PERSON>n nhóm đơn vị tính", "maDotBien": "<PERSON><PERSON> đột biến", "vuiLongNhapMaDotBien": "<PERSON><PERSON> lòng nhập mã đột biến", "danhPhap": "<PERSON><PERSON> ph<PERSON>p", "nhapDanhPhap": "<PERSON><PERSON><PERSON><PERSON> danh ph<PERSON>p", "tenDotBien": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON>n", "vuiLongNhapTenDotBien": "<PERSON><PERSON> lòng nhập tên đột biến", "yNghiaDotBien": "Ý ngh<PERSON>a đột biến", "nhapYNghiaDotBien": "<PERSON><PERSON><PERSON><PERSON> <PERSON> nghĩa đột biến", "dotBien": "<PERSON><PERSON><PERSON>", "danhMucMauKQXNCoDotBien": "<PERSON><PERSON> mục mẫu KQ XN có đột biến", "dacTinh": "đặc tính", "maDanhGia": "Mã đánh giá", "vuiLongNhapMaDanhGia": "<PERSON><PERSON> lòng nhập mã đánh giá", "vuiLongNhapMaDanhGiaKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã đánh giá không quá 20 ký tự", "nhapMaDanhGia": "<PERSON><PERSON><PERSON><PERSON> mã đánh giá", "tenDanhGia": "<PERSON><PERSON><PERSON> đ<PERSON> giá", "vuiLongNhapTenDanhGia": "<PERSON><PERSON> lòng nhập tên đ<PERSON>h giá", "nhapTenDanhGia": "<PERSON><PERSON><PERSON><PERSON> tên đ<PERSON>h giá", "giaTriNhoNhat": "<PERSON><PERSON><PERSON> trị nhỏ nhất", "nhapGiaTri": "<PERSON><PERSON><PERSON><PERSON> giá trị", "giaTriLonNhat": "<PERSON><PERSON><PERSON> trị lớn nhất", "giaTriLonNHatPhaiLonHonGiaTriNhoNhat": "Giá trị lớn nhất phải > giá trị nhỏ nhất", "danhGia": "<PERSON><PERSON><PERSON> giá", "vuiLongNhapTenDanhGiaKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên đánh giá không quá 1000 ký tự", "danhMucDichVuChung": "<PERSON><PERSON> m<PERSON><PERSON> d<PERSON><PERSON> v<PERSON> chung", "maGoi": "Mã gói", "timMaGoi": "T<PERSON>m mã gói", "tenGoi": "<PERSON><PERSON><PERSON>", "donGiaKhongBaoHiem": "Đơn gi<PERSON> không BH", "maDichVuHangHoa": "<PERSON><PERSON> d<PERSON> v<PERSON>, h<PERSON><PERSON> h<PERSON>a", "timTheoMaDichVuHangHoa": "<PERSON><PERSON><PERSON> theo mã dịch v<PERSON>, hàng hóa", "tenDichVuHangHoa": "<PERSON><PERSON><PERSON>, hà<PERSON> h<PERSON>a", "timTheoTenDichVuHangHoa": "<PERSON><PERSON><PERSON> theo tên d<PERSON> v<PERSON>, hàng hóa", "timNhomDvCap1": "Tìm nhóm Dv cấp 1", "donGiaBH": "Đơn giá BH", "donGiaKhongBH": "Đơn gi<PERSON> không BH", "vuiLongNhapMaGoi": "<PERSON><PERSON> lòng nhập mã gói", "vuiLongNhapMaGoiKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã gói không quá 20 ký tự", "vuiLongNhapMaGoiCap1": "<PERSON><PERSON> lòng nhập mã gói cấp 1", "vuiLongNhapTenGoi": "<PERSON><PERSON> lòng nhập tên gói", "vuiLongNhapTenGoiKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên gói không quá 1000 ký tự", "vuiLongNhapDonGiaKhongBH": "<PERSON><PERSON> lòng nhập đơn gi<PERSON> không BH", "vuiLongChonPhongThucHien": "<PERSON><PERSON> lòng chọn phòng thực hiện", "chonPhongThucHien": "<PERSON><PERSON><PERSON> phòng thực hiện", "maMayTinh": "<PERSON><PERSON> máy t<PERSON>h", "timMaMayTinh": "T<PERSON>m mã máy t<PERSON>h", "tenMayIn": "<PERSON><PERSON><PERSON> in", "timTenMayIn": "<PERSON><PERSON><PERSON> tên m<PERSON> in", "diaChiIp": "Địa chỉ IP", "timTheoDiaChiIp": "<PERSON><PERSON><PERSON> theo đ<PERSON>a chỉ IP", "iSofHToolsVersion": "iSofHTools Version", "nhapISofHToolsVersion": "iSofHTools Version", "capNhatThanhCongDuLieuMayIn": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu máy in!", "vuiLongChonKhoGiay": "<PERSON>ui lòng chọn khổ giấy!", "vuiLongChonHuongGiay": "<PERSON><PERSON> lòng chọn h<PERSON> g<PERSON>", "capNhatThanhCongDuLieuThietLapChanKy": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu thiết lập chân ký!", "themMoiThanhCongDuLieuThietLapChanKy": "Thêm mới thành công dữ liệu thiết lập chân ký!", "themMoiThanhCongDuLieuBaoCao": "Thêm mới thành công dữ liệu báo cáo!", "capNhatThanhCongDuLieuBaoCao": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu báo cáo!", "capNhatThanhCongDuLieuXuatXu": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu xuất xứ!", "themMoiThanhCongDuLieuXuatXu": "Thêm mới thành công dữ liệu xuất xứ!", "xoaBanGhiThanhCong": "<PERSON><PERSON><PERSON> bản ghi thành công!", "maNhomChiSo": "Mã nhóm chỉ số", "nhapMaNhomChiSo": "<PERSON><PERSON><PERSON><PERSON> mã nhóm chỉ số", "vuiLongNhapMaNhomChiSo": "<PERSON><PERSON> lòng nhập mã nhóm chỉ số", "tenNhomChiSo": "<PERSON><PERSON><PERSON> n<PERSON> chỉ số", "nhapTenNhomChiSo": "<PERSON><PERSON><PERSON><PERSON> tên nhóm chỉ số", "vuiLongNhapTenNhomChiSo": "<PERSON><PERSON> lòng nhập tên nhóm chỉ số", "vaiTroHeThong": "vai trò hệ thống", "maVaiTro": "Mã vai trò", "vuiLongNhapMaVaiTro": "<PERSON><PERSON> lòng nhập mã vai trò", "vuiLongNhapMaVaiTroKhongQua50KyTu": "Vui lòng nhập mã vai trò không quá 50 ký tự!", "vuiLongChonQuyen": "<PERSON>ui lòng chọn quyền!", "quyenDuocGan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>án", "chonNhomTinhNang": "<PERSON><PERSON><PERSON> nh<PERSON> t<PERSON>h n<PERSON>ng", "timKiemQuyen": "<PERSON><PERSON><PERSON> k<PERSON> quyền", "capNhatThanhCongDuLieuVaiTroHeThong": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu vai trò hệ thống!", "themMoiThanhCongDuLieuVaiTroHeThong": "Thêm mới thành công dữ liệu vai trò hệ thống!", "vuiLongNhapKetQuaThamChieu": "<PERSON><PERSON> lòng nh<PERSON>p kết quả tham chiếu", "themMoiThanhCongDuLieuLoaiBenhAn": "Thêm mới thành công dữ liệu loại bệnh án!", "capNhatThanhCongDuLieuKhoa": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu khoa!", "themMoiThanhCongDuLieuKhoa": "Thêm mới thành công dữ liệu khoa!", "capNhatKhongThanhCongDuLieuKhoa": "<PERSON><PERSON><PERSON> nhật không thành công dữ liệu khoa!", "themMoiKhongThanhCongDuLieuKhoa": "Thêm mới không thành công dữ liệu khoa!", "dongBoThanhCong": "<PERSON><PERSON>ng bộ thành công", "themMoiThanhCongDuLieuThietLapChung": "Thêm mới thành công dữ liệu thiết lập chung!", "uploadVideoThanhCong": "Upload video thành công", "khongTimThayThongTinBenhNhan": "<PERSON><PERSON><PERSON>ng tìm thấy thông tin bệnh nhân", "capNhatThanhCongDuLieuLoaGoiSo": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu loa gọi số!", "themMoiThanhCongDuLieuLoaGoiSo": "Thêm mới thành công dữ liệu loa gọi số!", "capNhatThanhCongDuLieuLoaiDoiTuong": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu loại đối tượng!", "themMoiThanhCongDuLieuLoaiDoiTuong": "Thêm mới thành công dữ liệu loại đối tượng!", "capNhatDuLieuThanhCong": "<PERSON><PERSON><PERSON> nhật dữ liệu thành công!", "themMoiDuLieuThanhCong": "Thêm mới dữ liệu thành công!", "capNhatThanhCongDuLieuMaMay": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu mã máy!", "themMoiThanhCongDuLieuMaMay": "Thêm mới thành công dữ liệu mã máy!", "capNhatThanhCongDuLieuChuongPttt": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu chương PTTT!", "themMoiThanhCongDuLieuChuongPttt": "Thêm mới thành công dữ liệu chương PTTT!", "capNhatThanhCongDuLieuNhomPttt": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu nhóm PTTT!", "themMoiThanhCongDuLieuNhomPttt": "Thêm mới thành công dữ liệu nhóm PTTT!", "capNhatThanhCongDuLieuLoaiBenh": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu loại bệnh!", "themMoiThanhCongDuLieuLoaiBenh": "Thêm mới thành công dữ liệu loại bệnh!", "capNhatThanhCongDuLieuTenPttt": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu tên PTTT!", "themMoiThanhCongDuLieuTenPttt": "Thêm mới thành công dữ liệu tên PTTT!", "doiTuongKCB": "<PERSON><PERSON><PERSON> KCB", "nhaChiDinh": "Nhà chỉ định", "chonNhaChiDinh": "<PERSON><PERSON><PERSON> nhà chỉ định", "phongLayMau": "<PERSON>òng lấy mẫu", "chonPhongLayMau": "<PERSON>ọn phòng lấy mẫu", "diaDiem": "<PERSON><PERSON><PERSON>", "vuiLongNhapKhoaChiDinh": "<PERSON><PERSON> lòng nhập khoa chỉ định", "chonKhoaChiDinh": "<PERSON><PERSON><PERSON> khoa chỉ định", "nhapNhomDVCap1": "Nhập nhóm DV cấp 1", "vuiLongNhapNhomChiDinhCap1": "<PERSON><PERSON> lòng nhập nhóm chỉ định cấp 1", "vuiLongChonPhongLayMau": "<PERSON>ui lòng chọn phòng lấy mẫu", "nhapDiaDiem": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> điể<PERSON>", "slHangDoi": "SL hàng đợi", "nhapSlHangDoi": "Nhập SL hàng đợi", "maChucVu": "Mã chức vụ", "vuiLongNhapMaChucVu": "<PERSON><PERSON> lòng nhập mã chức vụ", "nhapMaChucVu": "<PERSON><PERSON>ậ<PERSON> mã chức vụ", "tenChucVu": "<PERSON><PERSON><PERSON> v<PERSON>", "nhapTenChucVu": "<PERSON><PERSON><PERSON><PERSON> tên chứ<PERSON> v<PERSON>", "vuiLongNhapTenChucVu": "<PERSON><PERSON> lòng nhập tên chức vụ", "maQuyenKy": "<PERSON><PERSON> quyền ký", "timMaQuyenKy": "<PERSON><PERSON><PERSON> mã quyền ký", "tenQuyenKy": "<PERSON><PERSON><PERSON> quyền ký", "timTenQuyenKy": "<PERSON><PERSON><PERSON> tên quyền ký", "vuiLongNhapMaQuyenKy": "<PERSON><PERSON> lòng nhập mã quyền ký", "vuiLongNhapTenQuyenKy": "<PERSON><PERSON> lòng nhập tên quyền ký", "maBenhPham": "<PERSON><PERSON> b<PERSON>nh phẩm", "tenBenhPham": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> ph<PERSON>m", "vuiLongNhapMaBenhPham": "<PERSON><PERSON> lòng nhập mã bệnh phẩm", "vuiLongNhapTenBenhPham": "<PERSON><PERSON> lòng nhập tên bệnh phẩm", "benhPham": "b<PERSON><PERSON> phẩm", "capNhatThanhCongDuLieuBenhPham": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu bệnh phẩm!", "themMoiThanhCongDuLieuBenhPham": "Thêm mới thành công dữ liệu bệnh phẩm!", "yeuCauDotDung": "<PERSON><PERSON><PERSON> c<PERSON>u đợt dùng", "PLThuoc": "ph<PERSON> lo<PERSON>i thuốc", "vuiLongNhapMaPhanLoaiThuoc": "<PERSON><PERSON> lòng nhập mã phân loại thuốc", "vuiLongNhapMaPhanLoaiThuocKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã phân loại thuốc không quá 20 ký tự", "vuiLongNhapTenPhanLoaiThuoc": "<PERSON><PERSON> lòng nhập tên phân loại thuốc", "vuiLongNhapTenPhanLoaiThuocKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên phân loại thuốc không quá 1000 ký tự", "chonLoaiDonThuoc": "<PERSON><PERSON><PERSON> lo<PERSON>i đơn thuốc", "vuiLongNhapLabelTitleKhongQuaCountKyTu": "<PERSON>ui lòng nhập {{ label }} {{ title }} không quá {{ count }} ký tự", "VuiLongNhapTenTitle": "<PERSON><PERSON> lòng nhập tên {{ title }}", "VuiLongNhapMaTitle": "<PERSON><PERSON> lòng nhập mã {{ title }}", "themMoiThanhCongDuLieuTitle": "<PERSON><PERSON><PERSON><PERSON> mới thành công dữ liệu {{ title }}", "themMoiKhongThanhCongDuLieuTitle": "Thê<PERSON> mới không thành công dữ liệu {{ title }}!", "capNhatThanhCongDuLieuTitle": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu {{ title }}!", "capNhatKhongThanhCongDuLieuTitle": "<PERSON><PERSON><PERSON> nhật không thành công dữ liệu {{ title }}!", "maBoChiDinh": "<PERSON><PERSON> bộ chỉ định", "timMaBoChiDinh": "<PERSON><PERSON><PERSON> mã bộ chỉ định", "tenBoChiDinh": "<PERSON><PERSON><PERSON> bộ chỉ định", "timTenBoChiDinh": "<PERSON><PERSON><PERSON> tên bộ chỉ định", "chonLoaiDichVu": "<PERSON><PERSON><PERSON> lo<PERSON> d<PERSON>ch vụ", "taiKhoanBoChiDinh": "<PERSON><PERSON><PERSON> k<PERSON>n chỉ định bộ", "chonTaiKhoanChiDinhBo": "<PERSON><PERSON><PERSON> tài khoản chỉ định bộ", "boThuocKeNgoai": "<PERSON><PERSON> thuốc kê ngoài", "thongTinBoChiDinh": "<PERSON><PERSON><PERSON><PERSON> tin bộ chỉ định", "vuiLongNhapTenBoChiDinh": "<PERSON><PERSON> lòng nhập tên bộ chỉ định", "vuiLongNhapTenBoChiDinhKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên bộ chỉ định không quá 1000 ký tự!", "vuiLongChonKeDichVu": "<PERSON><PERSON> lòng chọn kê dịch vụ", "chonHopKeDv": "<PERSON><PERSON><PERSON> h<PERSON>p kê DV", "coHanChe": "<PERSON><PERSON> h<PERSON>n ch<PERSON>", "khongHanChe": "<PERSON><PERSON><PERSON><PERSON> hạn chế", "goiPhauThuatThuThuat": "<PERSON><PERSON><PERSON> phẫu thuật thủ thuật", "themMoiThanhCongDuLieuGoiDichVu": "Thêm mới thành công dữ liệu gói dịch vụ!", "capNhatThanhCongDuLieuGoiDichVu": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu gói dịch vụ!", "themMoiThanhCongDuLieuChiTietGoiDichVu": "Thêm mới thành công dữ liệu chi tiết gói dịch vụ!", "capNhatThanhCongDuLieuChiTietGoiDichVu": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu chi tiết gói dịch vụ!", "lichSuThayDoiThongTin": "<PERSON><PERSON><PERSON> sử thay đổi thông tin", "doiTacCon": "<PERSON><PERSON><PERSON> t<PERSON> con", "capNhatThanhCongDuLieuDoiTac": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu đối tác!", "themMoiThanhCongDuLieuDoiTac": "Thêm mới thành công dữ liệu đối tác!", "capNhatThanhCongDuLieuDoiTacCon": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu đối tác con!", "themMoiThanhCongDuLieuDoiTacCon": "Thêm mới thành công dữ liệu đối tác con!", "maChuyenKhoa": "<PERSON><PERSON> chuyên khoa", "vuiLongNhapMaChuyenKhoa": "<PERSON><PERSON> lòng nhập mã chuyên khoa", "vuiLongNhapMaChuyenKhoaKhongQuaNumKyTu": "<PERSON>ui lòng nhập mã chuyên khoa không quá {{num}} ký tự!", "tenChuyenKhoa": "<PERSON><PERSON><PERSON> chuyên khoa", "vuiLongNhapTenChuyenKhoa": "<PERSON><PERSON> lòng nhập tên chuyên khoa", "vuiLongNhapTenChuyenKhoaKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập tên chuyên khoa không quá {{num}} ký tự!", "capNhatThanhCongDuLieuChuyenKhoa": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu chuyên khoa!", "themMoiThanhCongDuLieuChuyenKhoa": "Thêm mới thành công dữ liệu chuyên khoa!", "vuiLongNhapMaDuongDung": "<PERSON><PERSON> lòng nhập mã đường dùng", "vuiLongNhapMaDuongDungKhongQua20KyTu": "<PERSON>ui lòng nhập mã đường dùng không quá 20 ký tự!", "tenDuongDung": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> dùng", "vuiLongNhapTenDuongDung": "<PERSON><PERSON> lòng nhập tên đường dùng", "vuiLongNhapTenDuongDungKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên đường dùng không quá 1000 ký tự!", "nhapThuTuHienThi": "<PERSON><PERSON><PERSON><PERSON> thứ tự hiển thị", "vuiLongNhapDonGiaBh": "<PERSON><PERSON> lòng nh<PERSON>p đơn giá BH", "vuiLongNhapKhongBh": "<PERSON><PERSON> lòng nh<PERSON>p không BH", "vuiLongNhapPhuThu": "<PERSON><PERSON> lòng n<PERSON><PERSON> thu", "vuiLongChonDvt": "<PERSON><PERSON> lòng chọn đơn vị t<PERSON>h", "vuiLongChonNhomDvCapNum": "<PERSON>ui lòng chọn nhóm dịch vụ cấp {{num}}", "nhomDvCapNum": "Nhóm DV Cấp {{num}}", "timMaNhomThuocCap2": "<PERSON><PERSON><PERSON> mã nhóm thu<PERSON> cấp 2", "timMaNhomThuocCap3": "<PERSON><PERSON><PERSON> mã nhóm thu<PERSON> cấp 3", "timTenMau": "<PERSON><PERSON><PERSON> tên mẫu", "timKetLuan": "<PERSON><PERSON><PERSON>", "timKetQua": "<PERSON><PERSON><PERSON> kết quả", "viThe": "<PERSON><PERSON> thể", "timViThe": "<PERSON><PERSON><PERSON> vị thể", "daiThe": "<PERSON><PERSON><PERSON> thể", "timDaiThe": "<PERSON><PERSON><PERSON> đại thể", "tenXetNghiem": "<PERSON><PERSON><PERSON>", "timTenXetNghiem": "<PERSON><PERSON><PERSON> tên x<PERSON>t ng<PERSON>m", "vuiLongNhapMaKhongQuaNumKyTu": "<PERSON>ui lòng nhập mã không quá {{num}} ký tự!", "vuiLongNhapTenMau": "<PERSON><PERSON> lòng nhập tên mẫu", "vuiLongNhapTenMauKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập tên mẫu không quá {{num}} ký tự!", "vuiLongNhapKetLuan": "<PERSON><PERSON> lòng nh<PERSON><PERSON> kết luận", "vuiLongNhapKetQua": "<PERSON><PERSON> lòng nh<PERSON>p kết quả", "vuiLongNhapViThe": "<PERSON><PERSON> lòng nhập vị thể", "vuiLongNhapDaiThe": "<PERSON><PERSON> lòng nhập đại thể", "vuiLongChonTenXetNghiem": "<PERSON><PERSON> lòng chọn tên xét nghi<PERSON>m", "chiTietMauKetQua": "<PERSON> tiết mẫu kết quả", "capNhatThanhCongDuLieuMauKqXn": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu mẫu kết quả xét nghiệm!", "themMoiThanhCongDuLieuMauKqXn": "Thêm mới thành công dữ liệu mẫu kết quả xét nghiệm!", "capNhatThanhCongDuLieuPhieuIn": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu phiếu in!", "themMoiThanhCongDuLieuPhieuIn": "Thêm mới thành công dữ liệu phiếu in!", "capNhatThanhCongDuLieuViTriPhieuIn": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu vị trí phiếu in!", "themMoiThanhCongDuLieuViTriPhieuIn": "Thêm mới thành công dữ liệu vị trí phiếu in!", "capNhatThanhCongDuLieuManHinhPhieuIn": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu màn hình phiếu in!", "themMoiThanhCongDuLieuManHinhPhieuIn": "Thêm mới thành công dữ liệu màn hình phiếu in!", "vuiLongChonMaViTri": "<PERSON><PERSON> lòng nhập mã vị trí", "vuiLongNhapNoiDungViTri": "<PERSON><PERSON> lòng nhập nội dung vị trí", "vuiLongChonManHinh": "<PERSON><PERSON> lòng chọn màn hình", "vuiLongNhapMaManHinhPhieuIn": "<PERSON><PERSON> lòng nhập mã màn hình phi<PERSON>u in", "vuiLongNhapMaManHinhPhieuInKhongQuaNumKyTu": "<PERSON>ui lòng nhập mã màn hình phiếu in không quá {{num}} ký tự!", "vuiLongNhapTenManHinhPhieuIn": "<PERSON><PERSON> lòng nhập tên màn hình phi<PERSON>u in", "vuiLongNhapTenManHinhPhieuInKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập tên màn hình phiếu in không quá {{num}} ký tự!", "mauKetQuaPttt": "Mẫu kết quả PT - TT", "maMauKetQuaPttt": "Mã mẫu kết quả PT - TT", "vuiLongNhapMaMauKetQuaPttt": "<PERSON><PERSON> lòng nhập mã mẫu kết quả PT - TT", "vuiLongNhapMaMauKetQuaPtttKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã mẫu kết quả PT - TT không quá 20 ký tự", "tenMauKetQuaPttt": "Tên mẫu kết quả PT - TT", "vuiLongNhapTenMauKetQuaPttt": "<PERSON><PERSON> lòng nhập tên mẫu kết quả PT - TT", "vuiLongNhapTenMauKetQuaPtttKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên mẫu kết quả PT - TT không quá 1000 ký tự", "vuiLongChonPhuongPhap": "<PERSON><PERSON> lòng chọn ph<PERSON><PERSON>ng pháp", "chanDoanSauPttt": "<PERSON><PERSON>n đo<PERSON> sau PTTT", "vuiLongNhapChanDoan": "<PERSON><PERSON> lòng nh<PERSON>p chẩn đo<PERSON>", "phuongPhapPttt": "Phương pháp PTTT", "vuiLongNhapPhuongPhap": "<PERSON><PERSON> lòng nhập ph<PERSON><PERSON><PERSON> pháp", "cachThucPttt": "<PERSON><PERSON><PERSON> th<PERSON>c <PERSON>", "vuiLongNhapCachThuc": "<PERSON><PERSON> lòng nh<PERSON><PERSON> c<PERSON>ch thức", "danhMucMauKetQuaPhauThuatThuThuat": "<PERSON><PERSON> mục mẫu kết quả phẫu thuật - thủ thuật", "tinhtp": "Tỉnh/TP", "quanhuyen": "Quận/huyện", "xaphuong": "Xã/Phường", "timQuocGia": "<PERSON><PERSON><PERSON> quốc gia", "timTinhtp": "Tìm tỉnh/TP", "timQuanhuyen": "<PERSON><PERSON><PERSON> quận/Huyện", "timXaphuong": "Tìm xã/Phường", "maXaphuong": "Mã xã/phường", "timMaXaphuong": "Tìm mã xã/Phường", "tenXaphuong": "Tên xã/phường", "timTenXaphuong": "<PERSON><PERSON><PERSON> tên xã/Phường", "timTenVietTat": "<PERSON><PERSON><PERSON> tên vi<PERSON><PERSON> tắt", "maQuanhuyen": "<PERSON>ã quận/huyện", "vuiLongNhapMaQuanhuyen": "<PERSON><PERSON> lòng nhập mã quận/huyện!", "vuiLongNhapMaQuanhuyenKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã quận/huyện không quá 20 ký tự!", "tenQuanhuyen": "Tên quận/huyện", "vuiLongNhapTenQuanhuyen": "<PERSON><PERSON> lòng nhập tên quận/huyện!", "vuiLongNhapTenQuanhuyenKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên quận/huyện không quá 1000 ký tự!", "vuiLongNhapQuanhuyen": "<PERSON><PERSON> lòng nhập quận/huyện", "vuiLongNhapTenVietTatKhongQua2KyTu": "<PERSON>ui lòng nhập tên viết tắt không quá 2 ký tự!", "vuiLongNhapChonTinhtp": "<PERSON><PERSON> lòng nhập chọn tỉnh/tp!", "chonTinhtp": "Chọn tỉnh/tp", "vuiLongNhapChonQuocGia": "<PERSON>ui lòng nhập chọn quốc gia!", "chonQuocGia": "<PERSON><PERSON><PERSON> quốc gia", "tatCaQuocGia": "<PERSON><PERSON><PERSON> gia", "tatCaTinh": "<PERSON><PERSON><PERSON> cả Tỉnh", "chonTinh": "<PERSON><PERSON><PERSON> tỉnh", "maTinhtp": "Mã tỉnh/tp", "vuiLongNhapMaTinhtp": "<PERSON>ui lòng nhập mã tỉnh/tp!", "vuiLongNhapMaTinhtpKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã tỉnh/tp không quá 20 ký tự!", "tenTinhtp": "Tên tỉnh/tp", "vuiLongNhapTenTinhtp": "<PERSON><PERSON> lòng nhập tên tỉnh/tp!", "vuiLongNhapTenTinhtpKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên tỉnh/tp không quá 1000 ký tự!", "vuiLongNhapTinhtp": "<PERSON><PERSON> lòng nhập tỉnh/tp", "maTinh": "Mã tỉnh", "tenTinh": "<PERSON><PERSON>n tỉnh", "vuiLongNhapMaXaphuong": "<PERSON>ui lòng nhập mã xã/phường!", "vuiLongNhapMaXaphuongKhongQua20KyTu": "<PERSON>ui lòng nhập mã xã/phường không quá 20 ký tự!", "vuiLongNhapTenXaphuong": "<PERSON>ui lòng nhập tên xã/phường!", "vuiLongNhapTenXaphuongKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên xã/phường không quá 1000 ký tự!", "vuiLongNhapXaphuong": "<PERSON><PERSON> lòng nhập xã/phường", "vuiLongNhapChonQuanhuyen": "<PERSON><PERSON> lòng nhập chọn quận/huyện!", "chonQuanhuyen": "<PERSON><PERSON><PERSON> quận/huyện", "tatCaHuyen": "<PERSON><PERSON><PERSON>", "maPhong": "Mã phòng", "timMaPhong": "T<PERSON>m mã phòng", "timTenPhong": "<PERSON><PERSON>m tên phòng", "loaiPhong": "Loại phòng", "chonLoaiPhong": "<PERSON><PERSON><PERSON> lo<PERSON>i phòng", "timTenKhoa": "<PERSON><PERSON><PERSON> tên khoa", "timTenChuyenKhoa": "<PERSON><PERSON><PERSON> tên chuyên khoa", "timDiaDiem": "<PERSON><PERSON><PERSON> địa điểm", "timTenNha": "<PERSON><PERSON><PERSON> tên nh<PERSON>", "thoiGianThucHienTrungBinh": "<PERSON><PERSON><PERSON><PERSON> gian thực hiện trung bình", "timThoiGianThucHienTrungBinh": "<PERSON><PERSON><PERSON> thời gian thực hiện trung bình", "chonNoiTru": "<PERSON><PERSON><PERSON> nội trú", "noiTru": "Nội trú", "chonNgoaiTru": "<PERSON><PERSON><PERSON> ngoại trú", "ngoaiTru": "Ngoại trú", "coMacDinh": "<PERSON><PERSON> mặc định", "khongMacDinh": "Không mặc định", "chonMacDinh": "<PERSON><PERSON>n mặc định", "checkInVaoQmsPhong": "Checkin vào QMS phòng", "ngoaiVien": "<PERSON><PERSON><PERSON><PERSON> viện", "vuiLongNhapMaPhong": "<PERSON><PERSON> lòng nhập mã phòng", "vuiLongNhapMaPhongKhongQuaNumKyTu": "<PERSON>ui lòng nhập mã phòng không quá {{num}} ký tự!", "vuiLongNhapTenPhong": "<PERSON><PERSON> lòng nhập tên phòng", "vuiLongNhapTenPhongKhongQuaNumKyTu": "<PERSON>ui lòng nhập tên phòng không quá {{num}} ký tự!", "vuiLongChonLoaiPhong": "<PERSON><PERSON> lòng chọn loại phòng", "vuiLongNhapDiaDiem": "<PERSON><PERSON> lòng nh<PERSON><PERSON> đ<PERSON><PERSON> điểm", "vuiLongNhapThoiGianThucHienTrungBinh": "<PERSON><PERSON> lòng nhập thời gian thực hiện trung bình", "capNhatThanhCongDuLieuPhong": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu phòng!", "themMoiThanhCongDuLieuPhong": "Thêm mới thành công dữ liệu phòng!", "donViYTe": "Đơn vị y tế", "chiNhanh": "<PERSON> n<PERSON>h", "maChiNhanh": "Mã chi nh<PERSON>h", "vuiLongNhapMaChiNhanh": "<PERSON>ui lòng nhập mã chi nhánh!", "vuiLongNhapMaChiNhanhKhongQua20KyTu": "<PERSON>ui lòng nhập mã chi nhánh không quá 20 ký tự!", "tenChiNhanh": "<PERSON><PERSON><PERSON> chi nh<PERSON>h", "vuiLongNhapTenChiNhanh": "<PERSON>ui lòng nhập tên chi nhánh!", "maDonViYTe": "<PERSON><PERSON> đơn vị y tế", "vuiLongChonNguonNguoiBenh": "<PERSON>ui lòng chọn nguồn người bệnh!", "chonDonViYTe": "<PERSON><PERSON><PERSON> đơn vị y tế", "laCoQuanChuQuan": "<PERSON><PERSON> cơ quan chủ quản", "timMaChiNhanh": "<PERSON><PERSON>m mã chi nh<PERSON>h", "timTenChiNhanh": "<PERSON><PERSON>m tên chi nh<PERSON>h", "vuiLongNhapMaDonViYTe": "<PERSON><PERSON> lòng nhập mã đơn vị y tế!", "vuiLongNhapMaDonViYTeKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã đơn vị y tế không quá 20 ký tự!", "tenDonViYTe": "<PERSON><PERSON><PERSON> đơn vị y tế", "vuiLongNhapTenDonViYTe": "<PERSON><PERSON> lòng nhập tên đơn vị y tế!", "timMaDonViYTe": "<PERSON><PERSON><PERSON> mã đơn vị y tế", "timTenDonViYTe": "<PERSON><PERSON><PERSON> tên đơn vị y tế", "maHoaChat": "Mã hóa chất", "vuiLongNhapMaHoaChat": "<PERSON><PERSON> lòng nhập mã hóa chất", "vuiLongNhapMaHoaChatKhongQua20KyTu": "<PERSON>ui lòng nhập mã hóa chất không quá 20 ký tự!", "vuiLongNhapMaHoaChatKhongQua50KyTu": "<PERSON>ui lòng nhập mã hóa chất không quá 50 ký tự!", "vuiLongNhapMaHoaChatKhongQua200KyTu": "<PERSON>ui lòng nhập mã hóa chất không quá 200 ký tự!", "timMaHoaChat": "<PERSON><PERSON><PERSON> mã hóa chất", "timTenHoaChat": "<PERSON><PERSON><PERSON> tên hóa chất", "vuiLongNhapTenHoaChat": "<PERSON><PERSON> lòng nhập tên hóa chất", "vuiLongNhapTenHoaChatKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên hóa chất không quá 1000 ký tự!", "timTenDonViTinh": "<PERSON><PERSON><PERSON> tên đơn vị tính", "timNhomHoaChat": "<PERSON><PERSON><PERSON> nhóm hóa chất", "timNuocSanXuat": "<PERSON><PERSON><PERSON> sản xu<PERSON>t", "timNhaSanXuat": "<PERSON><PERSON><PERSON> n<PERSON> sản xuất", "timNhaCungCap": "<PERSON><PERSON><PERSON> nhà cung cấp", "timGiaTran": "<PERSON><PERSON><PERSON> gi<PERSON> trần", "timTyLeBhThanhToan": "Tìm tỷ lệ BH thanh toán", "timTyLeThanhToanDV": "Tìm tỷ lệ thanh toán DV", "timNhomDvCap2": "<PERSON>ìm nhóm dv cấp 2", "timNhomDvCap3": "<PERSON>ìm nhóm dv cấp 3", "vuiLongNhapGiaNhap": "<PERSON><PERSON> lòng nhập gi<PERSON> nhập", "vuiLongNhapGiaTran": "<PERSON><PERSON> lòng nhập gi<PERSON> trần", "vuiLongNhapTranBaoHiem": "<PERSON><PERSON> lòng nhập tr<PERSON><PERSON> b<PERSON><PERSON> hi<PERSON>m", "vuiLongNhapTyLeBhThanhToan": "<PERSON><PERSON> lòng nhập tỷ lệ BH thanh toán", "vuiLongChonNhomDvCap1": "<PERSON>ui lòng chọn nhóm dv cấp 1!", "vuiLongChonNhomDvCap2": "<PERSON>ui lòng chọn nhóm dv cấp 2!", "capNhatThanhCongDuLieuHoaChat": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu hóa chất!", "themMoiThanhCongDuLieuHoaChat": "Thêm mới thành công dữ liệu hóa chất!", "maToaNha": "Mã tòa nhà", "nhapMaToaNha": "<PERSON>hậ<PERSON> mã tòa nhà", "kyHieuHoaDon": "<PERSON><PERSON> hi<PERSON>u ho<PERSON> đơn", "nhapKyHieuHoaDon": "<PERSON><PERSON><PERSON><PERSON> ký hiệu ho<PERSON> đơn", "vuiLongNhapMaToaNha": "Vui lòng nhập mã tòa nhà!", "tenToaNha": "<PERSON><PERSON>n t<PERSON>a nhà", "nhapTenToaNha": "<PERSON><PERSON><PERSON><PERSON> tên tòa nhà", "vuiLongNhapTenToaNha": "Vui lòng nhập tên tòa nhà!", "nhapLoaiQms": "<PERSON><PERSON><PERSON><PERSON> Q<PERSON>", "maMauQms": "Mã mẫu QMS", "vuiLongNhapMaMauQms": "<PERSON><PERSON> lòng nhập mã mẫu QMS", "tenMauQms": "Tên mẫu QMS", "vuiLongNhapTenMauQms": "<PERSON><PERSON> lòng nhập tên mẫu QMS", "linkMauQms": "Link mẫu QMS", "vuiLongNhapLinkMauQms": "<PERSON><PERSON> lòng nhập link mẫu QMS", "maHinhThucNhapLoaiXuat": "<PERSON><PERSON> hình thức nhập/Loại xuất", "vuiLongNhapMaHinhThucNhapLoaiXuat": "<PERSON><PERSON> lòng nhập mã hình thức nhập/<PERSON><PERSON><PERSON> xuất", "vuiLongNhapMaHinhThucNhapLoaiXuatKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã hình thức nhập/<PERSON><PERSON><PERSON> xuất không quá 20 ký tự!", "tenHinhThucNhapLoaiXuat": "<PERSON><PERSON><PERSON> hình thức nhập/<PERSON><PERSON><PERSON> xuất", "vuiLongNhapTenHinhThucNhapLoaiXuat": "<PERSON><PERSON> lòng nhập tên hình thức nhập/<PERSON><PERSON><PERSON> xuất", "vuiLongNhapTenHinhThucNhapLoaiXuatKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên hình thức nhập/<PERSON><PERSON><PERSON> xuất không quá 1000 ký tự!", "chonHinhThucNhapXuat": "<PERSON><PERSON><PERSON> hình thức nhập xuất", "hinhThucNhapLoaiXuat": "<PERSON><PERSON><PERSON> thức nh<PERSON>p/Lo<PERSON>i xuất", "capNhatThanhCongDuLieuHinhThucNhapLoaiXuat": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu hình thức nhâp/loại xuất!", "themMoiThanhCongDuLieuHinhThucNhapLoaiXuat": "Thêm mới thành công dữ liệu hình thức nhâp/loại xuất!", "khongQua20KyTu": " không quá 20 ký tự!", "tenVietTatHocHamHocVi": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> tắt học hàm học vị", "vuiLongNhapTenVietTatHocHamHocVi": "<PERSON><PERSON> lòng nhập tên viết tắt học hàm học vị!", "vuiLongNhapTenVietTatHocHamHocViKhongQua20KyTu": "<PERSON><PERSON> lòng nhập tên viết tắt học hàm học vị không quá 20 ký tự!", "khongQua1000KyTu": " không quá 1000 ký tự!", "timMaHoiDong": "<PERSON><PERSON><PERSON> mã hội đồng", "tenHoiDong": "<PERSON><PERSON><PERSON> h<PERSON>i đồng", "timTenHoiDong": "<PERSON><PERSON><PERSON> tên hội đồng", "chonLoaiHoiDong": "<PERSON><PERSON><PERSON> lo<PERSON>i hội đồng", "loaiHoiDong": "<PERSON><PERSON><PERSON> hội đồng", "thongTinHoiDong": "<PERSON><PERSON><PERSON><PERSON> tin hội đồng", "chiTietHoiDong": "<PERSON> tiết hội đồng", "vuiLongChonLoaiHoiDong": "<PERSON><PERSON> lòng chọn loại hội đồng", "nhapMaHoiDong": "<PERSON><PERSON><PERSON><PERSON> mã hội đồng", "nhapTenHoiDong": "<PERSON><PERSON><PERSON><PERSON> tên hội đồng", "vuiLongNhapTenHoiDong": "<PERSON><PERSON> lòng nhập tên hội đồng", "vuiLongNhapTenHoiDongKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên không quá 1000 ký tự", "vuiLongNhapMaHoiDong": "<PERSON><PERSON> lòng nhập mã hội đồng", "vuiLongNhapMaHoiDongKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã Hội đồng không quá 20 ký tự", "maHoiDong": "<PERSON><PERSON> hội đồng", "hoiDongKiemKe": "<PERSON><PERSON>i đồng kiểm kê", "hoiDongKiemKeChiTiet": "<PERSON><PERSON><PERSON> đồng kiểm kê chi tiết", "timVaiTro": "T<PERSON>m vai trò", "vuiLongChonVaiTro": "<PERSON>ui lòng chọn vai trò", "chonVaiTro": "<PERSON><PERSON><PERSON> vai trò", "timMaChucVu": "<PERSON><PERSON><PERSON> mã chức vụ", "vuiLongChonHoVaTen": "<PERSON>ui lòng chọn họ và tên", "nhapChucVu": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "maNguoiDaiDien": "<PERSON><PERSON> người đại di<PERSON>n", "vuiLongNhapMaNguoiDaiDien": "<PERSON><PERSON> lòng nhập mã người đại diện!", "vuiLongNhapMaNguoiDaiDienKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã người đại diện không quá 20 ký tự!", "nhapMaNguoiDaiDien": "<PERSON><PERSON><PERSON><PERSON> mã người đại di<PERSON>n", "vuiLongNhapTenNguoiDaiDienKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên người đại diện không quá 1000 ký tự!", "nhapTenNguoiDaiDien": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i đại <PERSON>n", "nhapNgaySinh": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> sinh", "maNgayNghi": "<PERSON>ã ngày nghỉ", "vuiLongNhapMaNgayNghi": "<PERSON><PERSON> lòng nhập mã ngày nghỉ", "vuiLongNhapMaNgayNghiKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã ngày nghỉ không quá 20 ký tự", "tenNgayNghi": "<PERSON><PERSON><PERSON> ng<PERSON> nghỉ", "vuiLongNhapTenNgayNghi": "<PERSON><PERSON> lòng nhập tên ngày nghỉ", "vuiLongNhapTenNgayNghiKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên ngày nghỉ không quá 1000 ký tự", "vuiLongChonNgay": "<PERSON><PERSON> lòng chọn ng<PERSON>y", "ngayNghiLe": "<PERSON><PERSON><PERSON> nghỉ lễ", "capNhatThanhCongDuLieuPttt": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu PTTT!", "themMoiThanhCongDuLieuPttt": "Thêm mới thành công dữ liệu PTTT!", "timSoLuongHangDoi": "<PERSON><PERSON><PERSON> số lượng hàng đợi", "dsDoiTuong": "<PERSON> <PERSON><PERSON><PERSON>", "timDsDoiTuong": "T<PERSON>m ds đối t<PERSON>", "doiTuongTiepDon": "<PERSON><PERSON><PERSON> tư<PERSON><PERSON> tiếp đ<PERSON>", "vuiLongChonDoiTuongTiepDon": "<PERSON><PERSON> lòng chọn đối tượng tiếp đón", "vuiLongNhapKyHieu": "<PERSON><PERSON> lòng nh<PERSON>p ký hi<PERSON>u ", "timSoHieuGiuong": "<PERSON><PERSON><PERSON> s<PERSON> hiệu g<PERSON>", "vuiLongNhapSoHieuGiuong": "<PERSON><PERSON> lòng nhập số hiệu giường!", "vuiLongNhapSoHieuGiuongKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập tên xuất xứ không quá {{num}} ký tự!", "nhapSoHieuGiuong": "<PERSON><PERSON><PERSON><PERSON> số hiệu g<PERSON>", "vuiLongChonLoaiGiuong": "<PERSON>ui lòng chọn loại giường!", "vbChuyenMon": "VB chuy<PERSON>n môn", "maKhangNguyen": "Mã kháng nguyên!", "vuiLongNhapMaKhangNguyen": "<PERSON><PERSON> lòng nhập mã kháng nguyên", "vuiLongNhapTenKhangNguyen": "<PERSON><PERSON> lòng nhập tên kháng nguyên", "tuGio": "<PERSON><PERSON> giờ", "denGio": "Đến giờ", "maCaLamViec": "Mã ca làm việc", "vuiLongNhapMaCaLamViec": "Vui lòng nhập mã ca làm việc!", "vuiLongNhapCaLamViecKhongQua20KyTu": "<PERSON>ui lòng nhập ca làm việc không quá 20 ký tự!", "vuiLongNhapCaLamViec": "Vui lòng nhập ca làm việc!", "nhapCaLamViec": "<PERSON><PERSON><PERSON><PERSON> ca làm việc", "tenCaLamViec": "<PERSON>ên ca làm viêc", "vuiLongNhapTenCaLamViec": "Vui lòng nhập tên ca làm việc!", "vuiLongNhapTenCaLamViecKhongQua1000KyTu": "<PERSON>ui lòng nhập tên ca làm việc không quá 1000 ký tự!", "chonTuGio": "<PERSON><PERSON><PERSON> từ giờ", "cauHoiSangLoc": "CÂU HỎI SÀNG LỌC", "maCauHoi": "Mã câu hỏi", "timTheoMaCauHoi": "Tìm theo mã câu hỏi", "noiDungCauHoi": "<PERSON><PERSON>i dung câu hỏi", "timTheoNoiDungCauHoi": "<PERSON><PERSON><PERSON> theo nội dung câu hỏi", "chonLoaiPhieuSangLoc": "<PERSON><PERSON><PERSON> lo<PERSON>i phiếu sàng lọc", "sttCauHoi": "STT câu hỏi", "loaiCauHoi": "Loại câu hỏi", "chonLoaiCauHoi": "<PERSON><PERSON><PERSON> loại câu hỏi", "vuiLongNhapMaCauHoi": "<PERSON><PERSON> lòng nhập mã câu hỏi", "vuiLongNhapNoiDungCauHoi": "<PERSON><PERSON> lòng nhập nội dung câu hỏi", "vuiLongNhapSttCauHoi": "<PERSON><PERSON> lòng nhập STT câu hỏi", "vuiLongNhapMaNhomDvCap1": "<PERSON><PERSON> lòng nhập mã nhóm dv cấp 1!", "vuiLongNhapMaNhomDvCap1KhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã nhóm dv cấp 1 không quá 20 ký tự!", "tenLoaiPhieuSangLoc": "<PERSON><PERSON><PERSON> lo<PERSON>i phi<PERSON>u sàng lọc", "vuiLongNhapTenNhomDvCap1": "<PERSON><PERSON> lòng nhập tên nhóm dv cấp 1!", "vuiLongNhapTenNhomDvCap1KhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên nhóm dv cấp 1 không quá 1000 ký tự!", "mauInBangKiem": " Mẫu in bảng kiểm", "vuiLongNhapMauInBangKiem": "<PERSON><PERSON> lòng nhập mẫu in bảng kiểm", "timTheoMaNhomDvCap1": "<PERSON><PERSON><PERSON> theo mã nhóm dv cấp 1", "timTheoTenNhomDvCap1": "<PERSON><PERSON><PERSON> theo tên nhóm dv cấp 1", "timMauInBangKiem": "Tìm mẫu in bảng kiểm", "maHdsd": "Mã HDSD", "vuiLongNhapMaHdsd": "<PERSON>ui lòng nhập mã HDSD!", "vuiLongNhapMaHdsdKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã HDSD không quá 20 ký tự!", "nhapMaHdsd": "<PERSON><PERSON><PERSON><PERSON> mã HDSD", "tenTaiLieuHdsd": "<PERSON><PERSON><PERSON> tài li<PERSON>u <PERSON>", "vuiLongNhapTenTaiLieuHdsd": "<PERSON><PERSON> lòng nhập tên tài liệu HDSD!", "vuiLongNhapTenKhongQua1000KyTu": "<PERSON>ui lòng nhập tên không quá 1000 ký tự!", "nhapTenTaiLieuHdsd": "<PERSON><PERSON><PERSON><PERSON> tên tài li<PERSON>u HDSD", "coLoiXayRa": "<PERSON><PERSON> lỗi xảy ra", "taiAnhBia": "<PERSON><PERSON><PERSON> b<PERSON>a", "vuiLongChonFileTaiLen": "Vui lòng chọn file tải lên!", "taiLieu": "t<PERSON><PERSON> li<PERSON>u", "taiLenHdsd": "<PERSON><PERSON><PERSON>n <PERSON>", "taiLenAnhBiaHdsd": "<PERSON><PERSON><PERSON> l<PERSON>n <PERSON> b<PERSON>a HDSD", "taiFileHdsdDangHtmlPdf": "Tải file HDSD dạng html, pdf", "maTheBaoHiem": "<PERSON><PERSON> thẻ b<PERSON>o hiểm", "vuiLongNhapMaTheBh": "<PERSON>ui lòng nhập mã thẻ BH!", "vuiLongNhapMaTheBhKhongQua20KyTu": "<PERSON>ui lòng nhập mã thẻ BH không quá 20 ký tự!", "nhapMaTheBaoHiem": "<PERSON><PERSON><PERSON><PERSON> mã thẻ b<PERSON>o hiểm", "tenTheBaoHiem": "<PERSON><PERSON><PERSON> thẻ b<PERSON>o hiểm", "vuiLongNhapTenTheBh": "<PERSON>ui lòng nhập tên thẻ BH!", "vuiLongNhapTenTheBhKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên thẻ BH không quá 1000 ký tự!", "nhapTenTheBaoHiem": "<PERSON><PERSON><PERSON><PERSON> tên thẻ b<PERSON><PERSON> hiểm", "vuiLongNhapMucHuong": "<PERSON><PERSON> lòng nhập mức hưởng!", "nhapMucHuong": "<PERSON><PERSON><PERSON><PERSON> mức hưởng", "chuongTrinhGiamGia": "Chương trình giảm giá", "maChuongTrinh": "<PERSON><PERSON> chương trình", "vuiLongNhapMaChuongTrinh": "<PERSON><PERSON> lòng nhập mã chương trình", "tenChuongTrinh": "<PERSON><PERSON><PERSON> trình", "vuiLongNhapTenChuongTrinh": "<PERSON>ui lòng nhập tên chương trình!", "vuiLongChonTuNgayApDung": "<PERSON>ui lòng chọn từ ngày áp dụng!", "vuiLongChonDenNgayApDung": "<PERSON><PERSON> lòng chọn đến ngày áp dụng", "hinhThucMienGiam": "<PERSON><PERSON><PERSON> thứ<PERSON> mi<PERSON>", "vuiLongChonHinhThucGiamGia": "<PERSON>ui lòng chọn hình thức giảm giá!", "cachThucMienGiam": "<PERSON><PERSON><PERSON> thức mi<PERSON>", "vuiLongChonCachThucGiamGia": "<PERSON>ui lòng chọn cách thức giảm giá!", "phanLoaiChuongTrinh": "<PERSON><PERSON> loại chương trình", "vuiLongChonPhanLoaiChuongTrinh": "<PERSON><PERSON> lòng chọn phân loại chương trình", "loaiApDung": "<PERSON><PERSON><PERSON> dụ<PERSON>", "vuiLongChonLoaiApDungGiamGia": "<PERSON>ui lòng chọn loại áp dụng giảm giá!", "vuiLongChonLoaiMienGiam": "<PERSON><PERSON> lòng chọn lo<PERSON>i miễn g<PERSON>", "chonLaiDichVu": "<PERSON><PERSON><PERSON> lại d<PERSON>ch vụ", "timTenChuongTrinh": "<PERSON><PERSON><PERSON> tên ch<PERSON> trình", "timGiaTri": "<PERSON><PERSON><PERSON> giá trị", "timMoTa": "<PERSON><PERSON><PERSON> mô tả", "timApDungTuNgay": "<PERSON><PERSON><PERSON> dụng từ ngày", "timApDungDenNgay": "<PERSON><PERSON><PERSON> dụng đến ngày", "timHinhThucMienGiam": "<PERSON><PERSON><PERSON> hình thức miễn g<PERSON>", "timCachThucMienGiam": "<PERSON><PERSON><PERSON> c<PERSON>ch thức miễn <PERSON>", "timLoaiApDung": "<PERSON><PERSON><PERSON> lo<PERSON> d<PERSON>", "timPhanLoaiChuongTrinh": "T<PERSON>m phân loại chương trình", "chonDichVuApDungGiamGia": "<PERSON><PERSON><PERSON> dịch v<PERSON> dụng giảm giá", "chonNhomDichVuApDungGiamGia": "<PERSON><PERSON><PERSON> nhóm dịch vụ <PERSON>p dụng giảm giá", "vuiLongChonNhomDichVu": "<PERSON>ui lòng chọn nhóm dịch vụ", "tenNhomDichVu": "<PERSON><PERSON><PERSON> d<PERSON> vụ", "timTenNhomDichVu": "<PERSON><PERSON><PERSON> tên nh<PERSON>m dịch vụ", "maVoucher": "Mã voucher", "timMaVoucher": "<PERSON><PERSON><PERSON> mã voucher", "vuiLongChonChuongTrinhGiamGia": "<PERSON>ui lòng chọn chương trình giảm giá!", "vuiLongNhapMaVoucher": "<PERSON><PERSON> lòng nhập mã voucher", "maSoBatDau": "<PERSON>ã số bắt đầu", "vuiLongNhapMaSoBatDau": "<PERSON>ui lòng nhập mã số bắt đầu!", "maSoKetThuc": "Mã số kết thúc", "vuiLongNhapMaSoKetThuc": "<PERSON>ui lòng nhập mã số kết thúc!", "soLuongPhaiSoLuongDaSuDung": "Số lượng phải >= số lượng đã sử dụng", "soLuongDaSuDung": "Số lượng đã sử dụng", "vuiLongNhapSoLuongDaSuDung": "<PERSON><PERSON> lòng nhập số lượng đã sử dụng", "soLuongConLai": "Số lượng còn lại", "chonChuongTrinhGiamGia": "<PERSON><PERSON><PERSON> ch<PERSON> trình giảm giá", "themMoiHangLoat": "<PERSON><PERSON><PERSON><PERSON> mới hàng lo<PERSON>t", "soDiemToiThieu": "<PERSON><PERSON> điểm tối thiểu", "timTenHangThe": "<PERSON><PERSON><PERSON> tên hạng thẻ", "vuiLongNhapSoDiemToiThieu": "<PERSON><PERSON> lòng nhập số điểm tối thiểu!", "maNguoiGioiThieu": "<PERSON>ã người giới thiệu", "vuiLongNhapMaNguoiGioiThieu": "<PERSON>ui lòng nhập mã người giới thiệu!", "vuiLongNhapMaNguoiGioiThieuKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã người giới thiệu không quá 20 ký tự!", "tenNguoiGioiThieu": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i giới thiệu", "vuiLongNhapTenNguoiGioiThieu": "<PERSON>ui lòng nhập tên người giới thiệu!", "chonTenNguonNguoiBenh": "<PERSON><PERSON><PERSON> tên nguồn ng<PERSON><PERSON>i bệnh", "timTenNguoiGioiThieu": "<PERSON><PERSON><PERSON> tên ng<PERSON>ời giới thiệu", "timMaNguonNguoiBenh": "<PERSON><PERSON><PERSON> mã nguồn ng<PERSON><PERSON><PERSON> b<PERSON>nh", "timTenNguonNguoiBenh": "<PERSON><PERSON><PERSON> tên nguồn ng<PERSON><PERSON> b<PERSON>nh", "vuiLongNhapMaLieuDungKhongQua20KyTu": "<PERSON>ui lòng nhập mã liều dùng không quá 20 ký tự!", "vuiLongNhapTenLieuDungKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên liều dùng không quá 1000 ký tự!", "vuiLongNhapSoLanNgay": "<PERSON><PERSON> lòng nhập số lần/ ngày", "soLuongLan": "Số lượng/ lần", "vuiLongNhapSoLuongLan": "<PERSON><PERSON> lòng nhập số lượ<PERSON>/ lần", "capNhatThanhCongDuLieuLieuDung": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu liều dùng!", "themMoiThanhCongDuLieuLieuDung": "Thêm mới thành công dữ liệu liều dùng!", "vuiLongNhapMaHoatChatKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã hoạt chất không quá 20 ký tự!", "vuiLongNhapTenHoatChat": "<PERSON><PERSON> lòng nhập tên ho<PERSON>t chất", "vuiLongNhapTenHoatChatKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên hoạt chất không quá 1000 ký tự!", "chonLoaiDV": "Chọn loại DV", "capNhatThanhCongDuLieuHoatChat": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu hoạt chất!", "themMoiThanhCongDuLieuHoatChat": "Thêm mới thành công dữ liệu hoạt chất!", "themMoiKhongThanhCongDuLieuHoatChat": "Thêm mới không thành công dữ liệu hoạt chất!", "capNhatKhongThanhCongDuLieuHoatChat": "<PERSON><PERSON><PERSON> nhật không thành công dữ liệu hoạt chất!", "mauNen": "<PERSON><PERSON><PERSON>", "mauPhanLoaiNguoiBenh": "<PERSON><PERSON><PERSON> phân loại ng<PERSON><PERSON>i b<PERSON>nh", "mauChu": "<PERSON><PERSON><PERSON> chữ", "nhapMauChu": "<PERSON><PERSON><PERSON><PERSON> màu chữ", "nhapMauNen": "<PERSON><PERSON><PERSON><PERSON> màu n<PERSON>n", "timTenBenh": "<PERSON><PERSON><PERSON> tên b<PERSON>nh", "tenLoaiBenh": "<PERSON><PERSON><PERSON> b<PERSON>nh", "tenNhomBenhChinh": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> ch<PERSON>h", "timTenNhomBenhChinh": "<PERSON><PERSON><PERSON> tên nhóm b<PERSON>nh ch<PERSON>h", "tenNhomBenhPhuI": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> b<PERSON><PERSON> phụ I", "timTenNhomBenhPhuI": "<PERSON><PERSON>m tên nhóm bệnh phụ I", "tenNhomBenhPhuII": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> b<PERSON><PERSON> ph<PERSON> II", "timTenNhomBenhPhuII": "<PERSON><PERSON><PERSON> tên nhóm b<PERSON>nh phụ II", "timTenChuong": "<PERSON><PERSON><PERSON> tên <PERSON>", "sttChuongBenh": "STT chư<PERSON>ng bệnh", "maChuongBenh": "<PERSON><PERSON> ch<PERSON> b<PERSON><PERSON>", "tenChuongBenh": "<PERSON><PERSON><PERSON> b<PERSON>nh", "timTheoTenChuongBenh": "<PERSON><PERSON><PERSON> theo tên ch<PERSON> b<PERSON>nh", "maNhomBenhChinh": "<PERSON><PERSON> n<PERSON> b<PERSON><PERSON> ch<PERSON>h", "vuiLongNhapMaNhomBenhChinh": "<PERSON><PERSON> lòng nhập mã nhóm b<PERSON><PERSON> ch<PERSON>h", "vuiLongNhapMaNhomBenhChinhKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã nhóm b<PERSON>nh chính không quá 20 ký tự!", "vuiLongNhapTenNhomBenhChinh": "<PERSON><PERSON> lòng nhập tên nhóm b<PERSON><PERSON> ch<PERSON>h", "vuiLongNhapTenNhomBenhChinhKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên nhóm b<PERSON>nh chính không quá 1000 ký tự!", "vuiLongNhapSttChuongBenh": "<PERSON>ui lòng nhập stt chư<PERSON>ng bệnh!", "vuiLongNhapMaChuongBenh": "<PERSON><PERSON> lòng nhập mã chư<PERSON> b<PERSON>nh", "vuiLongNhapMaChuongBenhKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã chương bệnh không quá 20 ký tự!", "vuiLongNhapTenChuongBenh": "<PERSON>ui lòng nhập tên chương bệnh!", "vuiLongNhapTenChuongBenhKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên chương bệnh không quá 1000 ký tự!", "timTheoTenNhomBenhChinh": "<PERSON><PERSON><PERSON> theo tên nhóm b<PERSON><PERSON> ch<PERSON>h", "maNhomBenhPhuI": "<PERSON><PERSON> nh<PERSON>m b<PERSON>nh phụ I", "timTheoTenNhomBenhPhuI": "T<PERSON>m theo tên nhóm bệnh phụ I", "tatCaNhomBenhChinh": "<PERSON><PERSON><PERSON> cả nhóm b<PERSON><PERSON> ch<PERSON>h", "chonNhomBenhChinh": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> ch<PERSON>h", "vuiLongNhapMaNhomBenhPhuI": "<PERSON><PERSON> lòng nhập mã nhóm bệnh phụ I", "vuiLongNhapMaNhomBenhPhuIKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã nhóm bệnh phụ I không quá 20 ký tự!", "vuiLongNhapTenNhomBenhPhuI": "<PERSON>ui lòng nhập tên nhóm bệnh phụ I!", "vuiLongNhapTenNhomBenhPhuIKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên nhóm bệnh phụ I không quá 1000 ký tự!", "vuiLongChonNhomBenhChinh": "<PERSON><PERSON> lòng chọn nhóm b<PERSON><PERSON> ch<PERSON>h", "maLoaiBenh": "<PERSON><PERSON> lo<PERSON> b<PERSON>nh", "vuiLongNhapMaLoaiBenh": "<PERSON><PERSON> lòng nhập mã lo<PERSON>i b<PERSON>nh", "vuiLongNhapMaLoaiBenhKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã loại bệnh không quá 20 ký tự!", "vuiLongNhapTenLoaiBenh": "<PERSON><PERSON> lòng nhập tên lo<PERSON>i b<PERSON>nh", "vuiLongNhapTenLoaiBenhKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên loại bệnh không quá 1000 ký tự!", "chonNhomBenhPhuI": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> b<PERSON><PERSON> phụ I", "chonNhomBenhPhuII": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> b<PERSON><PERSON> ph<PERSON> II", "timTheoTenLoaiBenh": "<PERSON><PERSON><PERSON> theo tên lo<PERSON>i b<PERSON>nh", "tatCaNhomBenhPhuI": "<PERSON><PERSON><PERSON> cả nhóm b<PERSON>nh phụ I", "tatCaNhomBenhPhuII": "<PERSON><PERSON><PERSON> cả nhóm b<PERSON><PERSON> phụ II", "maTenBenh": "<PERSON><PERSON> tên b<PERSON>nh", "timMaBenh": "<PERSON><PERSON><PERSON> mã b<PERSON>nh", "maNhomBCBYT": "Mã nhóm BC BYT", "timMaNhomBCBYT": "Tìm mã nhóm BC BYT", "maNhomChiTiet": "<PERSON>ã nhóm chi tiết", "timMaNhomChiTiet": "T<PERSON>m mã nhóm chi tiết", "maBenhGuiBHYT": "Mã Bệnh gửi BHYT", "timMaBenhGuiBHYT": "T<PERSON>m mã bệnh gửi BHYT", "tatCaLoaiBenh": "<PERSON><PERSON><PERSON> c<PERSON> lo<PERSON>i b<PERSON>nh", "chonNhomLoaiBenh": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> lo<PERSON> b<PERSON>nh", "vuiLongNhapMaTenBenh": "<PERSON><PERSON> lòng nhập mã tên bệnh", "vuiLongNhapMaTenBenhKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã tên bệnh không quá 20 ký tự!", "tenTenBenh": "<PERSON><PERSON><PERSON> tên b<PERSON>nh", "vuiLongNhapTenTenBenh": "<PERSON>ui lòng nhập tên tên bệnh!", "vuiLongNhapTenTenBenhKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên tên bệnh không quá 1000 ký tự!", "vuiLongNhapMaNhomBCBYT": "<PERSON><PERSON> lòng nhập mã nhóm BC BYT", "vuiLongNhapMaNhomChiTiet": "<PERSON><PERSON> lòng nhập mã nhóm chi tiết", "vuiLongNhapMaBenhGuiBHYT": "<PERSON><PERSON> lòng nhập mã bệnh gửi BHYT", "maNhomBenhPhuII": "<PERSON><PERSON> nh<PERSON>m b<PERSON>nh phụ II", "vuiLongNhapMaNhomBenhPhuII": "<PERSON><PERSON> lòng nhập mã nhóm b<PERSON>nh phụ II", "vuiLongNhapTenNhomBenhPhuII": "<PERSON><PERSON> lòng nhập tên nhóm bệnh phụ II!", "vuiLongNhapTenNhomBenhPhuIIKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên nhóm bệnh phụ II không quá 1000 ký tự!", "capNhatThanhCongDuLieuChuongBenh": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu chương bệnh!", "themMoiThanhCongDuLieuChuongBenh": "Thê<PERSON> mới thành công dữ liệu chương bệnh!", "capNhatThanhCongDuLieuNhomBenhChinh": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu nhóm bệnh ch<PERSON>h!", "themMoiThanhCongDuLieuNhomBenhChinh": "Thêm mới thành công dữ liệu nhóm bệnh ch<PERSON>h!", "capNhatThanhCongDuLieuNhomBenhPhuI": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu nhóm bệnh phụ I!", "themMoiThanhCongDuLieuNhomBenhPhuI": "Thêm mới thành công dữ liệu nhóm bệnh phụ I!", "capNhatThanhCongDuLieuNhomBenhPhuII": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu nhóm bệnh phụ II!", "themMoiThanhCongDuLieuNhomBenhPhuII": "Thêm mới thành công dữ liệu nhóm bệnh phụ II!", "capNhatThanhCongDuLieuTenBenh": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu tên bệnh!", "themMoiThanhCongDuLieuTenBenh": "Thêm mới thành công dữ liệu tên bệnh!", "timLoaiBenh": "<PERSON><PERSON><PERSON> b<PERSON>nh", "tenTitle": "Tên {{ title }}", "maTitle": "Mã {{ title }}", "timTitle": "Tìm {{ title }}", "vuiLongNhapTitle": "<PERSON><PERSON> lòng nhập {{ title }}", "vuiLongChonTitle": "<PERSON><PERSON> lòng chọn {{ title }}", "khoaChiDinhDichVu": "Khoa chỉ định dịch vụ", "timMaDichVu": "<PERSON><PERSON><PERSON> mã dịch vụ", "timGiaKhongBH": "T<PERSON>m giá không BH", "timGiaBH": "Tìm giá BH", "timGiaPhuThu": "Tìm giá phụ thu", "chonĐVT": "Chọn ĐVT", "chonDichVuCap1": "<PERSON><PERSON><PERSON> v<PERSON> cấp 1", "chonDichVuCap2": "<PERSON><PERSON><PERSON> v<PERSON> cấ<PERSON> 2", "chonDichVuCap3": "<PERSON><PERSON><PERSON> v<PERSON> cấ<PERSON> 3", "phanLoaiGiuong": "<PERSON>ân loại giư<PERSON>", "chonPhanLoaiGiuong": "<PERSON><PERSON>n phân loại giường", "truongHopKeDV": "<PERSON>rư<PERSON><PERSON> hợp kê DV", "chonTruongHopKeDV": "Chọn trư<PERSON><PERSON> hợp kê DV", "dichVu": "<PERSON><PERSON><PERSON> v<PERSON>", "tuongDuong": "<PERSON><PERSON><PERSON><PERSON>", "soQuyetDinh": "<PERSON><PERSON> quyết định", "nhapTitle": "<PERSON><PERSON><PERSON><PERSON> {{ title }}", "chonTitle": "<PERSON><PERSON><PERSON> {{ title }}", "vatTu": "<PERSON><PERSON><PERSON>", "kyHieu": "<PERSON><PERSON>", "gia": "Giá", "timGiaTranBH": "<PERSON><PERSON>m giá trần BH", "timTyLeBHTT": "Tìm tỷ lệ BHTT", "tyLeBHThanhToan": "Tỷ lệ BH thanh toán", "timTyLeTTDV": "Tìm tỷ lệ TTDV", "timGia": "<PERSON><PERSON><PERSON> gi<PERSON>", "maChiTiet": "<PERSON><PERSON> chi tiết", "tenChiTiet": "<PERSON><PERSON><PERSON> chi tiết", "soLuongTheoBo": "Số lượng theo bộ", "giaNhapSauVAT": "<PERSON><PERSON><PERSON> sau VAT", "tiLeThanhToan": "Tỉ lệ thanh toán", "maAnhXa": "Mã ánh xạ", "chiTietTrungThau": "<PERSON> tiết trúng thầu", "chiTietBoPhan": "<PERSON> tiết bộ phận", "maKichCo": "<PERSON><PERSON> kích cỡ", "kichCo": "<PERSON><PERSON><PERSON> cỡ", "chonKichCo": "<PERSON><PERSON><PERSON> k<PERSON>ch cỡ", "tenKichCoTrungThau": "<PERSON><PERSON><PERSON> kích cỡ trúng thầu", "chiTietKichCo": "<PERSON> tiết kích cỡ", "sTTTrenBangKe": "STT trên bảng kê", "timTheoSTTTrenBangKe": "T<PERSON><PERSON> theo STT trên bảng kê", "trangThaiHoanThanhDV": "Trạng thái hoàn thành DV", "trangThaiKhongDuocHoanThanhDV": "Trạng thái không đư<PERSON><PERSON> hoàn thành DV", "boQuaKLLau": "Bỏ qua KL lâu", "trangThaiSinhSoThuTu": "Tr<PERSON>ng thái sinh số thứ tự", "sinhSoRiengChoNBUuTien": "Sinh số riêng cho NB Ưu tiên", "chonUuTien": "<PERSON><PERSON><PERSON>u tiên", "sinhSoRiengChoNBNoiTru": "Sinh số riêng cho NB Nội trú", "timTheoMaNhomDvCap2": "<PERSON><PERSON><PERSON> theo mã nhóm dv cấp 2", "timTheoTenNhomDvCap2": "<PERSON><PERSON><PERSON> theo tên nhóm dv cấp 2", "tenBaoCao": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "baoCao": "Báo cáo", "luuPhimChup": "<PERSON><PERSON><PERSON> phim ch<PERSON>p", "congSuatToiDa": "<PERSON><PERSON><PERSON> suất tối đa", "tatCaNhomDvCap1": "<PERSON><PERSON><PERSON> c<PERSON> nhóm dv cấp 1", "sTTTrenBaoCao": "STT trên báo cáo", "timThuTu": "<PERSON><PERSON><PERSON> thứ tự", "maNhomDvCap3": "Mã nhóm dv cấp 3", "timTheoMaNhomDvCap3": "<PERSON><PERSON><PERSON> theo mã nhóm dv cấp 3", "tenNhomDvCap3": "<PERSON><PERSON><PERSON> dv cấp 3", "timTheoTenNhomDvCap3": "<PERSON><PERSON><PERSON> theo tên nhóm dv cấp 3", "chonDvTheoYeuCau": "Chọn dv theo y<PERSON>u c<PERSON>u", "tatCaNhomDvCap2": "<PERSON><PERSON><PERSON> c<PERSON> nhóm dv cấp 2", "trangThaiHoanThanhDv": "Tr<PERSON>ng thái hoàn thành dv", "trangThaiKhongHoanThanhDv": "Tr<PERSON>ng thái không hoàn thành dv", "boQuaKetQuaLau": "Bỏ qua kết quả lâu", "tenBaoCaoBatBuocDien": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o b<PERSON><PERSON> bu<PERSON><PERSON> điền", "vuiLongNhapSTT": "<PERSON><PERSON> lòng nhập STT", "nhapSTT": "Nhập STT", "STT": "STT", "tuyenBenhVien": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> viện", "hangBenhVien": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> viện", "timTinhThanhPho": "Tìm tỉnh thành phố", "benhVien": "Bệnh viện", "taiLen": "<PERSON><PERSON><PERSON>", "anhBanQuyenThuongHieu": "Ảnh bản quyền thương hiệu", "tongTienGoi": "<PERSON><PERSON><PERSON> tiền gói", "khongDuocNhapSLMacDinhLonHonSlKhaiBao": "<PERSON><PERSON><PERSON><PERSON> đượ<PERSON> nhập Số lượng mặc định lớn hơn Số lượng khai báo", "soLuongMacDinh": "<PERSON><PERSON> lượng mặc định", "goiDichVuChiTiet": "g<PERSON><PERSON> d<PERSON>ch vụ chi tiết", "dichVuDaTonTaiTrongBoChiDinh": "<PERSON><PERSON><PERSON> vụ {{name}} đã tồn tại trong bộ chỉ định. Bạn có muốn tiếp tục thêm?", "maDiaChiNhung": "<PERSON>ã địa chỉ nhúng", "diaChiNhung": "Địa chỉ nhúng", "vuiLongNhapMaDiaChiNhung": "<PERSON><PERSON> lòng nhập mã địa chỉ nhúng", "vuiLongNhapMaDiaChiNhungKhongQua50KyTu": "<PERSON><PERSON> lòng nhập mã địa chỉ nhúng không quá 50 ký tự", "vuiLongNhapTenCap2": "<PERSON><PERSON> lòng nh<PERSON><PERSON> tên cấp 2", "vuiLongNhapTenCap2KhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên cấp 2 không quá 1000 ký tự", "vuiLongNhapGroupId": "<PERSON><PERSON> lòng nhập groupId", "vuiLongNhapGroupIdKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập groupId không quá 1000 ký tự", "groupId": "groupId", "tenCap2": "<PERSON><PERSON><PERSON> c<PERSON> 2", "reportId": "reportId", "publicKey": "public<PERSON>ey", "vuiLongNhapReportId": "<PERSON><PERSON> lòng nh<PERSON>p reportId", "vuiLongNhapPublicKey": "<PERSON><PERSON> lòng nh<PERSON>p publicKey", "vuiLongNhapReportIdKhongQua1000KyTu": "<PERSON><PERSON> lòng nh<PERSON>p reportId không quá 1000 ký tự", "vuiLongNhapPublicKeyKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập publicKey không quá 1000 ký tự", "tenCap1": "<PERSON><PERSON>n c<PERSON> 1", "vuiLongNhapTenCap1": "<PERSON><PERSON> lòng nh<PERSON>p tên cấp 1", "vuiLongNhapTenCap1KhongQua70KyTu": "<PERSON><PERSON> lòng nhập tên cấp 1 không quá 70 ký tự", "module": "<PERSON><PERSON><PERSON>", "vuiLongChonNhomTinhNang": "<PERSON><PERSON> lòng chọn nhóm t<PERSON>h năng", "vuiLongChonIcon": "<PERSON><PERSON> lòng chọn icon", "thuTuTrongNhomTinhNang": "<PERSON><PERSON><PERSON> tự trong nhóm tính năng", "vuiLongNhapThuTuTrongNhomTinhNang": "<PERSON><PERSON> lòng nhập thứ tự trong nhóm tính năng", "nhapSoThuTuTrongNhomTinhNang": "<PERSON><PERSON><PERSON><PERSON> số thứ tự trong nhóm tính năng", "vuiLongNhapMoTaKhongQua500KyTu": "<PERSON><PERSON> lòng nhập mô tả không quá 500 ký tự", "laLinkPowerbi": "Là Link PowerBI", "moTrangMoi": "Mở trang mới", "icon": "Icon", "chonGio": "<PERSON><PERSON><PERSON> g<PERSON>", "chonIcon": "Chọn icon", "quanLyTaiChinhKeToan": "<PERSON><PERSON><PERSON><PERSON> lý tài ch<PERSON>h - kế toán", "quanLyTaiSanTrangThietBi": "<PERSON><PERSON><PERSON><PERSON> lý tài sản, trang thi<PERSON><PERSON> bị", "quanLyNhanLuc": "<PERSON><PERSON><PERSON><PERSON> lý nhân lực", "quanLyVanBanYTe": "<PERSON><PERSON><PERSON><PERSON> lý văn bản y tế", "chiDaoTuyenChiDaoTuXa": "Chỉ đạo tuyến (chỉ đạo từ xa)", "trangThongTinDienTu": "<PERSON>rang thông tin điện tử", "thuDienTuNoiBo": "<PERSON><PERSON><PERSON> điện tử nội bộ", "quanLyDaoTao": "<PERSON><PERSON><PERSON><PERSON> lý đào tạo", "quanLyNghienCuuKhoaHoc": "<PERSON><PERSON><PERSON><PERSON> lý nghiên cứu khoa học", "hoSoBenhAnDienTu": "<PERSON><PERSON>, b<PERSON><PERSON> <PERSON>n điện tử", "khaoSatHaiLong": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t hài lòng", "chiSoChatLuongBenhVien": "Chỉ số chất lư<PERSON> bệnh viện", "suCoYKhoa": "Sự cố y khoa", "quyen": "q<PERSON><PERSON><PERSON>", "timMaQuyen": "<PERSON><PERSON><PERSON> mã quyền", "timTheoTenQuyen": "<PERSON><PERSON><PERSON> theo tên quyền", "timTheoMoTa": "<PERSON><PERSON><PERSON> theo mô tả", "timMaNhomTinhNang": "T<PERSON>m mã nhóm tính năng", "timTheoTenNhomTinhNang": "T<PERSON>m theo tên nhóm tính năng", "danhMucThietLapChung": "<PERSON><PERSON> m<PERSON><PERSON> thi<PERSON><PERSON> lậ<PERSON> chung", "noiDungThongBao": "<PERSON><PERSON><PERSON> dung thông báo", "loaiThongBao": "<PERSON><PERSON><PERSON> thông báo", "xoaThongBao": "<PERSON><PERSON><PERSON> thông báo", "vuiLongNhapNoiDung": "<PERSON><PERSON> lòng nh<PERSON>p nội dung", "vuiLongNhapNoiDungKhongQua1500KyTu": "<PERSON><PERSON> lòng nhập nội dung không quá 1500 ký tự", "vuiLongChonLoaiThongBao": "<PERSON><PERSON> lòng chọn loại thông báo", "chonChucVu": "<PERSON><PERSON><PERSON> ch<PERSON> v<PERSON>", "capNhatThanhCongDuLieuThongBao": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu thông báo", "themMoiThanhCongDuLieuThongBao": "<PERSON>hê<PERSON> mới thành công dữ liệu thông báo", "xoaThanhCong": "<PERSON><PERSON><PERSON> thành công", "thietLapChung": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> chung", "themMoiThanhCongDuLieuThietLapPhieuLinhTra": "Thê<PERSON> mới thành công dữ liệu thiết lập phiếu lĩnh trả", "danhMucKhoaDuLieuBaoCaoKho": "<PERSON><PERSON> dữ liệu báo cáo kho", "khoaDuLieuBaoCaoKho": "Khóa dữ liệu báo cáo kho", "hangHoaCoDonViSoCapBatBuocDienHeSoDinhMucLonHon0": "<PERSON><PERSON><PERSON> hóa có đơn vị sơ cấp. <PERSON><PERSON><PERSON> buộ<PERSON> điền hệ số định mức > 0", "xuatDanhSachVatTuDayCong": "<PERSON><PERSON><PERSON> danh sách VT đ<PERSON>y cổng", "ngayThemMoi": "<PERSON><PERSON><PERSON> thêm mới", "xuatDuLieuMoi": "<PERSON><PERSON><PERSON> dữ liệu mới", "vuiLongChonngayThemMoi": "Vui lòng chọn ngày thêm mới!", "chonThuoc": "<PERSON><PERSON><PERSON>", "xuatDanhSachThuocDayCong": "<PERSON><PERSON><PERSON> danh sách thuốc đ<PERSON>y cổng", "url": "URL", "vuiLongNhapUrl": "<PERSON><PERSON> lòng nhập url", "vuiLongNhapUrlKhongQua500KyTu": "<PERSON><PERSON> lòng nhập Url không quá 500 ký tự", "xuatDanhSachDichVuDayCong": "<PERSON><PERSON><PERSON> danh sách dịch vụ đ<PERSON>y cổng", "danhSachLichKhoaDuLieu": "<PERSON><PERSON> s<PERSON>ch lịch kh<PERSON>a dữ liệu", "soGioCanhBaoThuocHuy": "S<PERSON> gi<PERSON> thu<PERSON><PERSON> c<PERSON>n h<PERSON>y", "dichVuKhamBenh": "<PERSON><PERSON><PERSON> v<PERSON> b<PERSON>nh", "dichVuXetNghiem": "<PERSON><PERSON><PERSON> v<PERSON> x<PERSON>", "dichVuCanLamSang": "<PERSON><PERSON><PERSON> v<PERSON> cận lâm sàng", "daTonTaiMaCodetrongDmServiceName": "<PERSON><PERSON> tồn tại mã = {{code}}, trong DM {{serviceName}}", "daTonTaiTenTitletrongDmServiceName": "<PERSON><PERSON> tồn tại tên = {{title}}, trong DM {{serviceName}}", "capNhatThanhCongDuLieuNhomDonViTinh": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu nhóm đơn vị tính!", "themMoiThanhCongDuLieuNhomDonViTinh": "Thêm mới thành công dữ liệu nhóm đơn vị tính!", "thoiGianCachTiem": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON>ch tiêm (ngày)", "khongDungChoDoiTuongBhyt": "<PERSON>h<PERSON>ng dùng cho đối tượng BHYT", "chonKhongDungChoDoiTuongBhyt": "<PERSON><PERSON><PERSON> không dùng cho đối tượng BHYT", "lyDoVaoVien": "Lý do vào viện", "vuiLongNhapLyDoVaoVien": "<PERSON><PERSON> lòng nhập lý do vào viện", "vuiLongNhapChanDoanVaoVien": "<PERSON><PERSON> lòng nhập chẩn đoán vào viện", "vuiLongNhapTomTatKetQuaCls": "<PERSON><PERSON> lòng nhập tóm tắt kết quả CLS", "coKetQuaLau": "<PERSON><PERSON> kết quả lâu", "troLyYTe": "<PERSON><PERSON><PERSON> lý y tế", "yTe4Cham0": "<PERSON> tế 4.0", "dichVuCDHA": "D<PERSON><PERSON> vụ CĐHA-TDCN", "dichVuNgoaiDieuTri": "<PERSON><PERSON><PERSON> vụ ngoài điều trị", "dichVuPTTT": "<PERSON><PERSON><PERSON> vụ Phẫu thuật - <PERSON><PERSON><PERSON> thuật", "vuiLongNhapChanDoanNoiGioiThieu": "<PERSON><PERSON> lòng nhập chẩn đoán nơi giới thiệu", "vuiLongNhapQuaTrinhBenhLy": "<PERSON><PERSON> lòng nhập quá trình b<PERSON>nh lý", "tienSuBanThan": "<PERSON><PERSON><PERSON><PERSON> sử bản thân", "vuiLongNhapTienSuBanThan": "<PERSON><PERSON> lòng nhập tiền sử bản thân", "tienSuGiaDinh": "Tiền sử gia đình", "vuiLongNhapTienSuGiaDinh": "<PERSON><PERSON> lòng nhập tiền sử gia đình", "vuiLongNhapToanThan": "<PERSON><PERSON> lòng nhập toàn thân", "vuiLongNhapCacBoPhan": "<PERSON><PERSON> lòng nhập các bộ phận", "daChoVaoChamSocTaiKhoa": "Đã cho vào chăm sóc tại khoa", "vuiLongChonDaChoVaoChamSocTaiKhoa": "<PERSON>ui lòng chọn đã cho vào chăm sóc tại khoa", "khoaHangLoat": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng lo<PERSON>", "danhMucMauKetQuaKhamBenh": "<PERSON><PERSON> mục Mẫu kết quả khám bệnh", "maMauKetQua": "<PERSON><PERSON> mẫu kết quả", "timMaMauKetQua": "<PERSON><PERSON><PERSON> mã mẫu kết quả", "vuiLongNhapMaMauKetQua": "<PERSON><PERSON> lòng nhập mã mẫu kết quả", "tenMauKetQua": "<PERSON><PERSON><PERSON> mẫu kết quả", "timTenMauKetQua": "<PERSON><PERSON><PERSON> tên mẫu kết quả", "vuiLongNhapTenMauKetQua": "<PERSON><PERSON> lòng nhập tên mẫu kết quả", "loaiChuyenKhoaKsk": "<PERSON><PERSON><PERSON> chuyên khoa KSK", "chonLoaiChuyenKhoaKsk": "<PERSON><PERSON><PERSON> lo<PERSON>i chuyên khoa KSK", "vuiLongChonLoaiChuyenKhoaKsk": "<PERSON><PERSON> lòng chọn loại chuyên khoa KSK", "mauKetQuaKhamSucKhoe": "Mẫu kết quả kh<PERSON>m sức khoẻ", "mauKetQuaKhamBenh": "Mẫu kết quả kh<PERSON>m bệnh", "themMoiThanhCongDuLieuMauKqKsk": "Thêm mới thành công dữ liệu mẫu kết quả khám sức khoẻ!", "capNhatThanhCongDuLieuMauKqKsk": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu mẫu kết quả khám sức khoẻ!", "mauKetQuaXetNghiem": "Mẫu kết quả xét nghiệm", "lieuDungBacSi": "<PERSON><PERSON><PERSON> d<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "layKetQuaLenPhieu": "<PERSON><PERSON><PERSON> kết quả lên phi<PERSON>u", "chonPhieu": "<PERSON><PERSON><PERSON>", "loaiThoiGianApDung": "<PERSON><PERSON><PERSON> thời gian <PERSON> d<PERSON>ng", "chonLoaiThoiGianApDung": "<PERSON><PERSON><PERSON> lo<PERSON>i thời gian <PERSON> dụng", "vuiLongChonLoaiThoiGianApDung": "<PERSON><PERSON> lòng chọn loại thời gian <PERSON>p dụng", "vuiLongChonBaoCao": "<PERSON><PERSON> lòng chọn báo c<PERSON>o", "batBuocNhap1Trong3GiaTri": "Bắt buộc nhập 1 trong 3 giá trị", "chiDinhCungGoi": "Chỉ định cùng gói", "slToiDa": "SL tối đa", "nhapSlToiDa": "<PERSON><PERSON><PERSON><PERSON> số lư<PERSON><PERSON> tối đa", "themDichVuHangHoa": "<PERSON><PERSON><PERSON><PERSON>, hà<PERSON> h<PERSON>a", "timDichVuHangHoa": "<PERSON><PERSON><PERSON>, hà<PERSON> h<PERSON>a", "nhapTuKhoaTimKiem": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "vuiLongNhapKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập không quá {{ num }} ký tự", "chonHoaChat": "<PERSON><PERSON><PERSON> h<PERSON>a chất", "mauKetQuaCDHATDCN": "Mẫu kết quả CĐHA - TDCN", "themKho": "<PERSON><PERSON><PERSON><PERSON> kho", "chonLoaiBenh": "<PERSON><PERSON><PERSON> b<PERSON>nh", "capNhatThanhCongDuLieuBoChiDinhChiTiet": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu bộ chỉ định chi tiết!", "themMoiThanhCongDuLieuBoChiDinhChiTiet": "Thêm mới thành công dữ liệu bộ chỉ định chi tiết!", "chiPhiHapSayVTYT": "<PERSON> phí hấp sấy VTYT", "capNhatThanhCongDuLieuChiSoCon": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu chỉ số con!", "themMoiThanhCongDuLieuChiSoCon": "Thêm mới thành công dữ liệu chỉ số con!", "capNhatThanhCongDuLieuChuongTrinhGiamGia": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu chương trình giảm giá!", "themMoiThanhCongDuLieuChuongTrinhGiamGia": "Thêm mới thành công dữ liệu chương trình giảm giá!", "capNhatThanhCongDuLieuThietLapQuyTacChonGiuong": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu thiết lập quy tắc chọn giường!", "themMoiThanhCongDuLieuThietLapQuyTacChonGiuong": "<PERSON>hê<PERSON> mới thành công dữ liệu thiết lập quy tắc chọn giường", "danhMucThietLapKhoChiDinh": "<PERSON><PERSON> mục thiết lập kho chỉ định", "goiPtttChiTiet": "<PERSON><PERSON><PERSON> phẫu thuật thủ thuật chi tiết", "capNhatThanhCongDuLieuHangThe": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu hạng thẻ!", "themMoiThanhCongDuLieuHangThe": "Thêm mới thành công dữ liệu hạng thẻ!", "capNhatThanhCongDuLieuHocHamHocVi": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu học hàm học vị!", "themMoiThanhCongDuLieuHocHamHocVi": "<PERSON>hê<PERSON> mới thành công dữ liệu học hàm học vị!", "capNhatThanhCongDuLieuLoaiCapCuu": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu loại cấp cứu!", "themMoiThanhCongDuLieuLoaiCapCuu": "Thêm mới thành công dữ liệu loại cấp cứu!", "kichCoVatTu": "<PERSON><PERSON><PERSON> cỡ vật tư", "capNhatThanhCongDuLieuLoaiHinhThanhToan": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu loại hình thanh toán!", "themMoiThanhCongDuLieuLoaiHinhThanhToan": "Thêm mới thành công dữ liệu loại hình thanh toán!", "loaiDoiTuongPhuongThucTT": "<PERSON><PERSON><PERSON> đối tượng ph<PERSON><PERSON><PERSON> thức thanh toán", "luocDoPt": "<PERSON><PERSON><PERSON><PERSON> đồ Phẫu thuật", "capNhatThanhCongDuLieuVoucher": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu voucher!", "themMoiThanhCongDuLieuVoucher": "Thêm mới thành công dữ liệu voucher!", "capNhatThanhCongDuLieuMoiQuanHe": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu mối quan hệ!", "themMoiThanhCongDuLieuMoiQuanHe": "Thêm mới thành công dữ liệu mối quan hệ!", "capNhatThanhCongDuLieuViTriChanThuong": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu vị trí chấn thương!", "themMoiThanhCongDuLieuViTriChanThuong": "Thêm mới thành công dữ liệu vị trí chấn thương!", "vanBang": "<PERSON><PERSON>n bằng", "capNhatThanhCongDuLieuVBChuyenMon": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu VB chuyên môn!", "themMoiThanhCongDuLieuVBChuyenMon": "Thêm mới thành công dữ liệu VB chuyên môn!", "tuongTacThuocXetNghiemCsc": "<PERSON><PERSON><PERSON><PERSON> tác thuốc xét nghiệm chỉ số con", "tuongTacThuocXetNghiem": "<PERSON><PERSON><PERSON><PERSON> tác thuốc xét ng<PERSON>m", "capNhatThanhCongDuLieuThoiGianCapCuu": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu thời gian cấp cứu!", "themMoiThanhCongDuLieuThoiGianCapCuu": "Thêm mới thành công dữ liệu thời gian cấp cứu!", "themMoiThanhCongDuLieuThietLapTichDiem": "Thêm mới thành công dữ liệu thiết lập tích điểm!", "themMoiThanhCongDuLieuThietLapLuuTruBenhAn": "Thêm mới thành công dữ liệu thiết lập lưu trữ bệnh án!", "capNhatThanhCongDuLieuTheBaoHiem": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu thẻ bảo hiểm!", "themMoiThanhCongDuLieuTheBaoHiem": "Thêm mới thành công dữ liệu thẻ bảo hiểm!", "capNhatThanhCongDuLieuQuanHam": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu quân hàm!", "themMoiThanhCongDuLieuQuanHam": "Thêm mới thành công dữ liệu quân hàm!", "capNhatThanhCongDuLieuPhuongPhapVoCam": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu phương pháp vô cảm!", "themMoiThanhCongDuLieuPhuongPhapVoCam": "Thêm mới thành công dữ liệu phương pháp vô cảm!", "capNhatThanhCongDuLieuNguoiDaiDien": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu người đại diện!", "themMoiThanhCongDuLieuNguoiDaiDien": "Thêm mới thành công dữ liệu người đại diện!", "phatThanhCong": "<PERSON><PERSON><PERSON> thành công", "huyPhatThanhCong": "<PERSON><PERSON><PERSON> ph<PERSON>t thành công", "duyetTraThanhCong": "<PERSON><PERSON><PERSON><PERSON> trả thành công", "huyDuyetTraThanhCong": "<PERSON><PERSON><PERSON> du<PERSON> trả thành công", "capNhatThanhCongDuLieuNhomDvCap2": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu nhóm dv cấp 2!", "themMoiThanhCongDuLieuNhomDvCap2": "Thêm mới thành công dữ liệu nhóm dv cấp 2!", "soLuongTonToiThieu": "<PERSON><PERSON> lư<PERSON>ng tồn tối thiểu", "nhapSoLuongTonToiThieu": "<PERSON><PERSON><PERSON><PERSON> số lư<PERSON> tồn tối thiểu", "thietLapQuyTacChonGiuong": "<PERSON><PERSON><PERSON><PERSON> lập quy tắc chọn gi<PERSON>", "securityKey": "security<PERSON>ey", "vuiLongNhap{{field}}": "<PERSON><PERSON> lòng nhập {{field}}", "vuiLongNhap{{field}}KhongQua{{num}}KyTu": "<PERSON>ui lòng nhập {{field}} không quá {{num}} ký tự", "danhMucViKhuan": "<PERSON><PERSON><PERSON>", "viKhuan": "<PERSON><PERSON>", "mucDoPheDuyetThuocKhangSinh": "<PERSON><PERSON><PERSON> phê duyệt thu<PERSON><PERSON> kh<PERSON>g sinh", "doiTuongKcb": "<PERSON><PERSON><PERSON> KCB", "danhMucDoiTuongKcb": "<PERSON><PERSON> <PERSON> KCB", "phanLoaiChiTietKcb": "<PERSON>ân loại chi tiết KCB", "nhomDoiTuongKcb": "<PERSON>hóm đố<PERSON> t<PERSON>ng KCB", "chonNhomDoiTuongKcb": "<PERSON><PERSON><PERSON> nh<PERSON>m đối tư<PERSON>ng KCB", "chonPhanLoaiChiTietKcb": "<PERSON><PERSON><PERSON> phân loại chi tiết KCB", "tenDoiTuongKcb": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> t<PERSON> KCB", "maDoiTuongKcb": "Mã đối t<PERSON>ng KCB", "vuiLongNhapMaDoiTuongKcb": "<PERSON><PERSON> lòng nhập mã đối tượng KCB", "vuiLongNhapTenDoiTuongKcb": "<PERSON><PERSON> lòng nhập tên đối tượng KCB", "xoaTuongTacThuocXetNghiemConfirm": "Bạn đang xóa tương tác thuốc xét nghiệm{{content}}. <br /> Bạn có chắc chắn muốn tiếp tục không?", "hangBangLaiXe": "Hạng bằng lái xe", "danhMucHangBangLaiXe": "<PERSON><PERSON> m<PERSON>c <PERSON>ng bằng lái xe", "yeuCauNhapSoLanNgayLonHon0": "<PERSON><PERSON><PERSON> c<PERSON>u nhập số lần/ ngày > 0", "yeuCauNhapSoLuongLanLonHon0": "<PERSON><PERSON><PERSON> c<PERSON>u nhập số lư<PERSON>/ lần > 0", "chonNhomDvCap2": "Chọn nhóm DV cấp 2", "maSinhPham": "<PERSON><PERSON> sinh phẩm", "maHieuSinhPham": "<PERSON><PERSON> hiệu sinh phẩm", "taiKhoanDoanhThu": "<PERSON><PERSON><PERSON>n do<PERSON>h thu", "taiKhoanChiPhi": "<PERSON><PERSON><PERSON> k<PERSON>n chi phí", "taiKhoanKho": "<PERSON><PERSON><PERSON> kho", "vuiLongNhapTaiKhoanDoanhThu": "<PERSON><PERSON> lòng nhập tài k<PERSON>n doanh thu", "vuiLongNhapTaiKhoanChiPhi": "<PERSON><PERSON> lòng nhập tài k<PERSON>n chi phí", "vuiLongNhapTaiKhoanKho": "<PERSON><PERSON> lòng nh<PERSON>p tài k<PERSON> kho", "danhMucDonViSph": "<PERSON><PERSON>ơn vị SPH", "danhMucTatKhucXa": "<PERSON><PERSON> m<PERSON> kh<PERSON>c x<PERSON>", "tatKhucXa": "<PERSON><PERSON><PERSON> kh<PERSON>c xạ", "maTatKhucXa": "Mã tật khúc xạ", "tenTatKhucXa": "<PERSON><PERSON><PERSON> tật kh<PERSON>c xạ", "donViSph": "đơn vị SPH", "maDonViSph": "Mã đơn vị SPH", "tenDonViSph": "<PERSON><PERSON><PERSON> vị SPH", "donViAxis": "đơn vị Axis", "danhMucDonViAxis": "<PERSON><PERSON>ơn vị <PERSON>", "sph": "SPH", "axis": "Axis", "donVi{{donVi}}": "Đơn vị {{donVi}}", "maDonViAxis": "Mã đơn vị Axis", "tenDonViAxis": "<PERSON><PERSON><PERSON> đơn vị Axis", "maDonViCyl": "Mã đơn vị CYL", "tenDonViCyl": "Tên <PERSON>n vị CYL", "donViCyl": "đơn vị CYL", "danhMucDonViCyl": "<PERSON><PERSON> Đơn vị CYL", "danhMucNhanAp": "<PERSON><PERSON>", "nhanAp": "<PERSON><PERSON><PERSON><PERSON>", "maNhanAp": "<PERSON><PERSON> nhãn <PERSON>", "tenNhanAp": "<PERSON><PERSON><PERSON> n<PERSON>", "IDQuyetDinhThau": "<PERSON> quyết đ<PERSON><PERSON> thầu", "khaiBaoPhuCapPttt": "<PERSON><PERSON> b<PERSON>o phụ cấp PTTT, CĐHA, XN", "khaiBaoPhuCapPtttChiTiet": "<PERSON><PERSON> b<PERSON><PERSON> phụ cấp <PERSON>, CĐHA, XN chi tiết", "thongTinKhaiBaoPhuCap": "<PERSON>h<PERSON>ng tin khai báo phụ cấp", "mucPhuCap": "<PERSON><PERSON><PERSON> p<PERSON> cấp", "phanTramHuong": "Phần trăm hưởng", "danhMucViTriChamCong": "<PERSON><PERSON> m<PERSON>c vị trí chấm công", "viTriChamCong": "<PERSON><PERSON> trí chấm công", "danhMucKhaiBaoPhuCapPttt": "<PERSON><PERSON> mụ<PERSON> khai b<PERSON>o phụ cấp PTTT, CĐHA, XN", "vuiLongNhapViTriChamCong": "<PERSON><PERSON> lòng nhập vị trí chấm công", "timTenGoi": "<PERSON><PERSON><PERSON> tên gói", "danhMucTuDienYKhoa": "<PERSON><PERSON> m<PERSON><PERSON>ừ điển y khoa", "maTuDienYKhoa": "<PERSON>ã từ điển y khoa", "tenTuDienYKhoa": "Tên từ điển y khoa (<PERSON><PERSON><PERSON> b<PERSON><PERSON>, tên thuố<PERSON>, tên ho<PERSON>t chất,...)", "tuDienYKhoa": "từ điển y khoa", "vuiLongTaiLenMauTuDienYKhoa": "<PERSON>ui lòng tải lên mẫu từ điển y khoa!", "chiChoPhepTaiLenFileCoDinhDangPDF": "Chỉ cho phép tải lên file có định dạng PDF!", "chongChiDinh": "Chống chỉ định", "chongThucHien": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> hi<PERSON>n", "dieuKienHanhNghe": "<PERSON><PERSON><PERSON><PERSON> kiện hành nghề", "lyDoChiDinh": "<PERSON><PERSON> do chỉ định", "nhapDuLieuDanhMucNhanVien": "<PERSON><PERSON><PERSON><PERSON> dữ liệu <PERSON> mục nhân viên", "xuatDuLieuDanhMucNhanVien": "<PERSON><PERSON><PERSON> dữ liệu <PERSON> mục nhân viên", "nhapDuLieuTTKhoaPhong": "<PERSON><PERSON><PERSON><PERSON> dữ liệu TT Khoa Phòng", "xuatDuLieuTTKhoaPhong": "<PERSON><PERSON>t dữ liệu TT Khoa Phòng", "nhapDuLieuDanhMucThuoc": "<PERSON><PERSON><PERSON><PERSON> dữ liệu <PERSON> mục thuốc", "xuatDuLieuDanhMucThuoc": "<PERSON><PERSON><PERSON> dữ liệu <PERSON> mục thuốc", "nhapDuLieuChePhamMau": "<PERSON><PERSON><PERSON><PERSON> dữ liệu <PERSON> phẩm máu", "xuatDuLieuChePhamMau": "<PERSON><PERSON><PERSON> dữ liệu <PERSON> phẩm máu", "trungTenThuongMai": "Trùng tên thương mại", "nhapDuLieuBoChiDinh": "<PERSON><PERSON><PERSON><PERSON> dữ liệu Bộ chỉ định", "xuatDuLieuBoChiDinh": "<PERSON><PERSON>t dữ liệu Bộ chỉ định", "nhapDuLieuTitle": "<PERSON><PERSON><PERSON><PERSON> dữ liệu {{ title }}", "xuatDuLieuTitle": "<PERSON><PERSON><PERSON> dữ liệu {{ title }}", "trichCNC": "<PERSON><PERSON><PERSON><PERSON>", "danhMucKetQuaChanDoanLaoKhangThuoc": "<PERSON><PERSON> m<PERSON><PERSON>t quả chẩn đoán <PERSON> kháng thuốc", "ketQuaChanDoanLaoKhangThuoc": "<PERSON><PERSON><PERSON> qu<PERSON> chẩn đo<PERSON> kháng thuốc", "ketQuaChanDoan": "<PERSON><PERSON><PERSON> qu<PERSON> chẩn đo<PERSON>", "danhMucPhuongPhapChanDoan": "<PERSON><PERSON> ph<PERSON>p chẩn đoán", "phuongPhapChanDoan": "Phư<PERSON><PERSON> ph<PERSON>p chẩn đo<PERSON>", "ppChanDoan": "PP chẩn đoán", "vuiLongNhapMaPP": "<PERSON><PERSON> lòng nh<PERSON>p mã <PERSON>", "vuiLongNhapTenPP": "<PERSON><PERSON> lòng nh<PERSON>p tên <PERSON>", "thongTinPPChanDoan": "Thông tin PP chẩn đoán", "tenKetQua": "<PERSON><PERSON><PERSON> kê<PERSON>t quả", "tachDong": "<PERSON><PERSON><PERSON> dòng", "khongTachDong": "<PERSON><PERSON><PERSON><PERSON> tách dòng", "danhMucPhanLoaiTheoTienSuDieuTri": "<PERSON><PERSON> <PERSON><PERSON> loại theo tiền sử điều trị", "phanLoaiTheoTienSuDieuTri": "<PERSON><PERSON> loại theo tiền sử điều trị", "nguonKhac": "<PERSON><PERSON><PERSON><PERSON>", "danhMucNguonKhac": "<PERSON><PERSON>", "themThanhCongDuLieuNguonKhac": "<PERSON><PERSON><PERSON><PERSON> thành công dữ liệu nguồn khác", "capNhatThanhCongDuLieuNguonKhac": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu nguồn khá", "xoaThanhCongDuLieuNguonKhac": "<PERSON><PERSON><PERSON> thành công dữ liệu nguồn khác", "vuiLongNhapMaNguonKhacKhongQua20KyTu": "<PERSON>ui lòng nhập mã nguồn khác không quá 20 ký tự!", "vuiLongNhapTenNguonKhacKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên nguồn khác không quá 1000 ký tự!", "loaiNguonKhac": "Loại nguồn kh<PERSON>c", "loaiTienChiTra": "<PERSON><PERSON><PERSON> tiền chi trả", "tinhTheoDonGiaKhongBH": "<PERSON><PERSON><PERSON> theo đơn gi<PERSON> không BH", "dinhMucThuocVTYT": "<PERSON><PERSON><PERSON> mức thuốc VTYT", "danhMucDinhMucThuocVTYT": "<PERSON><PERSON> mục <PERSON> mức thuốc/VTYT", "dichVuTrongDinhMuc": "<PERSON><PERSON><PERSON> vụ trong đ<PERSON><PERSON> mức", "dinhMuc": "<PERSON><PERSON><PERSON> m<PERSON>c", "nhapDinhMuc": "<PERSON><PERSON><PERSON><PERSON> mức", "vuiLongNhapDinhMucNgoaiTruLonHon0": "<PERSON><PERSON> lòng nh<PERSON><PERSON> đ<PERSON><PERSON> mức ngoại trú > 0", "vuiLongNhapDinhMucNoiTruLonHon0": "<PERSON><PERSON> lòng nh<PERSON><PERSON> đ<PERSON><PERSON> mức nội trú > 0", "dinhMucNgoaiTru": "<PERSON><PERSON><PERSON> mức ngoại trú", "nhapDinhMucNgoaiTru": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> mức ngoại trú", "dinhMucNoiTru": "<PERSON><PERSON><PERSON> mức nội trú", "nhapDinhMucNoiTru": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> mức nội trú", "vuiLongNhapMaDinhMuc": "<PERSON><PERSON> lòng nhập mã định mức", "soLuongDinhMuc": "S<PERSON> lư<PERSON><PERSON> đ<PERSON><PERSON> mức", "taiKhoanLienThongDTDT": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>n liên thông đơn thuốc điện tử", "matKhauLienThongDTDT": "<PERSON><PERSON><PERSON> kh<PERSON>u liên thông đơn thuốc điện tử", "xaHoiHoa": "Xã hội hóa", "danhMucLuongGia": "<PERSON><PERSON> giá", "luongGia": "Lượng giá", "chiTietLuongGia": "<PERSON> tiết lư<PERSON>ng giá", "thongTinLuongGia": "Thông tin lượng giá", "danhMucVanDeLienQuanDenThuoc": "<PERSON><PERSON> mục Vấn đề liên quan đến thuốc", "vanDeChung": {"title": "<PERSON><PERSON><PERSON> đ<PERSON> chung", "ma": "<PERSON><PERSON> Vấn đề liên quan chung", "ten": "<PERSON><PERSON><PERSON>n đề liên quan chung", "loai": "<PERSON><PERSON><PERSON>", "nhapMa": "<PERSON><PERSON><PERSON><PERSON> mã vấn đề liên quan chung", "nhapMaKhongQua20KyTu": "<PERSON>ui lòng nhập mã vấn đề liên quan chung không quá 20 ký tự!", "nhapTen": "<PERSON><PERSON><PERSON><PERSON> tên vấn đề liên quan chung", "nhapTenKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên vấn đề liên quan chung không quá 1000 ký tự!", "chonLoai": "<PERSON><PERSON><PERSON>", "timVanDeChung": "<PERSON><PERSON><PERSON> vấn đề chung", "tatCaVanDeChung": "<PERSON><PERSON><PERSON> cả vấn đề chung", "vuiLongChonLoai": "<PERSON><PERSON> lòng chọn lo<PERSON>i"}, "vanDeCuThe": {"title": "<PERSON><PERSON>n đề cụ thể", "ma": "<PERSON>ã vấn đề cụ thể", "ten": "<PERSON>ên vấn đề cụ thể", "loai": "<PERSON><PERSON><PERSON>", "tenVanDeChung": "<PERSON><PERSON><PERSON> vấn đề chung", "vuiLongNhapMa": "<PERSON><PERSON> lòng nhập mã vấn đề cụ thể", "nhapMa": "<PERSON><PERSON><PERSON><PERSON> mã vấn đề cụ thể", "nhapMaKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã vấn đề cụ thể không quá 20 ký tự!", "nhapTen": "<PERSON><PERSON><PERSON><PERSON> tên vấn đề cụ thể", "nhapTenKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập tên vấn đề cụ thể không quá 1000 ký tự!", "chonVanDeChung": "<PERSON><PERSON><PERSON> vấn đề chung", "vuiLongNhapChonQuocGia": "<PERSON><PERSON> lòng nhập chọn vấn đề chung", "timVanDeCuThe": "<PERSON><PERSON><PERSON> vấn đề cụ thể"}, "thoiGianDuKienCoKetQua": "<PERSON>hời gian dự kiến có K<PERSON>", "nhapThoiGianDuKienCoKetQua": "<PERSON><PERSON><PERSON><PERSON> thời gian dự kiến có kết quả", "guiVitimes": "<PERSON><PERSON><PERSON>", "loaiXnVitimes": "<PERSON><PERSON><PERSON> x<PERSON>t nghi<PERSON>m Vitimes", "chonLoaiXnVitimes": "<PERSON><PERSON><PERSON> lo<PERSON>i xét nghiệm Vitimes", "vuiLongChonLoaiXnVitimes": "<PERSON><PERSON> lòng chọn lo<PERSON>i xét nghiệm Vitimes", "maGuiVitimes": "Mã gửi Vitimes", "dayDmThuocSangVitimes": "<PERSON><PERSON><PERSON> danh mục thu<PERSON> sang Vitimes", "dayThuocSangVitimes": "<PERSON><PERSON><PERSON> sang <PERSON>iti<PERSON>", "dayDmXnSangVitimes": "<PERSON><PERSON><PERSON> danh mục x<PERSON><PERSON> sang Vitimes", "dayXnSangVitimes": "<PERSON><PERSON><PERSON><PERSON> sang Viti<PERSON>", "dayDmThuocSangVitimesThanhCong": "<PERSON><PERSON><PERSON> danh mục thu<PERSON><PERSON> sang Vitimes thành công!", "dayThuocSangVitimesThanhCong": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> sang <PERSON>iti<PERSON> thành công!", "dayDmXnSangVitimesThanhCong": "<PERSON><PERSON><PERSON> danh mục xét nghi<PERSON> sang Viti<PERSON> thành công!", "dayXnSangVitimesThanhCong": "<PERSON><PERSON><PERSON> x<PERSON><PERSON> sang Viti<PERSON> thành công!", "XNSaoBenhAn": "<PERSON><PERSON><PERSON> nghi<PERSON>m sao b<PERSON><PERSON>n", "danhMucXNSaoBenhAn": "<PERSON><PERSON> nghiệm sao b<PERSON>nh <PERSON>n", "maXNSaoBA": "<PERSON><PERSON> xét nghiệm sao b<PERSON>nh <PERSON>n", "tenXNSaoBA": "<PERSON><PERSON><PERSON> xét nghiệm sao b<PERSON>nh <PERSON>n", "maXNGuiLIS": "Mã xét nghiệm gửi LIS", "nhomKetQua": "<PERSON><PERSON><PERSON><PERSON> kết quả", "layChiSoCon": "<PERSON><PERSON>y chỉ số con", "khongLayChiSoCon": "<PERSON><PERSON><PERSON><PERSON> lấy chỉ số con", "sttXN": "STT x<PERSON>t ng<PERSON>", "dinhMucThuoc": "<PERSON><PERSON><PERSON> mứ<PERSON> thu<PERSON>c", "chonDinhMucThuoc": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> mức thuốc", "dinhMucVtyt": "<PERSON><PERSON><PERSON> mức VTYT", "chonDinhMucVtyt": "<PERSON><PERSON><PERSON> đ<PERSON> mức VTYT", "dieuDuongKeHoach": "<PERSON><PERSON><PERSON>u dưỡng kế hoạch", "hoLyKeHoach": "<PERSON><PERSON> lý / y công kế hoạch", "kyThuatVienKeHoach": "KTV kế hoạch", "doiTuongTuoi": "<PERSON><PERSON><PERSON> tư<PERSON> tuổi", "khongKiemTraTuoi": "<PERSON><PERSON><PERSON><PERSON> kiểm tra tuổi", "mienPhiGiamDocDuyet": "<PERSON><PERSON><PERSON> phí gi<PERSON>m đố<PERSON> ", "keTuGio": "<PERSON><PERSON> từ giờ", "keDenGio": "<PERSON><PERSON> đến giờ", "vuiLongChonLoaiBuaAn": "<PERSON><PERSON> lòng chọn loại bữa ăn", "kieuKy": "<PERSON><PERSON><PERSON>", "vuiLongChonLoaiKy": "<PERSON><PERSON> lòng chọn lo<PERSON>i ký", "guiISC": "Gửi ISC", "maNgoiThai": "<PERSON>ã ngôi thai", "vuiLongNhapMaNgoiThai": "<PERSON><PERSON> lòng nhập mã ngôi thai", "nhapMaNgoiThai": "<PERSON><PERSON><PERSON><PERSON> mã ngôi thai", "tenNgoiThai": "<PERSON><PERSON><PERSON> ngôi thai", "vuiLongNhapTenNgoiThai": "<PERSON><PERSON> lòng nhập tên ngôi thai", "nhapTenNgoiThai": "<PERSON><PERSON><PERSON><PERSON> tên ngôi thai", "ngoiThai": "<PERSON><PERSON><PERSON> thai", "danhMucNgoiThai": "<PERSON><PERSON><PERSON>ai", "cachThucDe": "<PERSON><PERSON><PERSON> thức đẻ", "danhMucCachThucDe": "<PERSON><PERSON> m<PERSON> thức đẻ", "maCachThucDe": "<PERSON><PERSON> cách thức đẻ", "vuiLongNhapMaCachThucDe": "<PERSON><PERSON> lòng nhập mã cách thức đẻ", "nhapMaCachThucDe": "<PERSON><PERSON><PERSON><PERSON> mã cách thức đẻ", "tenCachThucDe": "<PERSON><PERSON><PERSON> c<PERSON>ch thức đẻ", "vuiLongNhapTenCachThucDe": "<PERSON><PERSON> lòng nhập tên cách thức đẻ", "nhapTenCachThucDe": "<PERSON><PERSON><PERSON><PERSON> tên cách thức đẻ", "danhMucChePhamDinhDuong": {"title": "<PERSON><PERSON> m<PERSON><PERSON> phẩm dinh dưỡng", "maCpdd": "Mã CPDD", "vuiLongNhapMaCpdd": "<PERSON><PERSON> lòng nhập Mã chế phẩm dinh dưỡng", "vuiLongNhapMaCpddKhongQua20KyTu": "<PERSON><PERSON> lòng nhập Mã chế phẩm dinh dưỡng không quá 20 ký tự", "tenCpdd": "Tên CPDD", "vuiLongNhapTenCpdd": "<PERSON><PERSON> lòng nhập Tên chế phẩm dinh dưỡng", "vuiLongNhapTenCpddKhongQua500KyTu": "<PERSON><PERSON> lòng nhập Tên chế phẩm dinh dưỡng không quá 500 ký tự", "cachDongGoi": "<PERSON><PERSON><PERSON>", "vuiLongNhapQuyCachKhongQua500KyTu": "<PERSON><PERSON> lòng nhập quy cách không quá 500 ký tự", "vuiLongNhapCachDongGoiKhongQua500KyTu": "<PERSON><PERSON> lòng nhập cách đóng gói không quá 500 ký tự"}, "bienPhapCamMau": "<PERSON><PERSON>ệ<PERSON> ph<PERSON><PERSON> cầm máu", "danhMucBienPhapCamMau": "<PERSON><PERSON> m<PERSON><PERSON> pháp c<PERSON>m máu", "maBienPhapCamMau": "<PERSON><PERSON> bi<PERSON>n ph<PERSON>p cầm máu", "vuiLongNhapMaBienPhapCamMau": "<PERSON><PERSON> lòng nhập mã biện pháp cầm máu", "nhapMaBienPhapCamMau": "<PERSON><PERSON><PERSON><PERSON> mã biện pháp cầm máu", "tenBienPhapCamMau": "<PERSON><PERSON><PERSON> bi<PERSON>n ph<PERSON>p cầm máu", "vuiLongNhapTenBienPhapCamMau": "<PERSON><PERSON> lòng nhập tên biện pháp cầm máu", "nhapTenBienPhapCamMau": "<PERSON><PERSON><PERSON><PERSON> tên biện pháp cầm máu", "danhMucDiTatBamSinh": {"title": "<PERSON><PERSON> m<PERSON><PERSON> tật b<PERSON><PERSON> sinh", "name": "<PERSON><PERSON> tật b<PERSON><PERSON> sinh", "maDiTatBamSinh": "<PERSON><PERSON> dị tật b<PERSON><PERSON> sinh", "vuiLongNhapMaDiTatBamSinh": "<PERSON><PERSON> lòng nhập mã dị tật bẩm sinh", "vuiLongNhapMaDiTatBamSinhKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã dị tật bẩm sinh không quá 20 ký tự", "tenDiTatBamSinh": "<PERSON><PERSON><PERSON> dị tật b<PERSON><PERSON> sinh", "vuiLongNhapTenDiTatBamSinh": "<PERSON><PERSON> lòng nhập tên dị tật b<PERSON>m sinh"}, "vuiLongNhapGiaTriKhongQua50KyTu": "<PERSON><PERSON> lòng nhập giá trị không quá 50 ký tự!", "vuiLongChonLoaiKieuKy": "<PERSON><PERSON> lòng chọn loại kiểu ký", "themNhomDichVu": "<PERSON><PERSON><PERSON><PERSON> nhóm dịch vụ", "chonViTriCA": "Chọn vị trí CA", "thoiGianCanhBao": "<PERSON><PERSON><PERSON><PERSON> gian cảnh b<PERSON>o", "nhapThoiGianCanhBao": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i gian cảnh b<PERSON>o", "vuiLongNhapThoiGianCanhBao": "<PERSON><PERSON> lòng nhập thời gian cảnh báo", "phamViCanhBao": "Phạm vi cảnh báo", "chonPhamViCanhBao": "<PERSON><PERSON><PERSON> phạm vi cảnh báo", "vuiLongChonPhamViCanhBao": "<PERSON>ui lòng chọn phạm vi cảnh báo", "soNgayCachTiemGiuaCacMui": "<PERSON><PERSON> ng<PERSON>y cách tiêm gi<PERSON><PERSON> các mũi", "canhBaoSuDungThuoc": "<PERSON><PERSON><PERSON> báo sử dụng thuốc", "tuoiThoCuaThuoc": "<PERSON><PERSON><PERSON> thọ của thuốc", "noiDungCanhBaoSuDungThuoc": "<PERSON><PERSON><PERSON> dung cảnh báo sử dụng thuốc", "tenHienThiManHinhPhauThuatThuThuat": "Tên hiển thị màn hình phẫu thuật - thủ thuật", "dvNhomDvApDung": "DV, nhóm DV áp dụng", "chonNhomDichVuCap1ApDungGiamGia": "Chọn nhóm dịch vụ cấp 1 áp dụng giảm giá", "chonNhomDichVuCap2ApDungGiamGia": "Chọn nhóm dịch vụ cấp 2 áp dụng giảm giá", "timTenNhomDichVuCap1": "<PERSON><PERSON><PERSON> tên nh<PERSON> dịch vụ cấp 1", "timTenNhomDichVuCap2": "<PERSON><PERSON><PERSON> tên n<PERSON> dị<PERSON> vụ cấp 2", "nhapSoLien": "<PERSON><PERSON><PERSON><PERSON> liên", "timLoaiDonThuoc": "<PERSON><PERSON><PERSON> lo<PERSON> đơn thuốc", "timSoLienIn": "<PERSON><PERSON><PERSON> s<PERSON> liên in", "timDoiTuongNB": "<PERSON><PERSON><PERSON> đối t<PERSON>ng NB", "guiTckt": "Gửi TCKT", "thongTinThau": "<PERSON><PERSON><PERSON><PERSON> tin thầu", "nhapThongTinThau": "<PERSON><PERSON><PERSON><PERSON> thông tin thầu", "tinhGiaTheoBoChiDinh": "<PERSON><PERSON><PERSON> giá theo bộ chỉ định", "taiLieuThamKhao": "<PERSON><PERSON><PERSON> li<PERSON>u tham kh<PERSON>o", "mucHuongThemVoiHoNgheoCanNgheo": "<PERSON><PERSON><PERSON> hưởng thêm với hộ nghèo/cận nghèo", "canhBaoTuoi": "<PERSON><PERSON><PERSON> b<PERSON>o tu<PERSON>i", "danhSachCanhBaoTuoi": "<PERSON><PERSON> s<PERSON>ch cảnh b<PERSON>o tu<PERSON>i", "nhapDenTuoi": "<PERSON><PERSON><PERSON><PERSON> đến tuổi", "nhapTuTuoi": "<PERSON><PERSON><PERSON><PERSON> từ tuổi", "nhaNhapKhau": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> kh<PERSON>", "nhapNhaNhapKhau": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON> nhập kh<PERSON>u", "dieuKienBaoQuan": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>n b<PERSON><PERSON> qu<PERSON>n", "nhapDieuKienBaoQuan": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>u kiện b<PERSON><PERSON> quản", "chatLuongCamQuan": "<PERSON><PERSON><PERSON> cảm quan", "loaiDoiTuongMacDinh": "<PERSON><PERSON>i đối tượng mặc định", "vuiLongChonLoaiDoiTuongMacDinh": "<PERSON><PERSON> lòng chọn loại đối tượng mặc định", "loaiDoiTuongGioiHan": "<PERSON><PERSON><PERSON> đối tượng giới hạn", "vuiLongChonLoaiDoiTuongGioiHan": "<PERSON><PERSON> lòng chọn loại đối tượng giới hạn", "duocSuaGia": "<PERSON><PERSON><PERSON><PERSON> sửa giá", "phanLoaiBenhVien": "<PERSON><PERSON> lo<PERSON> b<PERSON><PERSON> viện", "vuiLongChonTenChiSoSong": "<PERSON><PERSON> lòng chọn tên chỉ số sống", "vuiLongNhapTuTuoi": "<PERSON><PERSON> lòng nhập từ tuối", "vuiLongNhapDenTuoi": "<PERSON><PERSON> lòng nhập đến tuối", "vuiLongNhapGiaTriToiThieu": "<PERSON><PERSON> lòng nhập giá trị tối thiểu", "vuiLongNhapGiaTriToiDa": "<PERSON><PERSON> lòng nhập giá trị tối đa", "vuiLongNhapTuTuoiNhoHonDenTuoi": "<PERSON><PERSON> lòng nhập từ tuổi nhỏ hơn đến tuổi!", "vuiLongNhapDenTuoiLonHonTuTuoi": "<PERSON><PERSON> lòng nhập đến tuổi lớn hơn từ tuổi", "vuiLongNhapGiaTriToiThieuNhoHonGiaTriToiDa": "<PERSON><PERSON> lòng nhập giá trị tối thiểu nhỏ hơn giá trị tối đa", "vuiLongNhapGiaTriToiDaLonHonGiaTriToiThieu": "<PERSON><PERSON> lòng nhập giá trị tối đa lớn hơn giá trị tối thiểu", "capNhatThanhCongDuLieuThietLapGiaTriCSS": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu thiết lập giá trị chỉ số sống", "vuiLongNhapTenHienThiManHinhPhauThuatThuThuat": "<PERSON><PERSON> lòng nhập tên hiển thị màn hình phẫu thuật - thủ thuật", "yeuCauLieuDung": "<PERSON><PERSON><PERSON> c<PERSON>u li<PERSON>u dùng", "vuiLongNhapTuTuoiHoacDenTuoi": "<PERSON><PERSON> lòng nhập từ tuổi hoặc đến tuổi!", "yeuCauPhaChe": "<PERSON><PERSON><PERSON> c<PERSON>u pha chế", "tenGoiSo": "<PERSON><PERSON><PERSON> s<PERSON>", "vuiLongNhapTenGoiSo": "<PERSON><PERSON> lòng nhập tên g<PERSON>i số", "nhapTenGoiSo": "<PERSON><PERSON><PERSON><PERSON> tên g<PERSON> s<PERSON>", "kieuGoiSo": "<PERSON><PERSON><PERSON> s<PERSON>", "chonKieuGoiSo": "<PERSON><PERSON><PERSON> ki<PERSON>u gọi số", "apDungTt20": "Á<PERSON> dụng TT20", "sttTrongTt20": "STT trong TT20", "ghiChuHoatChat": "<PERSON><PERSON> chú ho<PERSON>t chất", "ven": "Ven", "nhapGhiChuHoatChat": "<PERSON><PERSON><PERSON><PERSON> ghi chú hoạt chất", "nhapVen": "<PERSON><PERSON><PERSON><PERSON> ven", "soLuongGioiHan1Ngay": "SL giới hạn/ ngày", "nhapSoLuongGioiHan1Ngay": "Nhập SL giới hạn/ ngày", "yeuCauLapBenhAnDaiHan": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> lập b<PERSON><PERSON>n dài hạn", "hamLuongDdd": "<PERSON><PERSON><PERSON> DDD", "whoDdd": "DDD WHO", "donViDddWho": "Đơn vị DDD WHO", "maVaTu": "<PERSON><PERSON> vật tư", "yeuCauTheoDoi": "<PERSON><PERSON><PERSON> c<PERSON>u theo dõi", "chonLoaiLamTron": "<PERSON><PERSON><PERSON> loại làm tròn", "chungLoaiPTTT": "Chủng loại PTTT", "chonChungLoaiPTTT": "Chọn chủng loại PTTT", "vuiLongChonChungLoaiPTTT": "<PERSON><PERSON> lòng chọn chủng loại PTTT", "nguoiThucHienMacDinh": "<PERSON><PERSON><PERSON><PERSON> thực hiện mặc định", "chonNguoiThucHienMacDinh": "<PERSON><PERSON><PERSON> người thực hiện mặc định", "vuiLongChonNguoiThucHienMacDinh": "<PERSON><PERSON> lòng chọn người thực hiện mặc định", "khoaTrucThuoc": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON>", "timKhoaTrucThuoc": "<PERSON><PERSON><PERSON> khoa tr<PERSON><PERSON> th<PERSON>c", "chonKhoaTrucThuoc": "<PERSON><PERSON><PERSON> khoa tr<PERSON><PERSON> thu<PERSON>c", "khongLenPhieuThu": "<PERSON><PERSON><PERSON><PERSON> lên phi<PERSON>u thu", "chonKhongLenPhieuThu": "<PERSON><PERSON><PERSON> không lên phiếu thu", "chinhSachHoaHong": "<PERSON><PERSON><PERSON> s<PERSON>ch hoa hồng", "danhMucChinhSachHoaHong": "<PERSON><PERSON> s<PERSON>ch hoa hồng", "maChinhSach": "<PERSON><PERSON> ch<PERSON> s<PERSON>ch", "nhapMaChinhSach": "<PERSON><PERSON><PERSON><PERSON> mã ch<PERSON>h s<PERSON>ch", "vuiLongNhapMaChinhSach": "<PERSON><PERSON> lòng nhập mã ch<PERSON>h s<PERSON>ch", "nhapDoiTuongApDung": "<PERSON><PERSON><PERSON><PERSON> đối tư<PERSON> áp dụng", "vuiLongNhapDoiTuongApDung": "<PERSON><PERSON> lòng nhập đối tượng áp dụng", "ngayHieuLuc": "<PERSON><PERSON><PERSON>", "chonNgayHieuLuc": "<PERSON><PERSON><PERSON> ng<PERSON> hi<PERSON> l<PERSON>", "ngayHetHieuLuc": "<PERSON><PERSON><PERSON> h<PERSON> hi<PERSON> l<PERSON>", "chonNgayHetHieuLuc": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> hết hi<PERSON> l<PERSON>", "nhapKhuVuc": "<PERSON><PERSON><PERSON><PERSON> khu vực", "nhomDichVuHoaHong": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON> hoa hồng", "danhMucNhomDichVuHoaHong": "<PERSON><PERSON> dịch vụ hoa hồng", "doiTacHoaHong": "<PERSON><PERSON><PERSON> tác hoa hồng", "danhMucDoiTacHoaHong": "<PERSON><PERSON> m<PERSON> tác hoa hồng", "vuiLongChonDoiTac": "<PERSON><PERSON> lòng chọn đối tác", "chonDoiTac": "<PERSON><PERSON><PERSON> đ<PERSON> t<PERSON>c", "vuiLongChonChinhSachHoaHong": "<PERSON><PERSON> lòng chọn ch<PERSON>h sách hoa hồng", "chonChinhSachHoaHong": "<PERSON><PERSON><PERSON> ch<PERSON> s<PERSON>ch hoa hồng", "loaiNgayLamViec": "<PERSON><PERSON><PERSON> ng<PERSON>y làm vi<PERSON>c", "vuiLongChonLoaiNgayLamViec": "<PERSON><PERSON> lòng chọn lo<PERSON>i ngày làm việc", "chonloaiNgayLamViec": "<PERSON><PERSON><PERSON> lo<PERSON>i ngày làm vi<PERSON>c", "maNhomDvCapNumber": "<PERSON>ã nhóm dịch v<PERSON> cấp {{number}}", "tenNhomDvCapNumber": "<PERSON><PERSON><PERSON> n<PERSON> d<PERSON><PERSON> v<PERSON> cấp {{number}}", "nhomDvHoaHongCapNumber": "<PERSON><PERSON><PERSON><PERSON> dị<PERSON> v<PERSON> hoa hồng cấp {{number}}", "chonNhomDvHoaHongCapNumber": "<PERSON><PERSON><PERSON> nh<PERSON>m dịch vụ hoa hồng cấp {{number}}", "chonNhomDichVuHoaHong": "<PERSON><PERSON><PERSON> nh<PERSON>m dịch vụ hoa hồng", "ngoaiVienKhongThuTienGiaKBH": "<PERSON><PERSON><PERSON><PERSON> viện (<PERSON><PERSON><PERSON><PERSON> thu tiền giá KBH)", "coSoChiNhanh": "Cơ sở chi nhánh", "chonCoSoChiNhanh": "<PERSON><PERSON><PERSON> c<PERSON> sở chi nhánh", "dichVuTongHop": "<PERSON><PERSON><PERSON> v<PERSON> tổng hợp", "danhMucDichVuTongHop": "<PERSON><PERSON> v<PERSON> tổng hợp", "noiCongTac": "<PERSON><PERSON><PERSON> công tác", "timNoiCongTac": "<PERSON><PERSON><PERSON> n<PERSON> công tác", "nhapNoiCongTac": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> công tác", "danhMucDichVuHoaHong": "<PERSON><PERSON> v<PERSON>", "dichVuHoaHong": "<PERSON><PERSON><PERSON> v<PERSON> Hồ<PERSON>", "nguoiGioiThieuThanhToan": "<PERSON><PERSON><PERSON>i giới thiệu thanh toán", "nhapTenNganHang": "<PERSON><PERSON><PERSON><PERSON> tên ngân hàng", "fileHdsdDungThuoc": "File HDSD dùng thuốc", "slKhamToiDa": "Số lư<PERSON> khám tối đa", "nhapSlKhamToiDa": "<PERSON><PERSON><PERSON><PERSON> số lư<PERSON><PERSON> khám tối đa", "tienVatTu": "<PERSON><PERSON><PERSON><PERSON> vật tư", "tuSo": "<PERSON><PERSON> số", "nhapTuSo": "<PERSON><PERSON><PERSON><PERSON> từ số", "timTuSo": "<PERSON><PERSON><PERSON> từ số", "denSo": "<PERSON><PERSON><PERSON> s<PERSON>", "nhapDenSo": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "timDenSo": "<PERSON><PERSON><PERSON> đ<PERSON> số", "phanTramHHChiDinh": "<PERSON>ần trăm HH chỉ định", "nhapPhanTramHHChiDinh": "<PERSON><PERSON><PERSON><PERSON> phần trăm HH chỉ định", "phanTramHHThucHien": "<PERSON><PERSON><PERSON> tr<PERSON>m <PERSON> thực hi<PERSON>n", "nhapPhanTramHHThucHien": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>n tr<PERSON>m HH thực hiện", "phanTramHHGioiThieu": "Phần tr<PERSON>m HH giới thiệu", "nhapPhanTramHHGioiThieu": "<PERSON><PERSON><PERSON><PERSON> phần trăm HH giới thiệu", "tienHHChiDinh": "Tiền HH chỉ định", "nhapTienHHChiDinh": "<PERSON><PERSON><PERSON><PERSON> tiền HH chỉ định", "tienHHThucHien": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "nhapTienHHThucHien": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON>n <PERSON><PERSON> thực hiện", "tienHHGioiThieu": "Tiền HH giới thiệu", "nhapTienHHGioiThieu": "<PERSON><PERSON><PERSON><PERSON> tiền HH giới thiệu", "giamGiaToiDa": "<PERSON><PERSON><PERSON><PERSON> giá tối đa", "nhapGiamGiaToiDa": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON>m giá tối đa", "maTckt": "Mã TCKT", "nhapMaTckt": "Nhập mã TCKT", "timMaTckt": "Tìm mã TCKT", "tenTckt": "Tên TCKT", "nhapTenTckt": "<PERSON><PERSON><PERSON><PERSON> tên TCKT", "timTenTckt": "T<PERSON>m tên TCKT", "nhanVienKinhDoanh": "<PERSON><PERSON><PERSON> viên kinh doanh", "chonNhanVienKinhDoanh": "<PERSON><PERSON><PERSON> nhân viên kinh doanh", "duongDan": "Đường dẫn", "nhapDuongDan": "<PERSON><PERSON><PERSON><PERSON> đường dẫn", "taoDuongDan": "<PERSON><PERSON><PERSON> dư<PERSON> dẫn", "hopDong": "<PERSON><PERSON><PERSON>", "nhapHopDong": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> đồng", "nhomDvCapNumberHis": "<PERSON>h<PERSON><PERSON> d<PERSON><PERSON> v<PERSON> cấp {{number}} HIS", "vuiLongChonCoSoChiNhanh": "<PERSON>ui lòng chọn cơ sở chi nhánh!", "batBuocNhapChieuCaoNb": "Bắ<PERSON> b<PERSON><PERSON><PERSON> nh<PERSON>p chi<PERSON>u cao NB", "thuocLasa": "Thuốc LASA", "nhomLoaiDoiTuong": "<PERSON><PERSON><PERSON><PERSON> loại đối tư<PERSON>", "danhMucNhomLoaiDoiTuong": "<PERSON><PERSON> loại đối tượng", "maNhomLoaiDoiTuong": "<PERSON>ã nhóm loại đối tượng", "nhapMaNhomLoaiDoiTuong": "<PERSON><PERSON><PERSON><PERSON> mã <PERSON>óm loại đối tượng", "vuiLongNhapMaNhomLoaiDoiTuong": "<PERSON><PERSON> lòng nhập mã Nhóm loại đối tượng", "tenNhomLoaiDoiTuong": "<PERSON><PERSON><PERSON> nh<PERSON>m loại đối tượng", "nhapTenNhomLoaiDoiTuong": "<PERSON><PERSON><PERSON><PERSON> tên <PERSON> loại đối tượng", "vuiLongNhapTenNhomLoaiDoiTuong": "<PERSON><PERSON> lòng nhập tên <PERSON>m loại đối tượng", "chonNhomLoaiDoiTuong": "<PERSON><PERSON><PERSON> nhóm loại đối tượng", "vuiLongChonnhomLoaiDoiTuong": "<PERSON><PERSON> lòng chọn <PERSON> loại đối tượng", "huongDieuTri": "<PERSON><PERSON><PERSON><PERSON> điều trị", "ketQuaDieuTri": "<PERSON><PERSON><PERSON> quả điều trị", "hienThiTiepDon": "Hi<PERSON>n thị ở tiếp đón", "khaiBaoLoaiDoiTuongThoiGianLaySoVaSoLuongSttToiDa": "<PERSON><PERSON> b<PERSON><PERSON> loại đối tượ<PERSON>, thời gian lấy số và số lượng STT tối đa", "trongTuan": "<PERSON>rong tuần", "cuoiTuan": "<PERSON><PERSON><PERSON><PERSON> tu<PERSON>n", "tuGioDenGio": "Từ giờ - Đến giờ", "tenNhomDayCongBhyt": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> đ<PERSON>y cổng BHYT", "nhapTenHienThi": "<PERSON><PERSON><PERSON><PERSON> tên hiển thị", "sttTrenBangKe": "STT trên bảng kê", "thoiGianThucHienDichVuPhut": "<PERSON><PERSON><PERSON><PERSON> gian thực hiện d<PERSON> (phút)", "danhMucGoTat": "<PERSON><PERSON>", "goTat": "<PERSON><PERSON>", "maGoTat": "Mã gõ tắt", "nhapMaGoTat": "<PERSON>hậ<PERSON> mã gõ tắt", "vuiLongNhapMaGoTat": "<PERSON>ui lòng nhập mã gõ tắt!", "vuiLongNhapMaGoTatKhongQuaNumKyTu": "<PERSON>ui lòng nhập mã gõ tắt không quá {{ num }} ký tự!", "tenGoTat": "<PERSON><PERSON><PERSON> t<PERSON>", "nhapTenGoTat": "<PERSON><PERSON><PERSON><PERSON> tên g<PERSON> tắt", "vuiLongNhapTenGoTat": "<PERSON>ui lòng nhập tên gõ tắt!", "vuiLongNhapTenGoTatKhongQuaNumKyTu": "<PERSON>ui lòng nhập tên gõ tắt không quá {{ num }} ký tự!", "cumTuDayDu": "<PERSON><PERSON><PERSON> từ đầy đủ", "nhapCumTuDayDu": "<PERSON><PERSON><PERSON><PERSON> cụm từ đầy đủ", "vuiLongNhapCumTuDayDu": "<PERSON><PERSON> lòng nhập cụm từ đầy đủ!", "vuiLongNhapCumTuDayDuKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập cụm từ đầy đủ không quá {{ num }} ký tự!", "vuiLongNhapCachDungKhongQua255KyTu": "<PERSON>ui lòng nhập cách dùng không quá 255 ký tự!", "capBenhVien": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> viện", "soLuongToiDa": "<PERSON><PERSON> l<PERSON><PERSON> tối đa", "vuiLongNhapSoLuongToiDaKhongQua2KyTu": "<PERSON><PERSON> lòng nhập số lượng tối đa không quá 2 ký tự!", "muc1": "Mức 1", "muc2": "Mức 2", "muc3": "Mức 3", "tachDaiSoThuTuUuTien": "<PERSON><PERSON><PERSON> dải số thứ tự ưu tiên", "tienThucHien": "<PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON> hi<PERSON>n", "nhapTienThucHien": "<PERSON><PERSON><PERSON><PERSON> tiền thực hiện", "tienGioiThieu": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON>i thiệu", "nhapTienGioiThieu": "<PERSON><PERSON><PERSON><PERSON> tiền giới thiệu", "tienChiDinh": "Tiền chỉ định", "nhapTienChiDinh": "<PERSON><PERSON><PERSON><PERSON> tiền chỉ định", "hinhThucIn": "<PERSON><PERSON><PERSON> th<PERSON> in", "vuiLongChonHinhThucIn": "<PERSON><PERSON> lòng chọn hình thức in", "chonHinhThucIn": "<PERSON><PERSON><PERSON> hình thức in", "loaiPhuCap": "Loại phụ cấp", "taiFileMauImport": "Tải file mẫu import {{title}}", "sinhSoTheoPhongThucHien": "<PERSON>h số theo phòng thực hiện", "chonTheoPhong": "<PERSON><PERSON><PERSON> theo phòng", "nhapCachDung": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch dùng", "vuiLongNhapGhiChuKhongQuaNumKyTu": "<PERSON>ui lòng nhập ghi chú không quá {{ num }} ký tự!", "giaPhuThuLonHonGia": "G<PERSON><PERSON> phụ thu: <b>{{giaPhuThu}}</b> lớ<PERSON> hơ<PERSON> (<PERSON><PERSON><PERSON> BH - <PERSON><PERSON><PERSON> bảo hiể<PERSON>): <b>{{gia}}</b>. Bạn có muốn tiếp tục thêm mới?", "giaPhuThuBeHonGia": "G<PERSON><PERSON> phụ thu: <b>{{giaPhuThu}}</b> b<PERSON> (<PERSON><PERSON><PERSON>H - <PERSON><PERSON><PERSON> bảo hiểm): <b>{{gia}}</b>. Bạn có muốn tiếp tục thêm mới?", "vuiLongNhapBietDuoc2": "<PERSON><PERSON> lòng nh<PERSON><PERSON> Biệt dược 2!", "batBuocNhap1Trong4GiaTri": "Bắt buộc nhập 1 trong 4 giá trị", "vuiLongNhapDacTinhDuocLy2": "<PERSON><PERSON> lòng nhập đặc t<PERSON>h dư<PERSON> lý 2", "vuiLongNhapMaAtc2": "<PERSON><PERSON> lòng nhập Mã ATC 2", "viTriThucHienDaTonTaiTenLoaiPhuCap": "Vị trí thực hiện \"{{vitri}}\" đã tồn tại loại phụ cấp: \"{{loaiPhuCap}}\"", "slGioiHanLuotKcbNgoaiTruBhyt": "SL giới hạn/l<PERSON><PERSON>t KCB ngoại trú BHYT", "nhapSLGioiHanLuotKcbNgoaiTruBhyt": "Nhập SL giới hạn/<PERSON><PERSON><PERSON>t KCB ngoại trú BHYT", "sinhSoTheoNoiLayMau": "<PERSON><PERSON> số theo nơi lấy mẫu", "chonTheoNoiLayMau": "<PERSON><PERSON><PERSON> theo n<PERSON> lấy mẫu", "thongTinThanhToan": "Thông tin thanh toán", "MID": "MID", "nhapMID": "Nhập MID", "TID": "TID", "nhapTID": "Nhập TID", "bin": "BIN", "nhapBIN": "Nhập BIN", "nhapTaiKhoan": "<PERSON><PERSON><PERSON><PERSON>", "vuiLongNhapTruong": "<PERSON>ui lòng nhập trường!", "xoaDongDuLieu": "<PERSON><PERSON><PERSON> dòng dữ liệu", "thuocNguyCoCao": "<PERSON><PERSON><PERSON><PERSON> nguy cơ cao", "nhapMaNganHang": "<PERSON><PERSON><PERSON><PERSON> mã ngân hàng", "vuiLongNhapMaNganHangKhongQua8KyTu": "<PERSON><PERSON> lòng nhập mã ngân hàng không quá 8 ký tự!", "maPhongGuiBhyt": "Mã phòng gửi BHYT", "serviceCodeBilling": "serviceCode billing", "nhapServiceCodeBilling": "Nhập serviceCode billing", "phanLoaiNBCapCuu24h": "Phân loại NB Cấp c<PERSON>u 24h", "tenMoiThau": "<PERSON><PERSON><PERSON> mời thầu", "thoiGianThucHienKhamPhut": "<PERSON><PERSON><PERSON><PERSON> gian thực hi<PERSON> (phút)", "nhomDaiPhieuNhapXuat": "<PERSON><PERSON><PERSON><PERSON> dả<PERSON>, xu<PERSON><PERSON>", "tenHinhThucNhapVaLoaiXuat": "<PERSON><PERSON><PERSON> hình thứ<PERSON>, <PERSON><PERSON><PERSON> xu<PERSON>t", "chonTenHinhThucNhapLoaiXuat": "<PERSON><PERSON><PERSON>ê<PERSON> hình thức nh<PERSON>, lo<PERSON><PERSON> xuất", "nhapMaNhom": "<PERSON><PERSON><PERSON><PERSON> mã nhóm", "maHoatChatThuocCoDinhGuiKemBhyt": "<PERSON>ã hoạt chất thuốc cố định gửi kèm BHYT", "tenThuocCoDinhGuiKemBhyt": "<PERSON><PERSON><PERSON> thu<PERSON><PERSON> cố định gửi kèm BHYT", "nhapMaHoatChatThuoc": "<PERSON><PERSON><PERSON><PERSON> mã hoạt chất thu<PERSON>c", "danhMucNhomDaiPhieuNhapXuat": "<PERSON><PERSON>ả<PERSON>, xu<PERSON><PERSON>", "tuChua": "<PERSON><PERSON> chứa", "nhapTuChua": "<PERSON><PERSON><PERSON><PERSON> tủ chứa", "kiemTraHangNgay": "<PERSON><PERSON><PERSON> tra hàng ng<PERSON>y", "giuongThucKeToiDa": "<PERSON><PERSON><PERSON><PERSON><PERSON> thực kê tối đa", "nhapGiuongThucKeToiDa": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON><PERSON><PERSON> thực kê tối đa", "sapXepTruong": "<PERSON><PERSON><PERSON> xếp tr<PERSON>", "coPhim": "cỡ phim", "danhMucCoPhim": "<PERSON><PERSON> mục Cỡ phim", "maCoPhim": "Mã cỡ phim", "nhapMaCoPhim": "<PERSON><PERSON><PERSON><PERSON> mã cỡ phim", "tenCoPhim": "<PERSON>ên cỡ phim", "nhapTenCoPhim": "<PERSON><PERSON><PERSON><PERSON> tên cỡ phim", "vuiLongNhapMaCoPhim": "<PERSON>ui lòng nhập mã cỡ phim!", "vuiLongNhapTenCoPhim": "<PERSON><PERSON> lòng nhập tên cỡ phim!", "vuiLongNhapGiaTriKhongQua255KyTu": "<PERSON><PERSON> lòng nhập giá trị không quá 255 ký tự!", "maKhoaGiuongBhyt": "Mã khoa giường BHYT", "themNhanhMucDoTuongTac": "<PERSON><PERSON><PERSON><PERSON><PERSON> độ tương tác", "themNhanhHauQuaTuongTac": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>h <PERSON> quả tương tác", "tenHauQuaTuongTac": "<PERSON><PERSON><PERSON> hậu quả tư<PERSON>ng tác", "nhapTenHauQuaTuongTac": "<PERSON><PERSON><PERSON><PERSON> tên hậu quả tương tác", "vuiLongNhapTenHauQuaTuongTac": "<PERSON><PERSON> lòng nhập tên hậu quả tương tác!", "tenMucDoTuongTac": "<PERSON><PERSON><PERSON> mức độ tương tác", "nhapTenMucDoTuongTac": "<PERSON><PERSON><PERSON><PERSON> tên mức độ tương tác", "vuiLongNhapTenMucDoTuongTac": "<PERSON><PERSON> lòng nhập tên mức độ tương tác!", "terminalId": "terminal.id", "nhapTerminalId": "Nhập terminal.id", "secretKey": "secret.key", "nhapSecretKey": "<PERSON><PERSON>ậ<PERSON> secret.key", "tenKhoaDuocPhepChiDinh": "<PERSON><PERSON><PERSON> k<PERSON>a đ<PERSON><PERSON><PERSON> phép chỉ định", "dvKemChePhamMau": "DV kèm chế phẩm máu", "dangTienHanhImportDuLieuTitle": "<PERSON><PERSON> tiến hành import dữ liệu {{title}}", "importDuLieuTitleThanhCong": "Import dữ liệu {{title}} thành công!", "importDuLieuTitleThatBai": "Import dữ liệu {{title}} thất bại!", "importVaiTro": "Import vai trò", "saoChepTaiKhoan": "<PERSON>o ch<PERSON> tài <PERSON>n", "saoChepTuTaiKhoan": "<PERSON><PERSON> ch<PERSON>p từ tài k<PERSON>n", "danhSachVaiTro": "<PERSON><PERSON> s<PERSON>ch vai trò", "saoChepTaiKhoanThanhCong": "<PERSON>o chép tài kho<PERSON>n thành công", "saoChepTaiKhoanThatBai": "<PERSON><PERSON> ch<PERSON>p tài k<PERSON>n thất bại", "luuVaDongBoDanhMuc": "<PERSON><PERSON><PERSON> và đồng bộ danh mục", "taiKhoanHDDT": "Tài kho<PERSON>n HĐĐT", "chuKyTay": "Chữ ký tay", "soLuongToiDa1LanChiDinh": "Số lượng tối đa 1 lần chỉ định", "nhapSoLuongToiDa1LanChiDinh": "Nhập số lượng tối đa 1 lần chỉ định", "luuVaTaoMoi": "Lưu và Tạo mới", "khongLuuVaTaoMoi": "Không lưu và Tạo mới", "banGhiHienTaiChuaLuu": "<PERSON><PERSON><PERSON> ghi hiện tại chư<PERSON>", "boQuaCanBangTai": "Bỏ qua cân bằng tải", "danhMucPhanLoaiVTYT": "<PERSON><PERSON> mục phân loại VTYT", "phanLoaiVTYT": "Phân loại VTYT", "chonPhanLoaiVTYT": "Chọn phân loại VTYT", "khongChonNhieuKhoaVaPhong": "<PERSON><PERSON> lòng chỉ chọn nhiều khoa chỉ định hoặc nhiều phòng, không chọn cả hai cùng lúc.", "nhapLyDoDenKham": "<PERSON><PERSON><PERSON><PERSON> lý do đến kh<PERSON>m", "vuiLongLyDoDenKhamKhongQua4000KyTu": "<PERSON><PERSON> lòng nhập lý do đến khám không quá 4000 ký tự", "thuTuUuTienThucHienDV": "<PERSON><PERSON><PERSON> tự ưu tiên thực hiện DV", "nhapThuTuUuTienThucHienDV": "<PERSON><PERSON><PERSON><PERSON> thứ tự ưu tiên thực hiện DV", "dvKemThuoc": "DV kèm thuốc", "capNhapLoaiCoPhimSoLuongPhim": "<PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> cỡ phim, số lư<PERSON> phim", "mucDoBangChung": "<PERSON><PERSON><PERSON> độ bằng chứng", "danhMucMucDoBangChung": "<PERSON><PERSON> m<PERSON><PERSON> độ bằng chứng", "chuaChonKhoaChiDinh": "<PERSON><PERSON><PERSON> chọn khoa chỉ định", "vuiLongNhapMucDoBangChung": "<PERSON><PERSON> lòng nhập mức độ bằng chứng", "thoiGianKhoiPhat": "Thời gian khởi phát", "vuiLongNhapMaMucDoBangChungKhongQua20KyTu": "<PERSON><PERSON> lòng nhập Mã mức độ bằng chứng không quá 20 ký tự!", "vuiLongNhapTenMucDoBangChung": "<PERSON><PERSON> lòng nhập Tê<PERSON> mức độ bằng chứng", "vuiLongNhapMaMucDoBangChung": "<PERSON><PERSON> lòng nhập Mã mức độ bằng chứng", "vuiLongNhapTenMucDoBangChungKhongQua1000KyTu": "<PERSON><PERSON> lòng nhập Tê<PERSON> mức độ bằng chứng không quá 1000 ký tự!", "khongDuocPhepNhapKhoangTrang": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> phép nhập kho<PERSON>ng trắng", "khongDuocPhepNhapKyTuDacBiet": "<PERSON><PERSON><PERSON><PERSON> đư<PERSON><PERSON> phép nhập ký tự đặc biệt", "vuiLongNhapMaChiSoSong": "<PERSON><PERSON> lòng nhập mã chỉ số sống!", "vuiLongNhapMaChiSoSongKhongQua20KyTu": "<PERSON><PERSON> lòng nhập mã chỉ số sống không quá 20 ký tự!", "nhapGiaGoc": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> gốc", "nhapGiaHaoPhi": "<PERSON>hậ<PERSON> giá hao phí", "thuocVatTuBanGiao": "<PERSON><PERSON><PERSON><PERSON>, vật tư bàn giao", "danhMucThuocVatTuBanGiao": "<PERSON><PERSON>, vật t<PERSON> bàn giao", "capNhatThanhCongDuLieuNguoiGioiThieu": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công dữ liệu người giới thiệu", "themMoiThanhCongDuLieuNguoiGioiThieu": "<PERSON>hê<PERSON> mới thành công dữ liệu người giới thiệu", "ngayHoatDong": "<PERSON><PERSON><PERSON> động", "chonNgayHoatDong": "<PERSON><PERSON><PERSON> ng<PERSON>y ho<PERSON>t động", "yeuCauPhanTangNguyCo": "<PERSON><PERSON><PERSON> c<PERSON>u phân tầng nguy cơ", "manHinhHienThi": "<PERSON><PERSON><PERSON> hình hiển thị", "hienThiTiepDonTrenKiosk": "<PERSON><PERSON><PERSON> thị tiếp đón trên kiosk", "phanTangNguyCoBN": "<PERSON><PERSON> tầng nguy cơ b<PERSON>nh nhân", "danhMucPhanTangNguyCoBN": "<PERSON><PERSON> tầng nguy cơ bệnh nhân", "coCheThuChiNoiBo": "<PERSON><PERSON> chế thu chi <PERSON> bộ", "danhMucCoCheThuChiNoiBo": "<PERSON><PERSON><PERSON> chế thu chi <PERSON> bộ", "mucChi": "<PERSON><PERSON><PERSON> chi", "nhapMucChi": "<PERSON><PERSON><PERSON><PERSON> mức chi", "vuiLongNhapMucChi": "<PERSON>ui lòng nhập mức chi!", "vuiLongNhapMucChiLonHon0": "<PERSON><PERSON> lòng nh<PERSON>p mức chi > 0", "tyLeHuongTheoQuyChe": "Tỷ lệ hưởng theo quy chế", "nhapTyLeHuongTheoQuyChe": "<PERSON><PERSON><PERSON><PERSON> tỷ lệ hưởng theo quy chế", "VuiLongNhapTyLeHuongTheoQuyChe": "<PERSON><PERSON> lòng nhập tỷ lệ hưởng theo quy chế!", "tyLeHuongTheoGiamDocDuyet": "Tỷ lệ hưởng theo <PERSON> đố<PERSON> du<PERSON>", "nhapTyLeHuongTheoGiamDocDuyet": "<PERSON><PERSON><PERSON><PERSON> tỷ lệ hưởng theo <PERSON> đốc duy<PERSON>t", "thueTndn": "Thuế TNDN", "nhapThueTndn": "<PERSON><PERSON><PERSON><PERSON> thuế TNDN", "vuiLongNhapThueTndn": "<PERSON>ui lòng nhập thuế TNDN!", "danhMucMaTuyChinh": "<PERSON><PERSON> mục mã tuỳ chỉnh", "maTuyChinh": "Mã tuỳ chỉnh", "danhSachCaLamViec": "<PERSON><PERSON> s<PERSON>ch ca làm việc", "trua": "Trưa", "tenKhuVuc": "<PERSON><PERSON><PERSON> khu vực", "logNguoiDung": "Log Ngườ<PERSON> dùng", "vuiLongKiemTraLaiCaLamViec": "Vui lòng kiểm tra lại ca làm việc!", "vaiTroDuocGan": "<PERSON><PERSON> trò đ<PERSON><PERSON><PERSON> gán", "tenQuyen": "<PERSON><PERSON><PERSON> q<PERSON>", "anhKy": "Ảnh ký", "vuiLongNhapCheDoAn": "<PERSON><PERSON> lòng nhập chế độ ăn", "vuiLongChonCheDoChamSoc": "<PERSON><PERSON> lòng chọn chế độ chăm sóc", "nhomLoaiBenhAn": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON> b<PERSON><PERSON>", "danhMucNhomLoaiBenhAn": "<PERSON><PERSON> lo<PERSON> b<PERSON><PERSON>", "maNhomLoaiBenhAn": "<PERSON><PERSON> nhóm lo<PERSON>i b<PERSON><PERSON>n", "nhapMaNhomLoaiBenhAn": "<PERSON><PERSON><PERSON><PERSON> mã nhóm lo<PERSON>i b<PERSON><PERSON>n", "vuiLongNhapMaNhomLoaiBenhAn": "<PERSON>ui lòng nhập mã nhóm loại bệnh <PERSON>n!", "vuiLongNhapMaNhomLoaiBenhAnKhongQuaNumKyTu": "<PERSON>ui lòng nhập mã nhóm loại bệnh án không quá {{ num }} ký tự!", "tenNhomLoaiBenhAn": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> lo<PERSON> b<PERSON><PERSON>n", "nhapTenNhomLoaiBenhAn": "<PERSON><PERSON><PERSON><PERSON> tên nhóm lo<PERSON>i b<PERSON>nh <PERSON>n", "vuiLongNhapTenNhomLoaiBenhAn": "<PERSON>ui lòng nhập tên nhóm loại bệnh án!", "vuiLongNhapTenNhomLoaiBenhAnKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập tên nhóm loại bệnh án không quá {{ num }} ký tự!", "tenCanhBao1": "<PERSON>ên <PERSON> b<PERSON> 1", "tenCanhBao2": "<PERSON><PERSON><PERSON> b<PERSON> 2", "chonNhomLoaiBenhAn": "<PERSON><PERSON><PERSON> nh<PERSON>m lo<PERSON> b<PERSON><PERSON>n", "canhBaoSuDungDichVu": "<PERSON><PERSON><PERSON> báo sử dụng dịch vụ", "nhapCanhBaoSuDungDichVu": "<PERSON><PERSON><PERSON><PERSON> cảnh báo sử dụng dịch vụ", "chuaGomTrongGiuong": "<PERSON><PERSON><PERSON> bao gồm trong gói ngày giường", "chonChuaGomTrongGiuong": "<PERSON><PERSON><PERSON> chưa bao gồm trong gói ngày giường", "khongTheChonDongThoiKhongTinhTienVaChuaGomTrongGiuong": "<PERSON>h<PERSON>ng thể chọn đồng thời 'Không tính tiền' và '<PERSON><PERSON><PERSON> bao gồm trong gói ngày giường'", "danhMucThangInBaoCaoKho": "<PERSON><PERSON> m<PERSON> tháng in báo cáo kho", "thangInBaoCaoKho": "<PERSON><PERSON><PERSON><PERSON> in báo cáo kho", "vuiLongNhapThang": "<PERSON><PERSON> lòng nh<PERSON>p tháng", "vuiLongNhapNam": "<PERSON><PERSON> lòng nh<PERSON>p năm", "vuiLongNhapDenNgay": "<PERSON><PERSON> lòng nh<PERSON>p đến ng<PERSON>y", "nhapThang": "<PERSON><PERSON><PERSON><PERSON>g", "capNhatThanhCongSinhSoThuTuThuNgan": "<PERSON><PERSON><PERSON> nhật thiết lập sinh số thứ tự thu ngân thành công", "corticoid": "Corticoid", "phongQuanLy": "<PERSON><PERSON><PERSON> quản lý", "chonNgonNgu": "<PERSON><PERSON><PERSON> ngôn ngữ", "dongBoGiaThanhCong": "<PERSON><PERSON>ng bộ giá thành công", "dongBoGiaThatBai": "Đồng bộ giá thất bại", "nhapTenNhom": "<PERSON><PERSON><PERSON><PERSON> tên n<PERSON>", "nhomMaLuuTruBenhAn": "Nhóm mã lưu trữ b<PERSON>nh <PERSON>n", "chonNhomMaLuuTruBenhAn": "<PERSON><PERSON><PERSON> nhóm mã lưu trữ b<PERSON>nh án", "timNhomMaLuuTruBenhAn": "Tìm nhóm mã lưu trữ bệnh án", "nhapKyHieuLuuTru": "<PERSON><PERSON><PERSON><PERSON> ký hiệu lưu trữ", "timMaNhom": "<PERSON><PERSON><PERSON> mã nhóm", "timTenNhom": "<PERSON><PERSON><PERSON> tên n<PERSON>m", "timKyHieuLuuTru": "<PERSON><PERSON><PERSON> ký hiệu lưu trữ", "vuiLongChonMucDichSuDung": "<PERSON><PERSON> lòng chọn mục đích sử dụng", "vuiLongNhapMaTuongDuongKhongNumKyTu": "<PERSON><PERSON> lòng nhập mã tương đương không quá {{num}} ký tự", "vuiLongNhapThongTinThauKhongNumKyTu": "<PERSON><PERSON> lòng nhập thông tin thầu không quá {{num}} ký tự", "mimsGuid": "MIMS GUID", "mimsType": "MIMS TYPE", "maThuocByt": "Mã thuốc BYT", "timMimsGuid": "Tìm MIMS GUID", "timMimsType": "Tìm MIMS TYPE", "timMaByt": "<PERSON><PERSON><PERSON> thuốc BYT", "tacNhanDiUng": "<PERSON><PERSON><PERSON> nhân dị <PERSON>ng", "danhMucTacNhanDiUng": "<PERSON><PERSON> mục tác nhân dị <PERSON>ng", "maTacNhanDiUng": "Mã tác nhân dị ứng", "tenTacNhanDiUng": "<PERSON><PERSON><PERSON> t<PERSON>c nhân d<PERSON>ng", "loaiTacNhanDiUng": "Loại tác nhân dị <PERSON>ng", "capNhatThanhCongDuLieutacNhanDiUng": "<PERSON><PERSON><PERSON> nhật thành công dữ liệu tạc nhân dị ứng", "themMoiThanhCongDuLieutacNhanDiUng": "<PERSON>hê<PERSON> mới thành công dữ liệu tạc nhân dị ứng", "capNhatKhongThanhCongDuLieutacNhanDiUng": "<PERSON><PERSON><PERSON> nhật không thành công dữ liệu tạc nhân dị ứng", "themMoiKhongThanhCongDuLieutacNhanDiUng": "Thê<PERSON> mới không thành công dữ liệu tạc nhân dị ứng", "timMaTacNhanDiUng": "T<PERSON>m mã tác nhân dị <PERSON>ng", "timTenTacNhanDiUng": "<PERSON><PERSON><PERSON> tên tác nhân dị <PERSON>ng", "luonHienThiDMPhieu": "<PERSON><PERSON><PERSON> hiển thị ở danh mục phiếu", "luonHienThiPhieuOHSBA": "<PERSON><PERSON><PERSON> hiển thị phiếu ở HSBA", "thanhPhanThamGiaHoiChan": "<PERSON><PERSON><PERSON><PERSON> phần tham gia hội chẩn", "chonThanhPhanThamGiaHoiChan": "<PERSON><PERSON><PERSON> thành phần tham gia hội chẩn", "danhMucThanhPhanThamGiaHoiChan": "<PERSON><PERSON> m<PERSON><PERSON> phần tham gia hội chẩn", "maThanhPhanThamGiaHoiChan": "<PERSON><PERSON> phần tham gia hội chẩn", "tenThanhPhanThamGiaHoiChan": "<PERSON><PERSON><PERSON> phần tham gia hội chẩn", "dichVuGiuongTuChon": "<PERSON><PERSON><PERSON> vụ giườ<PERSON> tự chọn", "dichVuTrung": "<PERSON><PERSON><PERSON> v<PERSON> trùng", "nhapDichVuTrung": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> v<PERSON> trùng", "vuiLongChonKhoaThucHien": "<PERSON><PERSON> lòng chọn khoa thực hiện", "danhSachPhieuInDiKem": "<PERSON><PERSON> s<PERSON>ch p<PERSON>u in đi kèm", "thongTinDichVuTrongPhacDoDieuTri": "Th<PERSON>ng tin dịch vụ trong phác đồ điều trị", "khongDuocPhepChiDinhKhiChuaDenNgay": "<PERSON><PERSON><PERSON><PERSON> đư<PERSON><PERSON> phép chỉ định khi chưa đến ngày", "danhMucThietLapDieuKienChiDinh": "<PERSON><PERSON> <PERSON><PERSON><PERSON> lập điều kiện chỉ định", "thietLapDieuKienChiDinh": "<PERSON><PERSON><PERSON><PERSON> lập điều kiện chỉ định", "dieuKienChiDinh": "<PERSON><PERSON><PERSON>u kiện chỉ định", "chonDieuKienChiDinh": "<PERSON><PERSON>n điều kiện chỉ định", "donGiaHienThiKhiChiDinh": "Đơn giá hiển thị khi chỉ định", "danhMucNhomPhuCap": "<PERSON><PERSON> mục nhóm phụ cấp", "nhomPhuCap": "Nhóm phụ cấp", "soGioDuocPhepTraSuatAn": "Số giờ đư<PERSON><PERSON> phép trả suất ăn", "nhapSoGioDuocPhepTraSuatAn": "<PERSON><PERSON>ập số giờ đư<PERSON><PERSON> phép trả suất ăn", "khongApTran45ThangLuong": "<PERSON><PERSON><PERSON><PERSON> á<PERSON> tr<PERSON><PERSON> 45 tháng l<PERSON>", "khongGioiHanMucThanhToan": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON>i hạn mức thanh toán", "themMoiThanhCongThietLapChanKy": "<PERSON><PERSON><PERSON><PERSON> mới thành công thiết lập chân ký", "capNhatThanhCongThietLapChanKy": "<PERSON><PERSON><PERSON> nhật thành công thiết lập chân ký", "nhomHuongPhuCapPTTT": "Nhóm hưởng phụ cấp PT/TT", "huongPhuCapPTTT": "Hưởng phụ cấp PT/TT", "maNhomKhoaEkipHuongPhuCapTheoYeuCau": "<PERSON><PERSON> nhóm/khoa/ekip hưởng phụ cấp theo yêu cầu", "tenNhomKhoaEkipHuongPhuCapTheoYeuCau": "<PERSON>ê<PERSON> nhó<PERSON>/khoa/ekip hưởng phụ cấp theo yêu cầu", "danhMucNhomKhoaEkipHuongPhuCapTheoYeuCau": "<PERSON><PERSON> mục nhóm/khoa/ekip hưởng phụ cấp theo yêu cầu", "vuiLongNhapMaNhomKhoaEkipHuongPhuCapTheoYeuCau": "<PERSON><PERSON> lòng nhập mã nhóm/khoa/ekip hưởng phụ cấp theo yêu cầu!", "vuiLongNhapTenNhomKhoaEkipHuongPhuCapTheoYeuCau": "<PERSON><PERSON> lòng nhập tên nhóm/khoa/ekip hưởng phụ cấp theo yêu cầu!", "donViNhomHuong": "Đơn vị nhóm hưởng", "maDonVi": "Mã đơn vị", "nhapMaDonVi": "<PERSON><PERSON><PERSON><PERSON> mã đơn vị", "tenDonVi": "<PERSON><PERSON><PERSON> đơn vị", "nhapTenDonVi": "<PERSON><PERSON><PERSON><PERSON> tên đơn vị", "nhomKhoaPhongEkipHuongPhuCapTheoYeuCau": "<PERSON><PERSON><PERSON><PERSON> khoa/ phòng/ ekip hưởng phụ cấp theo yêu cầu", "chonLoaiHienThi": "<PERSON><PERSON><PERSON> hiển thị", "soNgayKeTruoc": "<PERSON><PERSON> ngày kê trước", "nhapSoNgayKeTruoc": "<PERSON><PERSON><PERSON><PERSON> số ngày kê trước", "soLuongNBChoPhep": "Số lượng NB cho phép", "nhapSoLuongNBChoPhep": "<PERSON><PERSON> lòng nhập số lượng NB cho phép", "khongNhapPtv": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> buôc nhập PTVC/ TTVC", "nhomDichVuCap1BangKe": "Nhóm dịch vụ cấp 1 bảng kê", "chonNhomDichVuCap1BangKe": "Chọn n<PERSON>ó<PERSON> dịch vụ cấp 1 bảng kê", "diaDiemTraKetQua": "<PERSON><PERSON><PERSON> điểm trả kết quả", "giaGoc": "<PERSON><PERSON><PERSON>", "khongCheckCapKy": "Không check cấp ký", "vaiTroXemNhomBaoCao": "Vai trò xem nhóm báo cáo", "chonVaiTroXemNhomBaoCao": "<PERSON><PERSON>n vai trò xem nhóm báo cáo", "vuiLongChonVaiTroXemNhomBaoCao": "<PERSON>ui lòng chọn vai trò xem nhóm báo cáo", "chonMaKho": "<PERSON><PERSON><PERSON> mã kho", "banCoChacChanMuonXoaKho": "Bạn có chắc chắn muốn xóa kho {{ten}}?", "taoBanSao": "T<PERSON><PERSON> bản sao", "loaiHienThiPhieuIn": "<PERSON><PERSON><PERSON> hiển thị phi<PERSON>u in", "danhMucLoaiHienThiPhieuIn": "<PERSON><PERSON> <PERSON><PERSON> hiển thị phi<PERSON>u in", "giaTriMacDinh": "<PERSON><PERSON><PERSON> trị mặc định", "thuaHungCheDoTuTucBhytCuaCha": "Thừa hưởng chế độ tự túc/BHYT của cha", "chonDieuKienKy": "<PERSON><PERSON><PERSON> điều kiện ký", "nhapMaKySo": "<PERSON><PERSON><PERSON><PERSON> mã ký số", "maKySo": "Mã ký số", "nhomBaoCaoPhuCapPTTT": "Nhóm báo c<PERSON>o phụ cấp PTTT", "chanNguongToiThieu": "Chặn ngưỡng tối thiểu", "chanNguongToiDa": "Chặn ngưỡng tối đa", "thietLapDKienHoanThanhKyKhiChuyenRaVien": "<PERSON><PERSON><PERSON><PERSON> lập điều kiện hoàn thành ký khi chuyển khoa ra viện", "ketNoiBenhAn": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "chonKetNoiBenhAn": "<PERSON><PERSON><PERSON> kết n<PERSON>i b<PERSON><PERSON>n", "danhMucKhaiBaoMauProtocolPttt": "<PERSON><PERSON> mục khai báo mẫu Protocol PTTT", "maTruong": "Mã trường", "timMaTruong": "<PERSON><PERSON><PERSON> mã trường", "vuiLongNhapMaTruong": "<PERSON><PERSON> lòng nhập mã trường", "tenNoiDungTinhChat": "<PERSON><PERSON><PERSON> nội dung/t<PERSON>h chất", "timTenNoiDungTinhChat": "T<PERSON><PERSON> tên nội dung/t<PERSON>h chất", "vuiLongNhapTenNoiDungTinhChat": "<PERSON><PERSON> lòng nhập tên nội dung/t<PERSON>h chất", "vuiLongNhapTenNoiDungTinhChatKhongQuaNumKyTu": "<PERSON><PERSON> lòng nhập tên nội dung/t<PERSON>h chất không quá {{ num }} ký tự!", "capCha": "<PERSON><PERSON><PERSON> cha", "timCapCha": "<PERSON><PERSON><PERSON> c<PERSON>p cha", "vuiLongChonCapCha": "<PERSON><PERSON> lòng chọn cấp cha", "danhMucMaHoaProtocolChung": "<PERSON><PERSON> mục mã hóa Protocol chung", "khaiBaoProtocolPTTT": "Khai báo Protocol PTTT", "maMauProtocol": "Mã mẫu protocol", "timMaMauProtocol": "Tìm mã mẫu protocol", "vuiLongNhapMaMauProtocol": "<PERSON><PERSON> lòng nhập mã mẫu protocol", "tenMauProtocol": "Tên mẫu protocol", "timTenMauProtocol": "T<PERSON><PERSON> tên mẫu protocol", "vuiLongNhapTenMauProtocol": "<PERSON><PERSON> lòng nhập tên mẫu protocol", "dichVuApDung": "<PERSON><PERSON><PERSON> v<PERSON> d<PERSON>", "chonDichVuApDung": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON> dụ<PERSON>", "timDichVuApDung": "<PERSON><PERSON><PERSON> d<PERSON> v<PERSON> d<PERSON>", "thietLapProtocol": "Thiế<PERSON> lập protocol", "dangHienThi": "<PERSON><PERSON><PERSON> hiển thị", "chonDangHienThi": "<PERSON><PERSON><PERSON> dạng hiển thị", "choPhepBoSungThemTextGhiChu": "<PERSON> ph<PERSON><PERSON> bổ sung thêm text ghi chú", "choPhepThucHienThatBai": "<PERSON> phép thực hiện thất bại", "macDinhChon": "Mặc đ<PERSON>nh ch<PERSON>n", "layLenBaoCao": "<PERSON><PERSON><PERSON> lên b<PERSON>o c<PERSON>o không", "batBuocKhaiBao": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> khai báo", "khoaLienKet": "<PERSON><PERSON><PERSON> li<PERSON><PERSON> k<PERSON>t", "themMoiProtocolChung": "Thêm mới protocol chung", "khaiBaoHangHoaDungKemDvkt": "<PERSON><PERSON> báo hàng hoá dùng kèm DVKT", "danhMucKhaiBaoHangHoaDungKemDvkt": "<PERSON><PERSON> mục khai báo hàng hoá dùng kèm DVKT", "tenDichVuApDung": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON> d<PERSON>", "chonTenDichVuApDung": "<PERSON><PERSON><PERSON> tên dịch v<PERSON> dụng", "maDichVuApDung": "<PERSON><PERSON> dịch v<PERSON> dụng", "chonMaDichVuApDung": "<PERSON><PERSON>n mã dịch v<PERSON> dụng", "vuiLongChonDichVuApDung": "Vui lòng chọn dịch vụ áp dụng!", "soGioTachDot": "Số giờ tách đợt", "soLuongNguoiBenhTaiKham": "Số lượng NB tái khám", "nhapSoLuongNguoiBenhTaiKham": "Nhập số lượng NB tái khám", "vuiLongNhapSoLuongNguoiBenhTaiKhamLonHon0": "<PERSON><PERSON> lòng nhập số lượng người bệnh tái khám lớn hơn 0", "chuyenKhoaToiKhoaThuong": "Chuyển khoa tới khoa thường", "chuyenKhoaToiKhoaPhauThuat": "Chuyển khoa tới khoa phẫu thuật", "chuyenKhoaTuKhoaPhauThuat": "<PERSON>y<PERSON><PERSON> từ khoa phẫu thuật", "raVienTuKhoaThuong": "<PERSON> viện từ khoa thường", "raVienTuKhoaPhauThuat": "<PERSON> viện từ khoa phẫu thuật", "chonTenPhieu": "<PERSON><PERSON><PERSON> tên <PERSON>u", "vuiLongChonTenPhieu": "<PERSON><PERSON> lòng chọn tên phi<PERSON>u", "danhMucPhanLoaiVTYTHoaChat": "<PERSON><PERSON> m<PERSON> loại VTYT/Hóa chất", "phanLoaiVTYTHoaChat": "Phân loại VTYT/Hoá chất", "vuiLongNhapMaPhanLoaiKhongQua50KyTu": "<PERSON><PERSON> lòng nhập mã phân loại không quá 50 ký tự", "maThuocThayThe": "<PERSON><PERSON> thuốc thay thế", "maHoaChatThayThe": "<PERSON><PERSON> hóa chất thay thế", "maVatTuThayThe": "<PERSON><PERSON> vật tư thay thế", "tenDichVuDungKem": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ dùng kèm", "vuiLongChonTenDichVuDungKem": "<PERSON>ui lòng chọn tên dịch vụ dùng kèm", "loaiDichVuApDung": "<PERSON><PERSON><PERSON> d<PERSON>ch v<PERSON> d<PERSON>", "vuiLongChonLoaiDichVuApDung": "<PERSON><PERSON> lòng chọn lo<PERSON>i dịch v<PERSON>p dụng", "maDvDungKem": "<PERSON><PERSON> dịch vụ dùng kèm", "tenDvDungKem": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ dùng kèm", "maDvApDung": "<PERSON><PERSON> dịch v<PERSON> dụng", "tenDvApDung": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON> d<PERSON>", "vuiLongChonMauProtocol": "<PERSON><PERSON> lòng chọn mẫu protocol", "mauProtocol": "Mẫu protocol", "phanLoaiHoaChat": "<PERSON><PERSON> lo<PERSON>i hóa chất", "chinhSuaHangHoaDungKemHangLoat": "Chỉnh sửa hàng hoá dùng kèm hàng loạt", "thongTinHangHoaChinhSua": "Thông tin hàng hoá chỉnh sửa", "hangHoaDungKemCanChinhSua": "<PERSON>àng hoá dùng kèm cần chỉnh sửa", "hangHoaDungKemApDungMoi": "Hàng hoá dùng kèm áp dụng mới", "vuiLongChonHangHoaDungKemCanChinhSua": "<PERSON><PERSON> lòng chọn hàng hoá dùng kèm cần chỉnh sửa", "vuiLongChonHangHoaDungKemApDungMoi": "<PERSON>ui lòng chọn hàng hoá dùng kèm áp dụng mới", "hangHoaDungKemCanChinhSuaVaApDungMoiKhacNhau": "Hàng hoá dùng kèm cần chỉnh sửa và áp dụng mới phải khác nhau"}