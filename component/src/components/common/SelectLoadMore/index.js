import { Select } from "antd";
import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  memo,
  useImperativeHandle,
  forwardRef,
} from "react";
import { t } from "i18next";
import { isArray, uniqBy } from "lodash";
const DEFAULT_SIZE = 20;
const DEFAULT_PARAM = { active: true };

const mapDataDefault = (i) => ({
  value: i.id,
  label: i.ten,
});

const SelectLoadMore = (
  {
    placeholder = "Tìm kiếm",
    value,
    onChange = () => {},
    getValueApi,
    className,
    mode,
    hasAll,
    limit = DEFAULT_SIZE,
    addParam = DEFAULT_PARAM,
    blurReset = false,
    addValue,
    disabled,
    refSelect,

    // bắt buộc
    api, // là 1 promise
    apiGetById, // là 1 promise
    mapData = mapDataDefault,
    filterFunc = () => true,
    keySearch = "ten",
    firstLoadData = true,
    uniqBy<PERSON>ey,
    customFilterAndMapData,
    haveLoading = false,
    ...rest
  },
  ref
) => {
  const refTimeoutSearch = useRef();
  const [state, _setState] = useState({
    param: { page: 0, size: limit },
    listData: [],
    fullLoad: false,
    loading: false,
    checkData: true,
  });
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  const pre = hasAll ? [{ value: "", label: t("common.tatCa") }] : [];

  const addValueCustom = useMemo(() => {
    return Array.isArray(addValue) ? addValue : addValue ? [addValue] : [];
  }, [addValue]);

  useImperativeHandle(ref, () => ({
    onClearListData: handleClearListData,
    onClearAllData: handleClearAllData,
    loadData,
  }));

  const listOption = useMemo(() => {
    return addValueCustom?.length
      ? [
          ...addValueCustom,
          ...(state.listData.filter((i) =>
            addValueCustom.find((x) => x.value !== i.value)
          ) || []),
        ]
      : state.listData;
  }, [addValueCustom, state.listData]);

  useEffect(() => {
    let data = value || [];
    if (!isArray(data)) {
      data = [data];
    }
    if (!state.checkData || !data?.length || !apiGetById || addValue) return;
    const getValueSelected = async () => {
      try {
        const promises = data.map((id) => {
          const item = state.listData.find((item) => item.value !== id);
          if (item) return item;
          return new Promise(async (resolve, reject) => {
            try {
              const res = await apiGetById(id);
              resolve(res.data);
            } catch (error) {
              resolve(null);
            }
          });
        });
        const values = (await Promise.all(promises))
          .filter((item) => item)
          .map((item) => mapData(item));
        const listData = [...state.listData, ...values];
        setState({ listData, checkData: false });
        loadData({}, true);
      } catch (error) {}
    };
    getValueSelected();
  }, [listOption, value, addValue, state.checkData, apiGetById]);

  const loadData = (data = {}, isReset) => {
    const param = {
      ...(isReset
        ? { page: 0, size: limit }
        : {
            ...state.param,
            page: state.param?.page + 1,
          }),
      ...data,
      ...(addParam || {}),
    };
    setState({
      param,
      loading: true,
      fullLoad: isReset ? false : state.fullLoad,
    });
    api &&
      api(param)
        .then((res) => {
          if (res.data.length === 0) {
            // không có dữ liệu
            // if (param[keySearch] && param.page === 0) {
            //   // search không có dữ liệu
            //   setState({ fullLoad: true, listData: [] });
            // } else {
            //   // cuộn tới page cuối cùng
            // }
            res.pageNumber === 0
              ? setState({ fullLoad: true, listData: [] })
              : setState({ fullLoad: true });
            return;
          }
          getValueApi && getValueApi(res.data);
          let _data = [];
          if (customFilterAndMapData) {
            _data = customFilterAndMapData(res.data);
          } else {
            _data = res.data?.filter(filterFunc).map(mapData);
          }
          let listData = isReset
            ? [...pre, ..._data]
            : [...state.listData, ..._data];
          if (uniqByKey) {
            listData = uniqBy(listData, `${uniqByKey}`);
          }
          setState({ listData });
        })
        .catch((err) => {})
        .finally(() => {
          setState({ loading: false });
        });
  };

  const handleSearch = (data) => {
    if (rest?.onSearch) {
      rest?.onSearch(loadData, data);
      return;
    }
    if (refTimeoutSearch.current) {
      clearTimeout(refTimeoutSearch.current);
    }

    refTimeoutSearch.current = setTimeout(() => {
      loadData({ [keySearch]: data }, true);
    }, 500);
  };

  const onClear = () => {
    loadData({}, true);
  };

  useEffect(() => {
    if (firstLoadData) {
      loadData({}, true);
    }
  }, [JSON.stringify(addParam)]);

  const handleClearListData = () => {
    setState({ listData: [] });
  };

  const handleClearAllData = () => {
    _setState({
      param: { page: 0, size: limit },
      listData: [],
      fullLoad: false,
      loading: false,
      checkData: true,
    });
  };

  console.log("🚀 KhoaMilan -> onChange", onChange);

  const handleChange = (value, item) => {
    console.log("🚀 KhoaMilan -> change");
    // nếu xóa hết các lựa chọn thì load lại dữ liệu
    if (mode === "multiple" && value?.length === 0) {
      loadData({}, true);
    }
    console.log("🚀 KhoaMilan -> data", { value, item });
    onChange(value, item);
  };

  const onBlur = () => {
    // khi blur khỏi select và đang có giá trị search thì sẽ load lại dữ liệu
    if (blurReset && state.param[keySearch]) {
      loadData({}, true);
    }
  };

  const onPopupScroll = (e) => {
    const { target } = e;
    if (
      target.scrollTop + target.offsetHeight > target.scrollHeight - 200 &&
      !state.loading &&
      !state.fullLoad
    ) {
      loadData();
    }
  };

  const onPasteSelect = (e) => {
    let text = e.clipboardData.getData("Text");
    rest?.onPaste && rest?.onPaste(loadData, text);
  };

  console.log("🚀 KhoaMilan -> rest", rest);

  return (
    <Select
      placeholder={placeholder}
      options={listOption}
      onChange={(e) => {
        console.log("🚀 KhoaMilan -> e", e);
        handleChange(e);
      }}
      onPopupScroll={onPopupScroll}
      showSearch
      onBlur={onBlur}
      filterOption={false}
      allowClear
      onClear={onClear}
      className={className}
      mode={mode}
      ref={refSelect}
      value={value}
      disabled={disabled}
      {...rest}
      onPaste={onPasteSelect}
      onSearch={handleSearch}
      {...(haveLoading && { loading: state.loading })}
    />
  );
};

export default memo(forwardRef(SelectLoadMore));
