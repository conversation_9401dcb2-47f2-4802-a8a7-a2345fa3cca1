import React, { useEffect, useMemo, useRef, useState } from "react";
import { MainPage } from "./styled";
import { useTranslation } from "react-i18next";
import { Col, Drawer, Menu, Row, message } from "antd";
import {
  CACHE_KEY,
  DATA_MODE_DS_NB,
  HOTKEY,
  ROLES,
  TRANG_THAI_PHA_CHE,
  DS_TINH_CHAT_KHOA,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import ThongTinNb from "./containers/ThongTinNb";
import DsPhieuPhaChe from "./containers/DsPhieuPhaChe";
import { Button, Card, Dropdown, ModalSignPrint } from "components";
import DsLichSuPhaChe from "./containers/DsLichSuPhaChe";
import BangPhaCheThuoc from "./containers/BangPhaCheThuoc";
import {
  useCache,
  useGuid,
  useLoading,
  useSearchParams,
  useStore,
} from "hooks";
import { useHistory, useParams } from "react-router-dom";
import { SVG } from "assets";
import { useDispatch } from "react-redux";
import { ModalDsNbPhaChe } from "../DanhSachPhaCheThuoc/ModalDsNbPhaChe";

const PhieuPhaCheThuoc = () => {
  const { t } = useTranslation();
  const history = useHistory();
  const { searchParams } = useSearchParams();
  const nbDotDieuTriId = searchParams.nbDotDieuTriId;
  const { id } = useParams();
  const { showLoading, hideLoading } = useLoading();
  const dataTongHopTheoId = useStore("nbPhaChe.dataTongHopTheoId", {});
  const listAllKhoaNoiTru = useStore("khoa.listKhoaTheoTaiKhoan", []);

  const refModalDsNbPhaChe = useRef(null);
  const refSaveBangPhaCheThuoc = useRef(null);
  const refClickBtnSave = useRef(null);
  const layerId = useGuid();
  const refPhieuIn = useRef(null);
  const refModalSignPrint = useRef(null);

  const {
    nbPhaChe: {
      chuyenPhaChe,
      duyetPhaChe,
      huyDuyetPhaChe,
      giaoPhaChe,
      huyGiaoPhaChe,
      huyChuyenPhaChe,
      onSearchTongHopTheoId,
      inPhieuPhaChe,
      inPhieuDichPha,
      inNhanPcHangLoat,
      onChangeInputSearch,
      clearData,
    },
    phimTat: { onRegisterHotkey, onAddLayer, onRemoveLayer },
    phieuIn: { getPhieuIn },
    khoa: { searchTheoTaiKhoan },
  } = useDispatch();

  const [modeDsNb] = useCache(
    CACHE_KEY.DATA_MODE_DS_NB_PHA_CHE_THUOC,
    "",
    null,
    false
  );
  const [modeDsNbLichSu] = useCache(
    CACHE_KEY.DATA_MODE_DS_NB_LICH_SU_PHA_CHE_THUOC,
    "",
    null,
    false
  );

  const [state, _setState] = useState({
    modeDsNb: DATA_MODE_DS_NB.DRAWER,
    modeDsNbLichSu: DATA_MODE_DS_NB.DRAWER,
    openDropdownPhieuIn: false,
  });
  const setState = (data = {}) => _setState((pre) => ({ ...pre, ...data }));

  const handleCloseDsNbDrawer = () => {
    setState({ isDrawerDsnb: false });
  };

  const handleCloseDsLichSuNbDrawer = () => {
    setState({ isDrawerLichSuPhaChe: false });
  };

  const changeModeDsNb = (modeDsNb) => {
    setState({
      modeDsNb,
      isDrawerDsnb: modeDsNb === DATA_MODE_DS_NB.DRAWER,
    });
  };

  const changeModeDsNbLichSu = (modeDsNbLichSu) => {
    setState({
      modeDsNbLichSu,
      isDrawerLichSuPhaChe: modeDsNbLichSu === DATA_MODE_DS_NB.DRAWER,
    });
  };

  const statusTicket = useMemo(() => {
    let result = 0;
    [
      { id: 1, ten: "/them-moi" },
      { id: 2, ten: "/chi-tiet" },
      { id: 3, ten: "/chinh-sua" },
    ].forEach((item) => {
      if (window.location.pathname.indexOf(item.ten) !== -1) result = item.id;
    });

    return result;
  }, [window.location.pathname]);

  useEffect(() => {
    onChangeInputSearch({
      page: 0,
      size: 50,
    });
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current();
          },
        },
        {
          keyCode: HOTKEY.F3, //F3
          onEvent: (e) => {
            refPhieuIn.current && refPhieuIn.current.click();
            document.getElementById("button-in-giay-to").focus();
          },
        },
      ],
    });

    return () => {
      onRemoveLayer({ layerId: layerId });
      clearData();
    };
  }, []);

  useEffect(() => {
    setState({
      modeDsNb: modeDsNb || DATA_MODE_DS_NB.DRAWER,
    });
  }, [modeDsNb]);

  useEffect(() => {
    setState({
      modeDsNbLichSu: modeDsNbLichSu || DATA_MODE_DS_NB.DRAWER,
    });
  }, [modeDsNbLichSu]);

  useEffect(() => {
    searchTheoTaiKhoan({
      page: "",
      size: "",
      active: true,
      dsTinhChatKhoa: DS_TINH_CHAT_KHOA.NOI_TRU,
    });
  }, []);

  useEffect(() => {
    if (dataTongHopTheoId?.khoaChiDinhId) {
      setState({ khoaChiDinhId: dataTongHopTheoId.khoaChiDinhId });
    } else if (listAllKhoaNoiTru?.length === 1) {
      setState({ khoaChiDinhId: listAllKhoaNoiTru[0].id });
    }
  }, [listAllKhoaNoiTru, dataTongHopTheoId?.khoaChiDinhId]);

  const handleLuuPhieuPhaChe = () => {
    if (!state.khoaChiDinhId) {
      message.error(t("phaCheThuoc.thieuThongTinKhoaChiDinh"));
    }
    if (statusTicket === 2) {
      history.push(
        `/pha-che-thuoc/phieu-pha-che-thuoc/chinh-sua/${id}?nbDotDieuTriId=${nbDotDieuTriId}`
      );
    } else {
      refSaveBangPhaCheThuoc.current?.save({
        statusTicket,
        khoaChiDinhId: state.khoaChiDinhId,
      });
    }
  };
  refClickBtnSave.current = handleLuuPhieuPhaChe;

  const handleResetPhieuPhaChe = () => {
    refSaveBangPhaCheThuoc.current?.reset();
  };

  const handleClick = (action) => async () => {
    const data = [{ id: dataTongHopTheoId.id }];
    showLoading();
    try {
      switch (action) {
        case "chuyenPhaChe":
          await chuyenPhaChe(data);
          break;
        case "duyetPhaChe":
          await duyetPhaChe(data);
          break;
        case "huyDuyetPhaChe":
          await huyDuyetPhaChe(data);
          break;
        case "giaoPhaChe":
          await giaoPhaChe(data);
          break;
        case "huyGiaoPhaChe":
          await huyGiaoPhaChe(data);
          break;
        case "huyChuyenPhaChe":
          await huyChuyenPhaChe(data);
          break;
        case "phaChe":
          history.push({
            pathname: `/pha-che-thuoc/phieu-xuat-pha-che-thuoc/them-moi`,
            state: { dsNb: [dataTongHopTheoId] },
          });
          break;
        default:
          break;
      }
      await onSearchTongHopTheoId(id);
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieu = (item) => async () => {
    try {
      showLoading();
      if (item?.kySo) {
        refModalSignPrint.current &&
          refModalSignPrint.current.showToSign({
            phieuKy: item,
            payload: {
              nbDotDieuTriId: nbDotDieuTriId,
              maManHinh: "033",
              maViTri: "03301",
              id: id,
            },
          });
      } else if (item.ma === "P305") {
        await inPhieuPhaChe({ dsId: id });
      } else if (item.ma === "P306") {
        await inPhieuDichPha({ dsId: id });
      } else if (item.ma === "P307") {
        await inNhanPcHangLoat({ dsId: id, lien: "1" });
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      hideLoading();
    }
  };
  const menu = useMemo(() => {
    return (
      <Menu
        items={(state.listPhieu || []).map((item, index) => ({
          key: index,
          label: (
            <a onClick={onPrintPhieu(item)}>{item.ten || item.tenBaoCao}</a>
          ),
        }))}
      />
    );
  }, [state.listPhieu]);

  const getListPhieuInGiayTo = (e) => {
    e.stopPropagation();
    e.preventDefault();

    if (state.openDropdownPhieuIn) {
      return;
    }

    setState({ loadingListPhieu: true, openDropdownPhieuIn: true });
    getPhieuIn({
      nbDotDieuTriId,
      maManHinh: "033",
      maViTri: "03301",
    })
      .then((listPhieu) => {
        setState({ listPhieu: listPhieu });
      })
      .finally(() => {
        setState({ loadingListPhieu: false });
      });
  };

  const renderActionPhaChe = () => {
    if (statusTicket === 1) {
      return (
        checkRole([ROLES["PHA_CHE"].THEM_PHIEU_PHA_CHE_THUOC]) && (
          <Button
            type="primary"
            minWidth={100}
            iconHeight={15}
            onClick={handleLuuPhieuPhaChe}
          >
            {t("common.luuF4")}
          </Button>
        )
      );
    }
    if (statusTicket === 2) {
      return (
        <>
          <Dropdown
            overlay={menu}
            trigger={["click"]}
            open={state.openDropdownPhieuIn && !state.loadingListPhieu}
            onOpenChange={(open) => {
              setState({ openDropdownPhieuIn: open });
            }}
          >
            <Button
              id="button-in-giay-to"
              rightIcon={<SVG.IcPrint />}
              iconHeight={15}
              ref={refPhieuIn}
              onClick={getListPhieuInGiayTo}
              loading={state.loadingListPhieu}
            >
              {t("common.inPhieu")} [F3]
            </Button>
          </Dropdown>
          {checkRole([ROLES["PHA_CHE"].HUY_GIAO_PHA_CHE]) &&
            dataTongHopTheoId?.trangThai === TRANG_THAI_PHA_CHE.DA_GIAO && (
              <Button type="default" onClick={handleClick("huyGiaoPhaChe")}>
                {t("phaCheThuoc.huyGiaoPhaChe")}
              </Button>
            )}
          {checkRole([ROLES["PHA_CHE"].HUY_CHUYEN_PHA_CHE]) &&
            dataTongHopTheoId?.trangThai === TRANG_THAI_PHA_CHE.CHO_DUYET && (
              <Button type="default" onClick={handleClick("huyChuyenPhaChe")}>
                {t("phaCheThuoc.huyChuyenPhaChe")}
              </Button>
            )}
          {checkRole([ROLES["PHA_CHE"].HUY_DUYET_PHA_CHE]) &&
            dataTongHopTheoId?.trangThai === TRANG_THAI_PHA_CHE.DA_DUYET && (
              <Button type="default" onClick={handleClick("huyDuyetPhaChe")}>
                {t("phaCheThuoc.huyDuyetPhaChe")}
              </Button>
            )}
          {checkRole([ROLES["PHA_CHE"].CHUYEN_PHA_CHE]) &&
            dataTongHopTheoId?.trangThai === TRANG_THAI_PHA_CHE.TAO_MOI && (
              <Button type="primary" onClick={handleClick("chuyenPhaChe")}>
                {t("phaCheThuoc.chuyenPhaChe")}
              </Button>
            )}
          {checkRole([ROLES["PHA_CHE"].DUYET_PHA_CHE]) &&
            dataTongHopTheoId?.trangThai === TRANG_THAI_PHA_CHE.CHO_DUYET && (
              <Button type="primary" onClick={handleClick("duyetPhaChe")}>
                {t("phaCheThuoc.duyetPhaChe")}
              </Button>
            )}
          {checkRole([ROLES["PHA_CHE"].PHA_CHE]) &&
            dataTongHopTheoId?.trangThai === TRANG_THAI_PHA_CHE.DA_DUYET && (
              <Button type="primary" onClick={handleClick("phaChe")}>
                {t("phaCheThuoc.phaChe")}
              </Button>
            )}
          {checkRole([ROLES["PHA_CHE"].GIAO_PHA_CHE]) &&
            dataTongHopTheoId?.trangThai === TRANG_THAI_PHA_CHE.DA_PHA_CHE && (
              <Button type="primary" onClick={handleClick("giaoPhaChe")}>
                {t("phaCheThuoc.giaoPhaChe")}
              </Button>
            )}
          {checkRole([ROLES["PHA_CHE"].SUA_PHIEU_PHA_CHE_THUOC]) &&
            ![
              TRANG_THAI_PHA_CHE.DA_DUYET,
              TRANG_THAI_PHA_CHE.DA_PHA_CHE,
              TRANG_THAI_PHA_CHE.DA_GIAO,
            ].includes(dataTongHopTheoId?.trangThai) && (
              <Button
                type="primary"
                minWidth={100}
                iconHeight={15}
                onClick={handleLuuPhieuPhaChe}
              >
                {t("common.sua")}
              </Button>
            )}
        </>
      );
    }
    if (statusTicket === 3) {
      return (
        <>
          {checkRole([ROLES["PHA_CHE"].SUA_PHIEU_PHA_CHE_THUOC]) &&
            ![
              TRANG_THAI_PHA_CHE.DA_DUYET,
              TRANG_THAI_PHA_CHE.DA_PHA_CHE,
              TRANG_THAI_PHA_CHE.DA_GIAO,
            ].includes(dataTongHopTheoId?.trangThai) && (
              <Button
                type="primary"
                minWidth={100}
                iconHeight={15}
                onClick={handleLuuPhieuPhaChe}
              >
                {t("common.luuF4")}
              </Button>
            )}
        </>
      );
    }
  };

  const renderPhaChe = () => {
    if (
      state.modeDsNb === DATA_MODE_DS_NB.MODULE &&
      state.modeDsNbLichSu === DATA_MODE_DS_NB.MODULE
    ) {
      return (
        <Col span={6} className="dsNb">
          <DsPhieuPhaChe
            modeDsNb={state.modeDsNb}
            changeModeDsNb={changeModeDsNb}
          />
          <DsLichSuPhaChe
            modeDsNbLichSu={state.modeDsNbLichSu}
            changeModeDsNbLichSu={changeModeDsNbLichSu}
          />
        </Col>
      );
    }
    if (state.modeDsNb === DATA_MODE_DS_NB.MODULE) {
      return (
        <Col span={6} className="dsNb">
          <DsPhieuPhaChe
            modeDsNb={state.modeDsNb}
            changeModeDsNb={changeModeDsNb}
          />
        </Col>
      );
    }
    if (state.modeDsNbLichSu === DATA_MODE_DS_NB.MODULE) {
      return (
        <Col span={6} className="dsNb">
          <DsLichSuPhaChe
            modeDsNbLichSu={state.modeDsNbLichSu}
            changeModeDsNbLichSu={changeModeDsNbLichSu}
          />
        </Col>
      );
    }
  };

  return (
    <MainPage
      breadcrumb={[
        { title: t("phaCheThuoc.phaCheThuoc"), link: "/pha-che-thuoc" },
        {
          title: t("phaCheThuoc.danhSachPhieuPhaCheThuoc"),
          link: "/pha-che-thuoc/danh-sach-pha-che-thuoc",
        },
        {
          title: t("phaCheThuoc.phieuPhaCheThuoc"),
          link: `/pha-che-thuoc/phieu-pha-che-thuoc/chi-tiet/${id}?nbDotDieuTriId=${nbDotDieuTriId}`,
        },
      ]}
      style={{ pageBodyPadding: "0" }}
    >
      <div className="wrapper">
        <Row
          gutter={
            state.modeDsNb === DATA_MODE_DS_NB.MODULE ||
            state.modeDsNbLichSu === DATA_MODE_DS_NB.MODULE
              ? { xl: 8, xll: 16 }
              : 0
          }
          className="wrapper-inner"
        >
          <Col
            span={
              state.modeDsNb === DATA_MODE_DS_NB.MODULE ||
              state.modeDsNbLichSu === DATA_MODE_DS_NB.MODULE
                ? 18
                : 24
            }
            className="main"
          >
            <Card className="main-card" bottom={0}>
              <div className="main-card-inner">
                <Row>
                  <Col className="header-info" span={24}>
                    <ThongTinNb
                      openModalDsNbPhaChe={() => {
                        refModalDsNbPhaChe.current &&
                          refModalDsNbPhaChe.current.show();
                      }}
                      openDrawerDsNb={() => {
                        setState({ isDrawerDsnb: true });
                      }}
                      openDrawerDsNbLichSu={() =>
                        setState({ isDrawerLichSuPhaChe: true })
                      }
                      modeDsNb={state.modeDsNb}
                      modeDsNbLichSu={state.modeDsNbLichSu}
                      statusTicket={statusTicket}
                      setKhoaChiDinhId={(khoaChiDinhId) =>
                        setState({ khoaChiDinhId })
                      }
                    />
                  </Col>
                </Row>
                <BangPhaCheThuoc
                  ref={refSaveBangPhaCheThuoc}
                  statusTicket={statusTicket}
                />
              </div>
            </Card>
            <div className="footer-action">{renderActionPhaChe()}</div>
          </Col>
          {renderPhaChe()}
        </Row>
        <Drawer
          placement={"right"}
          closable={false}
          onClose={handleCloseDsNbDrawer}
          open={state.isDrawerDsnb}
          width={"480px"}
          bodyStyle={{ padding: "12px", paddingBottom: "0px" }}
        >
          <DsPhieuPhaChe
            modeDsNb={state.modeDsNb}
            changeModeDsNb={changeModeDsNb}
            handleCloseDrawer={handleCloseDsNbDrawer}
            handleResetPhieuPhaChe={handleResetPhieuPhaChe}
            isDrawerDsnb={state.isDrawerDsnb}
          />
        </Drawer>
        <Drawer
          placement={"right"}
          closable={false}
          onClose={handleCloseDsLichSuNbDrawer}
          open={state.isDrawerLichSuPhaChe}
          width={"480px"}
          bodyStyle={{ padding: "12px", paddingBottom: "0px" }}
        >
          <DsLichSuPhaChe
            modeDsNbLichSu={state.modeDsNbLichSu}
            changeModeDsNbLichSu={changeModeDsNbLichSu}
            handleCloseDrawer={handleCloseDsLichSuNbDrawer}
            isDrawerLichSuPhaChe={state.isDrawerLichSuPhaChe}
          />
        </Drawer>
        <ModalDsNbPhaChe
          ref={refModalDsNbPhaChe}
          handleResetPhieuPhaChe={handleResetPhieuPhaChe}
        />
        <ModalSignPrint ref={refModalSignPrint} />
      </div>
    </MainPage>
  );
};

export default PhieuPhaCheThuoc;
