import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
} from "react";
import { useTranslation } from "react-i18next";
import { Input, Form } from "antd";
import { Checkbox, CreatedWrapper, Select } from "components";
import { checkRole } from "lib-utils/role-utils";
import { selectMaTen } from "redux-store/selectors";
import { ROLES } from "constants/index";

const FormProtocol = (
  { onCancel, editStatus, listAllDVPTTT, handleSaveForm },
  ref
) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const refAutoFocus = useRef(null);
  const formRef = useRef();

  const protocolId = Form.useWatch("id", form);

  useImperativeHandle(ref, () => ({
    form,
    refAutoFocus,
    resetFields: () => {
      form.resetFields();
      if (refAutoFocus.current) {
        refAutoFocus.current.focus();
      }
    },
  }));

  useEffect(() => {
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
  }, [protocolId]);

  return (
    <>
      <CreatedWrapper
        title={t("common.thongTinChiTiet")}
        onCancel={onCancel}
        cancelText={t("common.huy")}
        onOk={handleSaveForm}
        okText={t("common.luuF4")}
        roleSave={[ROLES["DANH_MUC"].PROTOCOL_THEM]}
        roleEdit={[ROLES["DANH_MUC"].PROTOCOL_SUA]}
        editStatus={editStatus}
      >
        <fieldset
          disabled={
            editStatus
              ? !checkRole([ROLES["DANH_MUC"].PROTOCOL_SUA])
              : !checkRole([ROLES["DANH_MUC"].PROTOCOL_THEM])
          }
        >
          <Form
            ref={formRef}
            form={form}
            layout="vertical"
            style={{ width: "100%" }}
            className="form-custom--one-line"
          >
            <Form.Item name="id" hidden>
              <Input hidden />
            </Form.Item>

            <Form.Item
              label={t("danhMuc.maMauProtocol")}
              name="ma"
              rules={[
                {
                  required: true,
                  message: `${t("danhMuc.vuiLongNhapMaMauProtocol")}!`,
                },
                {
                  whitespace: true,
                  message: t("danhMuc.vuiLongNhapMaMauProtocol") + "!",
                },
              ]}
            >
              <Input
                ref={refAutoFocus}
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapMaMauProtocol")}
                autoComplete="off"
              />
            </Form.Item>
            <Form.Item
              label={t("danhMuc.tenPhieu")}
              name="ten"
              rules={[
                {
                  required: true,
                  message: `${t("danhMuc.vuiLongNhapTenMauProtocol")}!`,
                },
                {
                  whitespace: true,
                  message: t("danhMuc.vuiLongNhapTenMauProtocol") + "!",
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapTenMauProtocol")}
              />
            </Form.Item>
            <Form.Item label={t("danhMuc.dichVuApDung")} name="dsDichVuId">
              <Select
                className="input-option"
                placeholder={t("danhMuc.chonDichVuApDung")}
                data={listAllDVPTTT}
                getLabel={selectMaTen}
                mode="multiple"
                allowClear
              />
            </Form.Item>
            {protocolId && (
              <Form.Item name="active" valuePropName="checked">
                <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
              </Form.Item>
            )}
          </Form>
        </fieldset>
      </CreatedWrapper>
    </>
  );
};

export default forwardRef(FormProtocol);
