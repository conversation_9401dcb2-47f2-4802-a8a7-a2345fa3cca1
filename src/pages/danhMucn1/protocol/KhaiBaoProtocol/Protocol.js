import React, { useState, useImperativeHandle, forwardRef } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Checkbox,
  Pagination,
  HeaderSearch,
  TableWrapper,
  Select,
  Button,
  EllipsisText,
  InputTimeout,
} from "components";
import {
  ROLES,
  PAGE_DEFAULT,
  PAGE_SIZE,
  HIEU_LUC,
  SORT_DEFAULT,
} from "constants/index";
import { combineSort } from "utils";
import { checkRole } from "lib-utils/role-utils";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { useRefFunc } from "hooks";
import dmProtocolProvider from "data-access/categories/dm-protocol-provider";

const Protocol = forwardRef(
  (
    {
      onEditProtocol,
      handleChangeshowTable,
      showFullTable,
      collapseStatus,
      handleCollapsePane,
      listAllDVPTTT,
      handleClickBtnAdd,
      setEditStatus,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [dataEditDefault, setDataEditDefault] = useState(null);

    const [page, setPage] = useState(PAGE_DEFAULT);
    const [size, setSize] = useState(PAGE_SIZE);
    const [totalElements, setTotalElements] = useState(0);
    const [sortData, setSortData] = useState(SORT_DEFAULT);
    const [dataSearch, setDataSearch] = useState({ active: true });

    const {
      protocol: { onExport, onImport },
    } = useDispatch();

    const { data: searchResult, refetch } = useQuery({
      queryKey: [
        "protocol",
        {
          page,
          size,
          sortData,
          dataSearch,
        },
      ],
      queryFn: () =>
        dmProtocolProvider.search({
          page,
          size,
          sort: combineSort(sortData),
          ...dataSearch,
        }),
      onSuccess: (data) => {
        setTotalElements(data.totalElements || 0);
      },
    });

    const listProtocol = searchResult?.data || [];

    const handleSelectRow = useRefFunc((index) => {
      const indexNextItem =
        (listProtocol?.findIndex((item) => item.id === dataEditDefault?.id) ||
          0) + index;
      if (-1 < indexNextItem && indexNextItem < listProtocol.length) {
        setEditStatus(true);
        setDataEditDefault(listProtocol[indexNextItem]);
        onEditProtocol(listProtocol[indexNextItem]);
        document
          .getElementsByClassName(
            "row-id-" + listProtocol[indexNextItem]?.id
          )[0]
          .scrollIntoView({ block: "end", behavior: "smooth" });
      }
    });

    useImperativeHandle(ref, () => ({
      handleSelectRow,
      setDataEditDefault,
      refetch,
    }));

    const onClickSort = (key, value) => {
      const sort = { ...sortData, [key]: value };
      setSortData(sort);
      setPage(PAGE_DEFAULT);
    };

    const onSearchInput = (key) => (value) => {
      setDataSearch({ ...dataSearch, [key]: value });
      setPage(PAGE_DEFAULT);
    };

    const onSizeChange = (newSize) => {
      setSize(newSize);
      setPage(PAGE_DEFAULT);
    };

    const onPageChange = (newPage) => {
      setPage(newPage - 1);
    };

    const columns = [
      {
        title: <HeaderSearch title={t("common.stt")} />,
        width: 40,
        dataIndex: "index",
        key: "index",
        align: "center",
        render: (_, __, index) => {
          return page * size + index + 1;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.maMauProtocol")}
            sort_key="ma"
            onClickSort={onClickSort}
            dataSort={sortData["ma"] || 0}
            search={
              <InputTimeout
                placeholder={t("danhMuc.timMaMauProtocol")}
                onChange={onSearchInput("ma")}
              />
            }
          />
        ),
        width: 100,
        dataIndex: "ma",
        key: "ma",
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.tenMauProtocol")}
            sort_key="ten"
            onClickSort={onClickSort}
            dataSort={sortData.ten || 0}
            search={
              <InputTimeout
                placeholder={t("danhMuc.timTenMauProtocol")}
                onChange={onSearchInput("ten")}
              />
            }
          />
        ),
        width: 160,
        dataIndex: "ten",
        key: "ten",
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.dichVuApDung")}
            searchSelect={
              <Select
                placeholder={t("danhMuc.timDichVuApDung")}
                data={listAllDVPTTT}
                value={dataSearch?.dsDichVuId}
                onChange={onSearchInput("dsDichVuId")}
              />
            }
          />
        ),
        width: 200,
        dataIndex: "dsDichVu",
        key: "dsDichVu",
        render: (dsDichVu) => {
          if (!Array.isArray(dsDichVu)) return null;

          const content = dsDichVu.map((dv) => dv.ten).join(", ");

          return (
            <EllipsisText.Tooltip
              tooltipPlacement="right"
              limitLine={3}
              content={content}
            />
          );
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.coHieuLuc")}
            sort_key="active"
            onClickSort={onClickSort}
            dataSort={sortData.active || 0}
            searchSelect={
              <Select
                defaultValue="true"
                data={HIEU_LUC}
                placeholder={t("danhMuc.chonHieuLuc")}
                onChange={onSearchInput("active")}
                hasAllOption={true}
              />
            }
          />
        ),
        width: 90,
        dataIndex: "active",
        key: "active",
        align: "center",
        render: (item) => {
          return <Checkbox checked={item} onClick={() => {}} />;
        },
      },
    ];

    const onRow = (record, index) => ({
      onClick: (event) => {
        setEditStatus(true);
        setDataEditDefault(record);
        onEditProtocol(record);
      },
    });

    return (
      <div>
        <TableWrapper
          titleUtilities={t("danhMuc.khaiBaoProtocolPTTT")}
          classNameRow={"custom-header"}
          styleMain={{ marginTop: 0 }}
          styleContainerButtonHeader={{
            display: "flex",
            width: "100%",
            justifyContent: "flex-end",
            alignItems: "center",
            paddingRight: 35,
          }}
          onExport={onExport}
          onImport={() => {
            return new Promise((resolve, reject) => {
              onImport()
                .then((s) => {
                  resolve(s);
                  refetch();
                })
                .catch(reject);
            });
          }}
          buttonHeader={
            checkRole([ROLES["DANH_MUC"].PROTOCOL_THEM])
              ? [
                  {
                    content: (
                      <Button
                        type="success"
                        onClick={handleClickBtnAdd}
                        rightIcon={<SVG.IcAdd />}
                      >
                        {t("common.themMoiF1")}
                      </Button>
                    ),
                  },
                  {
                    className: `btn-change-full-table ${
                      showFullTable ? "small" : "large"
                    }`,
                    title: showFullTable ? (
                      <SVG.IcShowThuNho />
                    ) : (
                      <SVG.IcShowFull />
                    ),
                    onClick: handleChangeshowTable,
                  },

                  {
                    className: "btn-collapse",
                    title: collapseStatus ? (
                      <SVG.IcExtend />
                    ) : (
                      <SVG.IcCollapse />
                    ),
                    onClick: handleCollapsePane,
                  },
                ]
              : [
                  {
                    className: `btn-change-full-table ${
                      showFullTable ? "small" : "large"
                    }`,
                    title: showFullTable ? (
                      <SVG.IcShowThuNho />
                    ) : (
                      <SVG.IcShowFull />
                    ),
                    onClick: handleChangeshowTable,
                  },

                  {
                    className: "btn-collapse",
                    title: collapseStatus ? (
                      <SVG.IcExtend />
                    ) : (
                      <SVG.IcCollapse />
                    ),
                    onClick: handleCollapsePane,
                  },
                ]
          }
          columns={columns}
          dataSource={listProtocol}
          onRow={onRow}
          dataEditDefault={dataEditDefault}
        />
        {!!totalElements && (
          <Pagination
            onChange={onPageChange}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            listData={listProtocol}
            onShowSizeChange={onSizeChange}
          />
        )}
      </div>
    );
  }
);

export default Protocol;
