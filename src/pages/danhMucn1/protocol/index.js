import React, { useEffect, useState, useRef } from "react";
import { Col, Tabs } from "antd";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import BaseDm2 from "pages/danhMuc/BaseDm2";
import { useLoading, useQueryAll, useStore, useRefFunc, useLayer } from "hooks";
import { safeConvertToArray } from "utils";
import { toSafePromise } from "lib-utils";
import MultiLevelTab from "components/MultiLevelTab";
import {
  ADD_LAYOUT_COLLAPSE,
  TABLE_LAYOUT_COLLAPSE,
  ADD_LAYOUT,
  TABLE_LAYOUT,
  ROLES,
  LOAI_DICH_VU,
  HOTKEY,
} from "constants/index";
import { query } from "redux-store/stores";
import FormProtocolTenTruong from "./ProtocolChung/FormProtocolChung";
import ProtocolChung from "./ProtocolChung/ProtocolChung";
import FormProtocol from "./KhaiBaoProtocol/FormProtocol";
import ThietLapProtocol from "./KhaiBaoProtocol/ThietLapProtocol";
import Protocol from "./KhaiBaoProtocol/Protocol";
import { Main } from "./styled";

const { TabPane } = Tabs;

const DmProtocol = () => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const layerId = useLayer();
  const { onRegisterHotkey } = useDispatch().phimTat;

  const [activeTab, setActiveTab] = useState("0");
  const [editProtocol, setEditProtocol] = useState(null);
  const [editStatus, setEditStatus] = useState(false);
  const [collapseStatus, setCollapseStatus] = useState(false);

  const refFormProtocolTenTruong = useRef(null);
  const refFormProtocol = useRef(null);
  const refTab = useRef(null);
  const refProtocolChung = useRef(null);
  const refProtocol = useRef(null);

  const [state, _setState] = useState({
    showFullTable: false,
    activeKeyTab: "1",
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const {
    protocolTenTruong: { createOrEditProtocolTenTruong },
    protocol: { createOrEditProtocol },
    dichVuKyThuat: { getAll: getAllDichVuPTTT },
  } = useDispatch();

  const {
    data: listAllProtocolTenTruong,
    refetch: refetchListAllProtocolTenTruong,
  } = useQueryAll(
    query.protocolTenTruong.queryAllProtocolTenTruong({
      persist: false,
      cacheTime: 0,
      staleTime: 0,
    })
  );

  const listAllDVPTTT = useStore("dichVuKyThuat.listAllDVPTTT", []);

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.UP,
          onEvent: (e) => {
            handleSelectRow(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN,
          onEvent: (e) => {
            handleSelectRow(1);
          },
        },
        {
          keyCode: HOTKEY.F1,
          onEvent: () => {
            handleClickBtnAdd();
          },
        },
        {
          keyCode: HOTKEY.F4,
          onEvent: () => {
            handleSaveForm();
          },
        },
      ],
    });
  }, []);

  useEffect(() => {
    if (!listAllDVPTTT?.length) {
      getAllDichVuPTTT({
        "dichVu.loaiDichVu": LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
      });
    }
  }, [listAllDVPTTT]);

  const onChangeTab = (key) => {
    setActiveTab(`${parseInt(key, 10)}`);
  };

  const handleChangeshowTable = () => {
    setState({
      changeShowFullTbale: true,
      showFullTable: !state.showFullTable,
    });
    setTimeout(() => {
      setState({
        changeShowFullTbale: false,
      });
    }, 1000);
  };

  const handleCollapsePane = () => {
    setCollapseStatus(!collapseStatus);
    setState({
      changeCollapseStatus: true,
      collapseStatus: !state.collapseStatus,
    });
    setTimeout(() => {
      setState({
        changeCollapseStatus: false,
      });
    }, 500);
  };

  const onEditProtocolTenTruong = (info) => {
    refFormProtocolTenTruong.current?.form?.setFieldsValue({
      ...info,
    });
  };

  const onEditProtocol = (info) => {
    const normalizedInfo = {
      ...info,
      dsDichVuId: safeConvertToArray(info.dsDichVuId),
    };

    setEditProtocol(normalizedInfo);

    if (refFormProtocol.current) {
      refFormProtocol.current.form?.setFieldsValue(normalizedInfo);
    }
  };

  const addNewProtocolTenTruong = (values) => {
    showLoading();

    createOrEditProtocolTenTruong({ ...values })
      .then(() => {
        refFormProtocolTenTruong.current?.form?.resetFields();
        refProtocolChung.current?.refetch();
        refetchListAllProtocolTenTruong();
      })
      .catch((err) => console.error(err))
      .finally(() => {
        hideLoading();
      });
  };

  const addNewProtocol = (values) => {
    showLoading();

    createOrEditProtocol({ ...values })
      .then(() => {
        refFormProtocol.current.resetFields();
        refProtocol.current?.refetch();
      })
      .catch((err) => console.error(err))
      .finally(() => {
        hideLoading();
      });
  };
  const handleClickBtnAdd = useRefFunc(() => {
    if (activeTab === "0") {
      refFormProtocolTenTruong.current?.resetFields();
      refProtocolChung.current?.setDataEditDefault(null);
      setEditStatus(false);
    } else if (activeTab === "1") {
      refFormProtocol.current?.resetFields();
      refProtocol.current?.setDataEditDefault(null);
      setState({
        activeKeyTab: "1",
      });
      setEditProtocol(null);
      setEditStatus(false);
    }
  });

  const handleSaveForm = useRefFunc(async () => {
    if (activeTab === "0") {
      const [err, values] = await toSafePromise(
        refFormProtocolTenTruong.current?.form?.validateFields()
      );
      if (!err) {
        addNewProtocolTenTruong(values);
      }
    } else if (activeTab === "1") {
      const [err, values] = await toSafePromise(
        refFormProtocol.current?.form?.validateFields()
      );
      if (!err) {
        addNewProtocol(values);
      }
    }
  });

  const handleSelectRow = useRefFunc((direction) => {
    if (activeTab === "0") {
      refProtocolChung.current?.handleSelectRow(direction);
    } else if (activeTab === "1") {
      refProtocol.current?.handleSelectRow(direction);
    }
  });

  const listPanel = [
    {
      key: 1,
      title: t("common.thongTinChiTiet"),
      render: () => {
        return (
          <FormProtocol
            ref={refFormProtocol}
            listAllDVPTTT={listAllDVPTTT}
            handleSaveForm={handleSaveForm}
            editStatus={editStatus}
          />
        );
      },
    },
    {
      key: 2,
      title: t("danhMuc.thietLapProtocol"),
      render: () => {
        return (
          <ThietLapProtocol
            protocolId={editProtocol?.id}
            roleSave={[ROLES["DANH_MUC"].PROTOCOL_THEM]}
            roleEdit={[ROLES["DANH_MUC"].PROTOCOL_SUA]}
          />
        );
      },
    },
  ];

  return (
    <Main>
      <BaseDm2
        breadcrumb={[
          { title: t("danhMuc.title"), link: "/danh-muc" },
          {
            title: t("danhMuc.danhMucKhaiBaoMauProtocolPttt"),
            link: "/danh-muc/protocol",
          },
        ]}
        title={t("danhMuc.danhMucKhaiBaoMauProtocolPttt")}
      >
        <Col
          {...(!state.showFullTable
            ? state.collapseStatus
              ? TABLE_LAYOUT_COLLAPSE
              : TABLE_LAYOUT
            : null)}
          className={`pr-3 ${
            state.changeCollapseStatus ? "transition-ease" : ""
          }`}
        >
          <Tabs
            activeKey={activeTab}
            defaultActiveKey={activeTab}
            onChange={onChangeTab}
            className="table-tab"
          >
            <TabPane tab={t("danhMuc.danhMucMaHoaProtocolChung")} key="0">
              <ProtocolChung
                ref={refProtocolChung}
                showFullTable={state.showFullTable}
                collapseStatus={state.collapseStatus}
                handleChangeshowTable={handleChangeshowTable}
                handleCollapsePane={handleCollapsePane}
                onEditProtocolTenTruong={onEditProtocolTenTruong}
                handleClickBtnAdd={handleClickBtnAdd}
                setEditStatus={setEditStatus}
              />
            </TabPane>
            <TabPane tab={t("danhMuc.khaiBaoProtocolPTTT")} key="1">
              <Protocol
                ref={refProtocol}
                showFullTable={state.showFullTable}
                collapseStatus={state.collapseStatus}
                handleChangeshowTable={handleChangeshowTable}
                handleCollapsePane={handleCollapsePane}
                listAllDVPTTT={listAllDVPTTT}
                onEditProtocol={onEditProtocol}
                handleClickBtnAdd={handleClickBtnAdd}
                setEditStatus={setEditStatus}
              />
            </TabPane>
          </Tabs>
        </Col>
        {!state.showFullTable && (
          <Col
            {...(state.collapseStatus ? ADD_LAYOUT_COLLAPSE : ADD_LAYOUT)}
            className={`mt-3  ${
              state.changeCollapseStatus ? "transition-ease" : ""
            }`}
            style={
              state.isSelected
                ? { border: "2px solid #c1d8fd", borderRadius: 20 }
                : {}
            }
          >
            {activeTab === "0" && (
              <>
                <FormProtocolTenTruong
                  ref={refFormProtocolTenTruong}
                  listAllProtocolTenTruong={listAllProtocolTenTruong}
                  handleSaveForm={handleSaveForm}
                  editStatus={editStatus}
                />
              </>
            )}
            {activeTab === "1" && (
              <MultiLevelTab
                className="tab-dm-protocol"
                ref={refTab}
                listPanel={listPanel}
                isBoxTabs={true}
                activeKey={state.activeKeyTab}
                onChange={(activeKeyTab) => setState({ activeKeyTab })}
              />
            )}
          </Col>
        )}
      </BaseDm2>
    </Main>
  );
};

export default DmProtocol;
