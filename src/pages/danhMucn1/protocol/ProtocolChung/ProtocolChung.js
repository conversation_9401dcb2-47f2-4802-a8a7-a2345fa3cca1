import React, { useState, useImperativeHandle, forwardRef } from "react";
import {
  Checkbox,
  Pagination,
  HeaderSearch,
  TableWrapper,
  Select,
  Button,
  InputTimeout,
} from "components";
import {
  PAGE_DEFAULT,
  PAGE_SIZE,
  HIEU_LUC,
  ROLES,
  HOTKEY,
  SORT_DEFAULT,
} from "constants/index";
import { combineSort } from "utils";
import { checkRole } from "lib-utils/role-utils";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { selectMaTen } from "redux-store/selectors";
import { useLazyKVMap, useQueryAll, useRefFunc } from "hooks";
import { query } from "redux-store/stores";
import dmProtocolTenTruongProvider from "data-access/categories/dm-protocol-ten-truong-provider";

const ProtocolTenTruong = forwardRef(
  (
    {
      onEditProtocolTenTruong,
      handleChangeshowTable,
      showFullTable,
      collapseStatus,
      handleCollapsePane,
      handleClickBtnAdd,
      setEditStatus,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [dataEditDefault, setDataEditDefault] = useState(null);

    const [page, setPage] = useState(PAGE_DEFAULT);
    const [size, setSize] = useState(PAGE_SIZE);
    const [totalElements, setTotalElements] = useState(0);
    const [sortData, setSortData] = useState(SORT_DEFAULT);
    const [dataSearch, setDataSearch] = useState({ active: true });

    const {
      protocolTenTruong: { onExport, onImport },
    } = useDispatch();

    const handleSelectRow = useRefFunc((index) => {
      const indexNextItem =
        (listProtocolTenTruong?.findIndex(
          (item) => item.id === dataEditDefault?.id
        ) || 0) + index;
      if (-1 < indexNextItem && indexNextItem < listProtocolTenTruong.length) {
        setEditStatus(true);
        setDataEditDefault(listProtocolTenTruong[indexNextItem]);
        onEditProtocolTenTruong(listProtocolTenTruong[indexNextItem]);
        document
          .getElementsByClassName(
            "row-id-" + listProtocolTenTruong[indexNextItem]?.id
          )[0]
          .scrollIntoView({ block: "end", behavior: "smooth" });
      }
    });

    const { data: listAllProtocolTenTruong } = useQueryAll(
      query.protocolTenTruong.queryAllProtocolTenTruong
    );

    const [getProtocolTenTruong] = useLazyKVMap(listAllProtocolTenTruong);

    const { data: searchResult, refetch } = useQuery({
      queryKey: [
        "protocolTenTruong",
        {
          page,
          size,
          sortData,
          dataSearch,
        },
      ],
      queryFn: () =>
        dmProtocolTenTruongProvider.search({
          page,
          size,
          sort: combineSort(sortData),
          ...dataSearch,
        }),
      onSuccess: (data) => {
        setTotalElements(data.totalElements || 0);
      },
    });

    useImperativeHandle(ref, () => ({
      handleSelectRow,
      setDataEditDefault,
      refetch,
    }));

    const listProtocolTenTruong = searchResult?.data || [];

    const onClickSort = (key, value) => {
      const sort = { ...sortData, [key]: value };
      setSortData(sort);
      setPage(PAGE_DEFAULT);
    };

    const onSearchInput = (key) => (value) => {
      setDataSearch({ ...dataSearch, [key]: value });
      setPage(PAGE_DEFAULT);
    };

    const onSizeChange = (newSize) => {
      setSize(newSize);
      setPage(PAGE_DEFAULT);
    };

    const onPageChange = (newPage) => {
      setPage(newPage - 1);
    };

    const columns = [
      {
        title: <HeaderSearch title={t("common.stt")} />,
        width: 50,
        dataIndex: "index",
        key: "index",
        align: "center",
        render: (_, __, index) => {
          return page * size + index + 1;
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.maTruong")}
            sort_key="ma"
            onClickSort={onClickSort}
            dataSort={sortData.ma || 0}
            search={
              <InputTimeout
                placeholder={t("danhMuc.timMaTruong")}
                onChange={onSearchInput("ma")}
              />
            }
          />
        ),
        width: 120,
        dataIndex: "ma",
        key: "ma",
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.tenNoiDungTinhChat")}
            sort_key="ten"
            onClickSort={onClickSort}
            dataSort={sortData.ten || 0}
            search={
              <InputTimeout
                placeholder={t("danhMuc.timTenNoiDungTinhChat")}
                onChange={onSearchInput("ten")}
              />
            }
          />
        ),
        width: 180,
        dataIndex: "ten",
        key: "ten",
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.capCha")}
            searchSelect={
              <Select
                placeholder={t("danhMuc.timCapCha")}
                data={listAllProtocolTenTruong}
                getLabel={selectMaTen}
                onChange={onSearchInput("tenTruongChaId")}
              />
            }
          />
        ),
        width: 180,
        dataIndex: "tenTruongChaId",
        key: "tenTruongChaId",
        render: (item) => {
          const obj = getProtocolTenTruong(item);
          return obj ? selectMaTen(obj) : "";
        },
      },
      {
        title: (
          <HeaderSearch
            title={t("danhMuc.coHieuLuc")}
            sort_key="active"
            onClickSort={onClickSort}
            dataSort={sortData.active || 0}
            searchSelect={
              <Select
                defaultValue="true"
                data={HIEU_LUC}
                placeholder={t("danhMuc.chonHieuLuc")}
                onChange={onSearchInput("active")}
                hasAllOption={true}
              />
            }
          />
        ),
        width: 90,
        dataIndex: "active",
        key: "active",
        align: "center",
        render: (item) => {
          return <Checkbox checked={item} onClick={() => {}} />;
        },
      },
    ];

    const onRow = (record, index) => ({
      onClick: (event) => {
        setEditStatus(true);
        onEditProtocolTenTruong(record);
        setDataEditDefault(record);
      },
    });

    return (
      <div>
        <TableWrapper
          titleUtilities={t("danhMuc.danhMucMaHoaProtocolChung")}
          classNameRow={"custom-header"}
          styleMain={{ marginTop: 0 }}
          styleContainerButtonHeader={{
            display: "flex",
            width: "100%",
            justifyContent: "flex-end",
            alignItems: "center",
            paddingRight: 35,
          }}
          onExport={onExport}
          onImport={() => {
            return new Promise((resolve, reject) => {
              onImport({
                id: LOAI_DICH_VU.PROTOCOL_TEN_TRUONG,
                ten: t("danhMuc.danhMucMaHoaProtocolChung"),
              })
                .then((s) => {
                  resolve(s);
                  refetch();
                })
                .catch(reject);
            });
          }}
          buttonHeader={
            checkRole([ROLES["DANH_MUC"].PROTOCOL_THEM])
              ? [
                  {
                    content: (
                      <Button
                        type="success"
                        onClick={handleClickBtnAdd}
                        rightIcon={<SVG.IcAdd />}
                      >
                        {t("common.themMoiF1")}
                      </Button>
                    ),
                  },
                  {
                    className: `btn-change-full-table ${
                      showFullTable ? "small" : "large"
                    }`,
                    title: showFullTable ? (
                      <SVG.IcShowThuNho />
                    ) : (
                      <SVG.IcShowFull />
                    ),
                    onClick: handleChangeshowTable,
                  },

                  {
                    className: "btn-collapse",
                    title: collapseStatus ? (
                      <SVG.IcExtend />
                    ) : (
                      <SVG.IcCollapse />
                    ),
                    onClick: handleCollapsePane,
                  },
                ]
              : [
                  {
                    className: `btn-change-full-table ${
                      showFullTable ? "small" : "large"
                    }`,
                    title: showFullTable ? (
                      <SVG.IcShowThuNho />
                    ) : (
                      <SVG.IcShowFull />
                    ),
                    onClick: handleChangeshowTable,
                  },

                  {
                    className: "btn-collapse",
                    title: collapseStatus ? (
                      <SVG.IcExtend />
                    ) : (
                      <SVG.IcCollapse />
                    ),
                    onClick: handleCollapsePane,
                  },
                ]
          }
          columns={columns}
          dataSource={listProtocolTenTruong}
          onRow={onRow}
          dataEditDefault={dataEditDefault}
        />
        {!!totalElements && (
          <Pagination
            onChange={onPageChange}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            listData={listProtocolTenTruong}
            onShowSizeChange={onSizeChange}
          />
        )}
      </div>
    );
  }
);

export default ProtocolTenTruong;
