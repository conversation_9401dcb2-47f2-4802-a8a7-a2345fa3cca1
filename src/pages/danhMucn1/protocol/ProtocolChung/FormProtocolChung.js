import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>andle,
  useRef,
} from "react";
import { Input, Form } from "antd";
import { Checkbox, CreatedWrapper, Select } from "components";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants/index";
import { useTranslation } from "react-i18next";
import { selectMaTen } from "redux-store/selectors";

const FormProtocolTenTruong = (
  { onCancel, editStatus, listAllProtocolTenTruong, handleSaveForm },
  ref
) => {
  const { t } = useTranslation();
  const refAutoFocus = useRef(null);
  const [form] = Form.useForm();

  const protocolId = Form.useWatch("id", form);

  useImperativeHandle(ref, () => ({
    form,
    refAutoFocus,
    resetFields: () => {
      form.resetFields();
      if (refAutoFocus.current) {
        refAutoFocus.current.focus();
      }
    },
  }));

  useEffect(() => {
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
  }, [protocolId]);

  return (
    <>
      <CreatedWrapper
        title={t("common.thongTinChiTiet")}
        onCancel={onCancel}
        cancelText={t("common.huy")}
        onOk={handleSaveForm}
        okText={t("common.luuF4")}
        roleSave={[ROLES["DANH_MUC"].PROTOCOL_THEM]}
        roleEdit={[ROLES["DANH_MUC"].PROTOCOL_SUA]}
        editStatus={editStatus}
      >
        <fieldset
          disabled={
            editStatus
              ? !checkRole([ROLES["DANH_MUC"].PROTOCOL_SUA])
              : !checkRole([ROLES["DANH_MUC"].PROTOCOL_THEM])
          }
        >
          <Form
            form={form}
            layout="vertical"
            style={{ width: "100%" }}
            className="form-custom--one-line"
          >
            <Form.Item name="id" hidden>
              <Input />
            </Form.Item>
            <Form.Item label={t("danhMuc.maTruong")} name="ma">
              <Input disabled className="input-option" />
            </Form.Item>
            <Form.Item
              label={t("danhMuc.tenNoiDungTinhChat")}
              name="ten"
              rules={[
                {
                  required: true,
                  message: t("danhMuc.vuiLongNhapTenNoiDungTinhChat") + "!",
                },
                {
                  max: 255,
                  message: t(
                    "danhMuc.vuiLongNhapTenNoiDungTinhChatKhongQuaNumKyTu",
                    { num: 255 }
                  ),
                },
                {
                  whitespace: true,
                  message: t("danhMuc.vuiLongNhapTenNoiDungTinhChat") + "!",
                },
              ]}
            >
              <Input
                className="input-option"
                placeholder={t("danhMuc.vuiLongNhapTenNoiDungTinhChat")}
              />
            </Form.Item>
            <Form.Item label={t("danhMuc.capCha")} name="tenTruongChaId">
              <Select
                className="input-option"
                placeholder={t("danhMuc.vuiLongChonCapCha")}
                data={listAllProtocolTenTruong?.filter(
                  (item) => item.id !== protocolId
                )}
                getLabel={selectMaTen}
                getValue={(option) => option.id}
              />
            </Form.Item>
            {protocolId && (
              <Form.Item name="active" valuePropName="checked">
                <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
              </Form.Item>
            )}
          </Form>
        </fieldset>
      </CreatedWrapper>
    </>
  );
};

export default forwardRef(FormProtocolTenTruong);
