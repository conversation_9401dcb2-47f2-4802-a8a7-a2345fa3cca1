import React, { useMemo } from "react";
import { CreatedWrapper, Pagination, TableWrapper } from "components";
import T from "prop-types";
import { useTranslation } from "react-i18next";
import StyleDm from "../StyleDm";
import classNames from "classnames";

const StyleDm1 = ({
  title,
  rightTitle,
  layerId,
  listData,
  onSizeChange,
  totalElements,
  page,
  size,
  dataEditDefault,
  onExport,
  onImport,
  breadcrumb,
  columns,
  rightContent,
  onAdd,
  onCancel,
  onSave,
  onClickItem,
  onChangePage,
  cancelText,
  okText,
  roleSave,
  roleEdit,
  editStatus,
  disabledBtnOk,
  isShowSync,
  listAllDataSync,
  titleSync,
  apiSync,
  loadingBtnSave,
  isLoading,
  onGetTemplate,
  menuCustom,
}) => {
  const { t } = useTranslation();
  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        onClickItem(record, index);
      },
    };
  };

  const arrColumns = useMemo(() => {
    if (!columns) return [];
    return columns;
  }, [columns]);

  const setRowClassName = (record) => {
    const isActive = !!record.id && record.id === dataEditDefault?.id;
    return classNames(`row-id-${layerId}_${record.id}`, {
      ["row-actived"]: isActive,
      ["row-gray"]: !isActive && !record.active,
    });
  };

  return (
    <StyleDm
      breadcrumb={breadcrumb}
      title={title}
      onExport={onExport}
      onImport={onImport}
      onAdd={onAdd}
      isShowSync={isShowSync}
      listAllDataSync={listAllDataSync}
      titleSync={titleSync}
      apiSync={apiSync}
      onGetTemplate={onGetTemplate}
      menuCustom={menuCustom}
      leftPanel={
        <>
          <TableWrapper
            showHeaderTable={false}
            scroll={{ x: 1000 }}
            classNameRow={"custom-header"}
            columns={arrColumns}
            dataSource={listData}
            onRow={onRow}
            layerId={layerId}
            dataEditDefault={dataEditDefault}
            rowClassName={setRowClassName}
            loading={!!isLoading}
          />
          {!!totalElements ? (
            <Pagination
              onChange={onChangePage}
              current={page + 1}
              listData={listData}
              pageSize={size}
              total={totalElements}
              onShowSizeChange={onSizeChange}
            />
          ) : null}
        </>
      }
      rightPanel={
        <CreatedWrapper
          title={rightTitle || t("danhMuc.thongTinChiTiet")}
          onCancel={onCancel}
          cancelText={cancelText || t("common.huy")}
          onOk={onSave}
          okText={okText || t("common.luuF4")}
          roleSave={roleSave}
          roleEdit={roleEdit}
          editStatus={editStatus}
          layerId={layerId}
          disabledBtnOk={disabledBtnOk}
          loading={loadingBtnSave}
        >
          {rightContent}
        </CreatedWrapper>
      }
    />
  );
};
StyleDm1.defaultProps = {
  onSizeChange: () => {},
  onClickItem: () => {},
  onChangePage: (page) => {},
  onCancel: () => {},
  onSave: () => {},
  columns: [],
};

StyleDm1.propTypes = {
  onSizeChange: T.func,
  onClickItem: T.func,
  onChangePage: T.func,
  onCancel: T.func,
  onSave: T.func,
  columns: T.array,
};

export default StyleDm1;
