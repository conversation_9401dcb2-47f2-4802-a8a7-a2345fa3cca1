import React, { useState, useRef, useMemo } from "react";
import { Col, Menu } from "antd";
import { Button, TableWrapper, Tooltip, Dropdown } from "components";
import T from "prop-types";
import {
  ADD_LAYOUT_COLLAPSE,
  TABLE_LAYOUT_COLLAPSE,
  TABLE_LAYOUT,
  ADD_LAYOUT,
} from "constants/index";
import { useTranslation } from "react-i18next";
import { Main } from "./styled";
import { SVG } from "assets";
import ModalSyncward from "components/ModalSyncward";
import { useLoading, useWindowSize } from "hooks";
const { ModalImport } = TableWrapper;

const StyleDm = ({
  title,
  onExport,
  onImport,
  breadcrumb,
  onAdd,
  isShowSync,
  listAllDataSync,
  titleSync,
  apiSync,
  leftPanel,
  rightPanel,
  onGetTemplate,
  menuCustom,
}) => {
  const { t } = useTranslation();
  const windowSize = useWindowSize();
  const refModalImport = useRef(null);
  const refModalSyncward = useRef(null);
  const { showLoading, hideLoading } = useLoading();
  const [state, _setState] = useState({
    isShowFullTable: false,
    isCollapse: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const onCollapse = () => {
    setState({ isCollapse: !state.isCollapse });
  };

  const onShowFullTable = () => {
    setState({
      changeShowFullTbale: true,
      isShowFullTable: !state.isShowFullTable,
    });
    setTimeout(() => {
      setState({
        changeShowFullTbale: false,
      });
    }, 1000);
  };

  const onClickImport = () => {
    refModalImport.current &&
      refModalImport.current.show({ isModalVisible: true });
  };
  const onClickExport = () => {
    if (onExport) {
      showLoading();
      onExport().finally(() => hideLoading());
    }
  };
  const onSyncnIward = () => {
    refModalSyncward.current &&
      refModalSyncward.current.show({
        data: listAllDataSync,
        title: titleSync,
      });
  };

  const menu = () => (
    <Menu
      items={[
        ...(onImport
          ? [
              {
                key: 1,
                label: (
                  <a onClick={onClickImport}>
                    <div
                      className="flex icon_utilities gap-8"
                      style={{ alignItems: "center" }}
                    >
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: title,
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onExport
          ? [
              {
                key: 2,
                label: (
                  <a onClick={onClickExport}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>
                        {t("danhMuc.xuatDuLieuTitle", {
                          title: title,
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(isShowSync && apiSync
          ? [
              {
                key: 3,
                label: (
                  <a onClick={onSyncnIward}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcReload width={20} height={20} />
                      <span>{t("danhMuc.dongBoIward")}</span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onGetTemplate
          ? [
              {
                key: 4,
                label: (
                  <a onClick={onGetTemplate}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>{t("tiepDon.taiFileMau")}</span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(menuCustom ? menuCustom : []),
      ]}
    />
  );

  const isShowTienIch = useMemo(() => {
    return !!onExport || !!onImport || (!!apiSync && isShowSync);
  }, [onExport, onImport, isShowSync, apiSync]);

  return (
    <Main breadcrumb={breadcrumb}>
      <Col
        {...(!state.isShowFullTable
          ? state.isCollapse
            ? TABLE_LAYOUT_COLLAPSE
            : TABLE_LAYOUT
          : null)}
        span={state.isShowFullTable ? 24 : null}
        className={`pr-3 ${state.changeShowFullTbale ? "" : "transition-ease"}`}
      >
        <div className="danh-muc-header">
          <div className="title">{title}</div>
          <div className="header-action">
            {isShowTienIch && (
              <Dropdown
                overlay={menu}
                trigger={"click"}
                placement="bottomLeft"
                overlayClassName="danh-muc-dropdown-tien-ich"
              >
                <Button rightIcon={<SVG.IcMore />} height={28}>
                  {t("common.tienIch")}
                </Button>
              </Dropdown>
            )}
            {onAdd && (
              <Button type="success" onClick={onAdd} rightIcon={<SVG.IcAdd />}>
                {t("common.themMoiF1")}
              </Button>
            )}
            {state.isShowFullTable ? (
              <Tooltip title={t("common.thuNho")}>
                <SVG.IcShowThuNho
                  className="header-action-icon"
                  onClick={onShowFullTable}
                />
              </Tooltip>
            ) : (
              <Tooltip title={t("common.moRong")}>
                <SVG.IcMoRong
                  className="header-action-icon"
                  onClick={onShowFullTable}
                />
              </Tooltip>
            )}
            {state.isCollapse ? (
              <Tooltip title={t("common.moRong")}>
                <SVG.IcExtend
                  className="header-action-icon"
                  onClick={onCollapse}
                />
              </Tooltip>
            ) : (
              <Tooltip title={t("common.thuGon")}>
                <SVG.IcCollapse
                  className="header-action-icon"
                  onClick={onCollapse}
                />
              </Tooltip>
            )}
          </div>
        </div>
        {leftPanel}
      </Col>
      {!state.isShowFullTable && windowSize.width >= 1200 ? (
        <Col
          {...(state.isCollapse ? ADD_LAYOUT_COLLAPSE : ADD_LAYOUT)}
          className={`mt-3 ${
            state.changeShowFullTbale ? "" : "transition-ease"
          }`}
          style={
            state.isSelected
              ? { border: "2px solid #c1d8fd", borderRadius: 20 }
              : {}
          }
        >
          {rightPanel}
        </Col>
      ) : (
        <Col {...ADD_LAYOUT} className={"mt-3 transition-ease"}>
          {rightPanel}
        </Col>
      )}
      {!!onImport && <ModalImport ref={refModalImport} onImport={onImport} />}
      {!!apiSync && isShowSync && (
        <ModalSyncward ref={refModalSyncward} onApi={apiSync} />
      )}
    </Main>
  );
};
StyleDm.defaultProps = {
  onSizeChange: () => {},
  onClickItem: () => {},
  onChangePage: (page) => {},
  onCancel: () => {},
  onSave: () => {},
  columns: [],
};

StyleDm.propTypes = {
  onSizeChange: T.func,
  onClickItem: T.func,
  onChangePage: T.func,
  onCancel: T.func,
  onSave: T.func,
  columns: T.array,
};

export default StyleDm;
