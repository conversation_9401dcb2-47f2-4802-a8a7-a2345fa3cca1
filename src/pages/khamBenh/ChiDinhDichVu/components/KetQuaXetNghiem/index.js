import React, { useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";

import { useEnum, useThietLap } from "hooks";

import { Popover } from "components";
import { <PERSON>t<PERSON><PERSON><PERSON>rapper, PopoverContent } from "./styled";
import {
  CSS_CLASSES,
  POPOVER_CONFIG,
  PHAN_LOAI_TO_CSS_CLASS_MOI,
  PHAN_LOAI_TO_CSS_CLASS_CU,
} from "./constants";
import { ENUM, THIET_LAP_CHUNG } from "constants/index";

const KetQuaXetNghiem = ({
  data = {},
  isNbThieuTien = false,
  isXetNghiem = false,
  showKetLuan = true,
}) => {
  const { t } = useTranslation();
  const [dataHIEN_THI_KET_QUA_XN] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_KET_QUA_XN
  );
  const [listPhanLoaiKetQuaXetNghiem] = useEnum(
    ENUM.PHAN_LOAI_KET_QUA_XET_NGHIEM
  );
  const { row = {} } = data;

  const phanLoaiKetQua = useMemo(() => {
    if (!listPhanLoaiKetQuaXetNghiem?.length || !row?.phanLoaiKetQua) {
      return null;
    }

    return listPhanLoaiKetQuaXetNghiem.find(
      (item) => row.phanLoaiKetQua === item?.id
    )?.id;
  }, [listPhanLoaiKetQuaXetNghiem, row?.phanLoaiKetQua]);

  const getCssClassByPhanLoai = useCallback(
    (phanLoai, isUseNewStyle = false) => {
      const mapping = isUseNewStyle
        ? PHAN_LOAI_TO_CSS_CLASS_MOI
        : PHAN_LOAI_TO_CSS_CLASS_CU;
      return mapping[phanLoai] || CSS_CLASSES.DEFAULT;
    },
    []
  );

  // Tạo giá trị tham chiếu từ chiSoThap và chiSoCao để so sánh với kết quả
  const giaTriThamChieu = useMemo(() => {
    const { chiSoThap, chiSoCao, ketQuaThamChieu } = row;

    return {
      thamChieu:
        ketQuaThamChieu &&
        typeof ketQuaThamChieu === "string" &&
        ketQuaThamChieu.trim()
          ? ketQuaThamChieu.trim()
          : "",
      chiSoThap: parseFloat(chiSoThap) || null,
      chiSoCao: parseFloat(chiSoCao) || null,
    };
  }, [row]);

  // Xác định phân loại kết quả dựa trên so sánh với giá trị tham chiếu
  const phanLoaiTuDong = useMemo(() => {
    // Chỉ thực hiện so sánh khi thiết lập HIEN_THI_KET_QUA_XN = true
    if (!dataHIEN_THI_KET_QUA_XN?.eval()) {
      return null;
    }

    const { ketQua } = row;
    const ketQuaNumber = parseFloat(ketQua);
    const { chiSoThap, chiSoCao, thamChieu } = giaTriThamChieu;

    // Các trường hợp trả về BINH_THUONG (0):
    // - Không có giá trị tham chiếu
    // - Kết quả không phải số hoặc có tham chiếu string
    // - Kết quả nằm trong khoảng bình thường
    if (
      (!chiSoThap && !chiSoCao) ||
      isNaN(ketQuaNumber) ||
      thamChieu ||
      (chiSoThap &&
        chiSoCao &&
        ketQuaNumber >= chiSoThap &&
        ketQuaNumber <= chiSoCao)
    ) {
      return 0; // BINH_THUONG
    }

    // Kết quả > giá trị tham chiếu cao → CAO (20)
    if (chiSoCao && ketQuaNumber > chiSoCao) {
      return 20; // CAO
    }

    // Kết quả < giá trị tham chiếu thấp → THAP (10)
    if (chiSoThap && ketQuaNumber < chiSoThap) {
      return 10; // THAP
    }

    // Mặc định trả về BINH_THUONG
    return 0;
  }, [row, giaTriThamChieu, dataHIEN_THI_KET_QUA_XN]);

  const displayContent = useMemo(() => {
    const { ketQua, ketLuan } = row;

    if (!isXetNghiem) {
      return ketQua || "";
    }

    // Với xét nghiệm: hiển thị "kết quả - kết luận"
    const ketQuaText = ketQua || "";
    const ketLuanText = ketLuan || "";

    if (!ketQuaText && !ketLuanText) return "";
    if (!ketLuanText) return ketQuaText;
    if (!ketQuaText) return ketLuanText;
    // Nếu showKetLuan=false thì chỉ hiển thị kết quả
    if (!showKetLuan) return ketQuaText;

    return `${ketQuaText} - ${ketLuanText}`;
  }, [row, isXetNghiem]);

  const cssClasses = useMemo(() => {
    const classes = [CSS_CLASSES.BASE];

    // Xác định có sử dụng style mới không
    const isUseNewStyle = dataHIEN_THI_KET_QUA_XN?.eval();

    // Ưu tiên sử dụng phân loại tự động từ so sánh giá trị tham chiếu khi HIEN_THI_KET_QUA_XN=true
    // Nếu không có thì dùng phân loại từ db
    const phanLoaiSuDung =
      phanLoaiTuDong !== null ? phanLoaiTuDong : phanLoaiKetQua;

    if (phanLoaiSuDung !== null && phanLoaiSuDung !== undefined) {
      classes.push(getCssClassByPhanLoai(phanLoaiSuDung, isUseNewStyle));
    }

    if (isNbThieuTien) {
      classes.push(CSS_CLASSES.BLUR);
    }

    return classes.join(" ");
  }, [
    phanLoaiTuDong,
    phanLoaiKetQua,
    isNbThieuTien,
    getCssClassByPhanLoai,
    dataHIEN_THI_KET_QUA_XN,
  ]);

  const ResultContent = useCallback(
    () => (
      <KetQuaWrapper
        className={cssClasses}
        dangerouslySetInnerHTML={{ __html: displayContent }}
      />
    ),
    [cssClasses, displayContent]
  );

  // Nếu người bệnh thiếu tiền, hiển thị với Popover cảnh báo
  if (isNbThieuTien) {
    return (
      <Popover
        content={
          <PopoverContent>
            {t("khamBenh.nguoiBenhThieuTienCanDiThanhToan")}
          </PopoverContent>
        }
        placement={POPOVER_CONFIG.PLACEMENT}
        trigger={POPOVER_CONFIG.TRIGGER}
      >
        <ResultContent />
      </Popover>
    );
  }

  return <ResultContent />;
};

export default KetQuaXetNghiem;
