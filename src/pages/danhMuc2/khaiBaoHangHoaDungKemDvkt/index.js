import React, { useEffect, useMemo, useRef } from "react";
import { Form, Input, InputNumber } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";

import { useEnum, useListAll, useQueryAll, useStore, useThietLap } from "hooks";
import { checkRole } from "lib-utils/role-utils";

import { Checkbox, Select, TableWrapper } from "components";
import BaseDmWrap from "components/BaseDmWrap";
import { ENUM, LOAI_DICH_VU, ROLES, THIET_LAP_CHUNG } from "constants/index";
import { Main } from "../styled";
import { isArray } from "utils/index";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { SVG } from "assets";
import ModalChinhSuaHangLoatHangHoaDungKem from "./ModalChinhSuaHangLoatHangHoaDungKem";

const { ColumnInput, ColumnSelect, ColumnCheckbox } = TableWrapper;

const params = { page: "", size: "", active: true, sort: "ten,asc" };

const LIST_LOAI_DV_CHINH = [
  LOAI_DICH_VU.XET_NGHIEM,
  LOAI_DICH_VU.CDHA,
  LOAI_DICH_VU.KHAM,
  LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
  LOAI_DICH_VU.NGOAI_DIEU_TRI,
];

const LIST_LOAI_DV_DUNG_KEM = [
  10, 20, 30, 40, 50, 60, 70, 90, 95, 100, 110, 120, 130, 270,
];

const paramsDichVu = {
  active: true,
  page: "",
  size: "",
  dsLoaiDichVu: LIST_LOAI_DV_CHINH,
};

const KhaiBaoHangHoaDungKemDvkt = () => {
  const { t } = useTranslation();

  const [listloaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const [listThoiDiemChiDinh] = useEnum(ENUM.THOI_DIEM_CHI_DINH);

  const listAllDichVuApDung = useStore("dichVu.listAllDichVuApDung", []);
  const listAllDichKemTheo = useStore("dichVu.listAllDichKemTheo", []);
  const [listAllKho] = useListAll("kho", {}, true);
  const listAllLoaiDoiTuong = useStore("loaiDoiTuong.listAllLoaiDoiTuong", []);
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const [dataDV_KHONG_LEN_PHIEU_THU] = useThietLap(
    THIET_LAP_CHUNG.DV_KHONG_LEN_PHIEU_THU
  );
  const refModalChinhSuaHangLoat = useRef(null);

  const {
    kho: { getListAllKho },
    loaiDoiTuong: { getListAllLoaiDoiTuong },
    dichVu: { getListAllDichVuTongHop },
  } = useDispatch();

  useEffect(() => {
    getListAllKho(params);
    getListAllLoaiDoiTuong(params);
    getListAllDichVuTongHop(paramsDichVu, {
      storeKey: "listAllDichVuApDung",
    });
    getListAllDichVuTongHop(
      { ...paramsDichVu, dsLoaiDichVu: LIST_LOAI_DV_DUNG_KEM },
      { storeKey: "listAllDichKemTheo" }
    );
  }, []);

  const listloaiDichVuMemo = useMemo(() => {
    return listloaiDichVu.filter((x) => LIST_LOAI_DV_CHINH.includes(x.id));
  }, [listloaiDichVu]);

  const getColumns = ({ baseColumns = {}, ...rest }) => [
    baseColumns.stt,
    ColumnInput({
      ...rest,
      title: t("danhMuc.maDvDungKem"),
      dataIndex: "dichVuKemTheo.ma",
      width: 120,
      render: (_, data) => data.dichVuKemTheo?.ma,
    }),
    ColumnInput({
      ...rest,
      title: t("danhMuc.tenDvDungKem"),
      dataIndex: "dichVuKemTheo.ten",
      width: 150,
      render: (_, data) => data.dichVuKemTheo?.ten,
    }),
    ColumnSelect({
      ...rest,
      title: t("common.loaiDichVu"),
      dataIndex: "dichVuKemTheo.loaiDichVu",
      searchKey: "dichVuKemTheo.loaiDichVu",
      width: 120,
      dataSelect: listloaiDichVuMemo,
      render: (_, data) =>
        listloaiDichVu.find((x) => x.id === data.dichVuKemTheo?.loaiDichVu)
          ?.ten,
    }),
    ColumnInput({
      ...rest,
      title: t("common.donViTinh"),
      dataIndex: "dichVuKemTheo.tenDonViTinh",
      width: 150,
      render: (_, data) => data.dichVuKemTheo?.tenDonViTinh,
    }),
    ColumnSelect({
      ...rest,
      title: t("danhMuc.maDichVuApDung"),
      dataIndex: "dichVuChinhId",
      searchKey: "dichVuChinhId",
      width: 120,
      dataSelect: listAllDichKemTheo,
      getLabel: (item) => item.ma,
      render: (item, data) => listAllDichKemTheo.find((x) => x.id === item)?.ma,
    }),
    ColumnSelect({
      ...rest,
      title: t("danhMuc.tenDichVuApDung"),
      dataIndex: "dichVuChinhId",
      searchKey: "dichVuChinhId",
      width: 150,
      dataSelect: listAllDichKemTheo,
      render: (item, data) =>
        listAllDichKemTheo.find((x) => x.id === item)?.ten,
    }),
    ColumnSelect({
      ...rest,
      title: t("danhMuc.thoiDiemChiDinh"),
      dataIndex: "thoiDiemChiDinh",
      searchKey: "thoiDiemChiDinh",
      width: 150,
      dataSelect: listThoiDiemChiDinh,
    }),
    ColumnSelect({
      ...rest,
      title: t("danhMuc.loaiDoiTuong"),
      dataIndex: "dsLoaiDoiTuongId",
      searchKey: "dsLoaiDoiTuongId",
      selectSearch: true,
      width: 150,
      dataSelect: listAllLoaiDoiTuong,
      render: (item) => {
        if (isArray(item, true) && isArray(listAllLoaiDoiTuong, true)) {
          let doiTuong = item.reduce((acc, cur) => {
            let curIdx = listAllLoaiDoiTuong.findIndex((i) => i.id === cur);
            if (curIdx > -1) {
              acc.push(listAllLoaiDoiTuong[curIdx].ten);
            }
            return acc;
          }, []);
          return doiTuong.join(", ");
        }
        return null;
      },
    }),
    ColumnInput({
      ...rest,
      title: t("danhMuc.soLuong"),
      dataIndex: "soLuong",
      width: 100,
    }),
    ColumnInput({
      ...rest,
      title: t("common.cachDung"),
      dataIndex: "cachDung",
      width: 150,
    }),
    ColumnSelect({
      ...rest,
      title: t("danhMuc.khoaChiDinh"),
      dataIndex: "dsKhoaChiDinhId",
      searchKey: "dsKhoaChiDinhId",
      selectSearch: true,
      width: 150,
      dataSelect: listAllKhoa,
      render: (item) => {
        if (isArray(item, true) && isArray(listAllKhoa, true)) {
          let khoa = item.reduce((acc, cur) => {
            let curIdx = listAllKhoa.findIndex((i) => i.id === cur);
            if (curIdx > -1) {
              acc.push(listAllKhoa[curIdx].ten);
            }
            return acc;
          }, []);
          return khoa.join(", ");
        }
        return null;
      },
    }),
    ColumnSelect({
      ...rest,
      title: t("title.kho"),
      dataIndex: "khoId",
      searchKey: "khoId",
      width: 150,
      dataSelect: listAllKho,
    }),
    ColumnCheckbox({
      title: t("danhMuc.khongLenPhieuThu"),
      dataIndex: "khongThuTien",
      width: 80,
      hidden: !dataDV_KHONG_LEN_PHIEU_THU?.eval(),
      ...rest,
    }),
    ColumnCheckbox({
      title: t("common.tuTra"),
      dataIndex: "tuTra",
      width: 80,
      ...rest,
    }),
    ColumnCheckbox({
      title: t("danhMuc.khongTinhTien"),
      dataIndex: "khongTinhTien",
      width: 80,
      ...rest,
    }),
    baseColumns.active,
  ];

  const renderForm = ({ editStatus }) => {
    return (
      <>
        <Form.Item
          label={t("danhMuc.tenDichVuDungKem")}
          name="dichVuKemTheoId"
          rules={[
            {
              required: true,
              message: t("common.vuiLongChonDichVu"),
            },
          ]}
        >
          <Select
            className="input-option"
            placeholder={t("danhMuc.vuiLongChonTenDichVuDungKem")}
            data={listAllDichKemTheo}
            getLabel={selectMaTen}
          />
        </Form.Item>
        <Form.Item
          label={t("danhMuc.tenDichVuApDung")}
          name="dichVuChinhId"
          rules={[
            {
              required: true,
              message: t("danhMuc.vuiLongChonDichVuApDung"),
            },
          ]}
        >
          <Select
            className="input-option"
            placeholder={t("danhMuc.chonDichVuApDung")}
            data={listAllDichVuApDung}
            getLabel={selectMaTen}
          />
        </Form.Item>
        <Form.Item label={t("danhMuc.thoiDiemChiDinh")} name="thoiDiemChiDinh">
          <Select
            className="input-option"
            placeholder={t("danhMuc.chonThoiDiemChiDinh")}
            data={listThoiDiemChiDinh}
          />
        </Form.Item>
        <Form.Item label={t("danhMuc.loaiDoiTuong")} name="dsLoaiDoiTuongId">
          <Select
            className="input-option"
            placeholder={t("danhMuc.chonLoaiDoiTuong")}
            data={listAllLoaiDoiTuong}
            mode="multiple"
          />
        </Form.Item>
        <Form.Item
          label={t("danhMuc.soLuong")}
          name="soLuong"
          rules={[
            {
              required: true,
              message: t("danhMuc.vuiLongNhapSoLuong"),
            },
          ]}
        >
          <InputNumber
            placeholder={t("danhMuc.nhapSoLuong")}
            style={{ width: "100%" }}
            min={0}
            type="number"
            precision={2}
          />
        </Form.Item>
        <Form.Item label={t("common.cachDung")} name="cachDung">
          <Input placeholder={t("quanLyNoiTru.toDieuTri.nhapCachDung")} />
        </Form.Item>
        <Form.Item label={t("danhMuc.khoaChiDinh")} name="dsKhoaChiDinhId">
          <Select
            className="input-option"
            data={listAllKhoa}
            mode="multiple"
            placeholder={t("danhMuc.chonKhoaChiDinh")}
          />
        </Form.Item>
        <Form.Item label={t("title.kho")} name="khoId">
          <Select placeholder={t("kho.chonKho")} data={listAllKho} />
        </Form.Item>
        <Form.Item name="tuTra" label="" valuePropName="checked">
          <Checkbox>{t("common.tuTra")}</Checkbox>
        </Form.Item>
        <Form.Item name="khongTinhTien" label="" valuePropName="checked">
          <Checkbox>{t("danhMuc.khongTinhTien")}</Checkbox>
        </Form.Item>
        {dataDV_KHONG_LEN_PHIEU_THU?.eval() && (
          <Form.Item name="khongThuTien" label="" valuePropName="checked">
            <Checkbox>{t("danhMuc.khongLenPhieuThu")}</Checkbox>
          </Form.Item>
        )}
        {editStatus && (
          <Form.Item name="active" label="" valuePropName="checked">
            <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
          </Form.Item>
        )}
      </>
    );
  };

  const onClickEdit = () => {
    refModalChinhSuaHangLoat.current?.show({}, () => {
      //
    });
  };

  const menuCustom = [
    ...(checkRole([ROLES["DANH_MUC"].CAP_NHAT_NHIEU_HANG_HOA_DUNG_KEM_DVKT])
      ? [
          {
            key: "cap-nhat-hang-hoa-dung-kem-dvkt",
            label: (
              <a onClick={onClickEdit}>
                <div className="flex icon_utilities gap-8">
                  <SVG.IcEdit />
                  <span>{t("danhMuc.chinhSuaHangHoaDungKemHangLoat")}</span>
                </div>
              </a>
            ),
          },
        ]
      : []),
  ];

  return (
    <Main>
      <BaseDmWrap
        getColumns={getColumns}
        renderForm={renderForm}
        menuCustom={menuCustom}
        titleTable={t("danhMuc.danhMucKhaiBaoHangHoaDungKemDvkt")}
        roleName="KHAI_BAO_HANG_HOA_DUNG_KEM_DVKT"
        storeName="khaiBaoHangHoaDungKemDvkt"
        listLink={[
          {
            title: t("danhMuc.danhMuc"),
            link: "/danh-muc",
          },
          {
            title: t("danhMuc.khaiBaoHangHoaDungKemDvkt"),
            link: "/danh-muc/khai-bao-hang-hoa-dung-kem-dvkt",
          },
        ]}
      />
      <ModalChinhSuaHangLoatHangHoaDungKem ref={refModalChinhSuaHangLoat} />
    </Main>
  );
};

export default KhaiBaoHangHoaDungKemDvkt;
