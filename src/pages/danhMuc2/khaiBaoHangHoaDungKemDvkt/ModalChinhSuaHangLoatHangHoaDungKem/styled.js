import styled from "styled-components";

export const Main = styled.div`
  padding: 10px;
  overflow: hidden;
  height: 85vh;
  display: flex;
  flex-direction: column;

  .wrap-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }

  .header-filter {
    margin-bottom: 10px;
    padding: 0;
    .item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 5px;
      label {
        min-width: 160px;
      }
      .ant-select {
        width: 100%;
      }
    }
  }

  .table-wrapper {
    flex: 1;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    gap: 8px;
  }

  .input-number-right input {
    direction: RTL;
    text-align: right;
    unicode-bidi: plaintext;
  }
`;
