import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
} from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Form, InputNumber, message } from "antd";
import classNames from "classnames";
import { cloneDeep, isEmpty } from "lodash";
import { useQuery } from "@tanstack/react-query";

import {
  useConfirm,
  useEnum,
  useListAll,
  useLoading,
  useQueryAll,
  useStore,
} from "hooks";

import {
  isArray,
  handleBlurInput,
  handleKeypressInput,
  isNumber,
  containText,
} from "utils/index";
import { Main } from "./styled";
import {
  Button,
  ModalTemplate,
  Select,
  SelectLoadMore,
  TableWrapper,
  HeaderSearch,
  InputTimeout,
  Pagination,
  Tooltip,
  Checkbox,
  BaseSearch,
  Card,
} from "components";
import { ENUM, HOTKEY } from "constants/index";
import dichVuKemTheoProvider from "data-access/categories/dm-dv-kem-theo-provider";
import { query } from "redux-store/stores";

function ModalChinhSuaHangLoatHangHoaDungKem(props, ref) {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const [state, _setState] = useState({
    dataSource: [],
    totalElements: 0,
    page: 0,
    size: 20,
    dataSearch: {
      dichVuDungKemId: null,
    },
    show: false,
  });
  const [totalElements, setTotalElements] = useState(0);
  const { showLoading, hideLoading } = useLoading();

  const listAllDichKemTheo = useStore("dichVu.listAllDichKemTheo", []);
  const listAllLoaiDoiTuong = useStore("loaiDoiTuong.listAllLoaiDoiTuong", []);
  const { data: listAllKhoa } = useQueryAll(
    query.khoa.queryAllKhoa({
      enabled: state.show,
    })
  );
  const [listThoiDiemChiDinh] = useEnum(ENUM.THOI_DIEM_CHI_DINH);
  const [listAllKho] = useListAll("kho", {}, state.show);

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useImperativeHandle(ref, () => ({
    show: ({}) => {
      setState({ show: true });
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const params = {
    page: state.page,
    size: state.size,
    ...state.dataSearch,
  };

  const { data, isLoading } = useQuery({
    queryKey: ["danhSachNBDieuTriDaiHan", params],
    queryFn: () => {
      return dichVuKemTheoProvider.search(params);
    },
    onSuccess: (data) => {
      setTotalElements(data?.totalElements || 0);
    },
    enabled: state.show && !!state.dataSearch?.dichVuDungKemId,
  });

  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onOk(false)();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        onOk(true)();
      },
    },
  ];

  const onChangePage = (page) => {
    setState({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setState({ size, page: 0 });
  };

  const onHandleSubmit = async () => {
    if (!state.dichVuKemTheoId) {
      message.error(t("danhMuc.vuiLongChonHangHoaDungKemCanChinhSua"));
      return;
    }
    if (!state.dichVuChinhId) {
      message.error(t("danhMuc.vuiLongChonHangHoaDungKemApDungMoi"));
      return;
    }
    if (state.dichVuKemTheoId === state.dichVuChinhId) {
      message.error(t("danhMuc.hangHoaDungKemCanChinhSuaVaApDungMoiKhacNhau"));
      return;
    }
    try {
      showLoading();
      //
    } catch (error) {
      console.error(error?.message || error);
    } finally {
      hideLoading();
    }
  };

  const onOk = (isOk) => () => {
    if (isOk) {
      onHandleSubmit();
    } else {
      setState({ show: false });
    }
  };

  const renderDsColumn = (item, listData) => {
    if (isArray(item, true) && isArray(listData, true)) {
      let curItem = item.reduce((acc, cur) => {
        let curIdx = listData.findIndex((i) => i.id === cur);
        if (curIdx > -1) {
          acc.push(listData[curIdx].ten);
        }
        return acc;
      }, []);
      return curItem.join(", ");
    }
    return null;
  };

  const columns = [
    {
      title: (
        <HeaderSearch title={t("common.stt")} className="flex flex-center" />
      ),
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.tenHangHoa")}
          className="flex flex-center"
        />
      ),
      width: 150,
      dataIndex: "dichVuChinhId",
      key: "dichVuChinhId",
      align: "left",
      i18Name: "kho.tenHangHoa",
      show: true,
      render: (item) => listAllDichKemTheo.find((x) => x.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch title={t("kho.maHangHoa")} className="flex flex-center" />
      ),
      width: 120,
      dataIndex: "dichVuChinhId",
      key: "dichVuChinhId",
      align: "left",
      i18Name: "kho.maHangHoa",
      show: true,
      render: (item) => listAllDichKemTheo.find((x) => x.id === item)?.ma,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.soLuong")}
          className="flex flex-center"
        />
      ),
      width: 100,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "left",
      i18Name: "danhMuc.soLuong",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.thoiDiemChiDinh")}
          className="flex flex-center"
        />
      ),
      width: 150,
      dataIndex: "thoiDiemChiDinh",
      key: "thoiDiemChiDinh",
      align: "left",
      i18Name: "danhMuc.thoiDiemChiDinh",
      show: true,
      render: (item) => {
        return listThoiDiemChiDinh.find((x) => x.id === item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.loaiDoiTuong")}
          className="flex flex-center"
        />
      ),
      width: 100,
      dataIndex: "dsLoaiDoiTuongId",
      key: "dsLoaiDoiTuongId",
      align: "left",
      i18Name: "danhMuc.loaiDoiTuong",
      show: true,
      render: (item) => renderDsColumn(item, listAllLoaiDoiTuong),
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.khoaChiDinh")}
          className="flex flex-center"
        />
      ),
      width: 100,
      dataIndex: "dsKhoaChiDinhId",
      key: "dsKhoaChiDinhId",
      align: "left",
      i18Name: "danhMuc.khoaChiDinh",
      show: true,
      render: (item) => renderDsColumn(item, listAllKhoa),
    },
    {
      title: (
        <HeaderSearch title={t("title.kho")} className="flex flex-center" />
      ),
      width: 150,
      dataIndex: "khoId",
      key: "khoId",
      align: "left",
      i18Name: "title.kho",
      show: true,
      render: (item) => {
        return listAllKho.find((x) => x.id === item)?.ten || "";
      },
    },
  ];

  console.log("🚀 KhoaMilan -> state", state);

  return (
    <>
      <ModalTemplate
        width={"85%"}
        ref={refModal}
        title={t("danhMuc.chinhSuaHangHoaDungKemHangLoat")}
        hotKeys={hotKeys}
        onCancel={onOk(false)}
        actionRight={
          <>
            <Button minWidth={100} iconHeight={15} onClick={onOk(false)}>
              {t("common.huy")}
            </Button>
            <Button
              type="primary"
              minWidth={100}
              iconHeight={15}
              onClick={onOk(true)}
              disabled={isLoading}
            >
              {t("common.xacNhan")}
            </Button>
          </>
        }
        destroyOnClose
      >
        <Main>
          <Card>
            <h1 className="title">{t("danhMuc.thongTinHangHoaChinhSua")}</h1>
            <div className="wrap-column">
              <div>
                <div>{t("danhMuc.hangHoaDungKemCanChinhSua")}</div>
                <SelectLoadMore
                  api={dichVuKemTheoProvider.search}
                  mapData={(i) => ({
                    value: i.dichVuDungKemId,
                    label: i.dichVuKemTheo?.ten,
                  })}
                  placeholder={t("common.chonDichVu")}
                  onChange={(data) => {
                    console.log("🚀 KhoaMilan -> data", data);
                    setState({
                      dichVuDungKemId: data,
                      dataSearch: {
                        ...state.dataSearch,
                        dichVuDungKemId: data,
                      },
                    });
                  }}
                  value={state.dichVuDungKemId ?? ""}
                  style={{ width: "100%" }}
                  keySearch={"dichVuKemTheo.ten"}
                />
              </div>
              <div>
                <div>{t("danhMuc.hangHoaDungKemApDungMoi")}</div>
                <SelectLoadMore
                  api={dichVuKemTheoProvider.search}
                  mapData={(i) => ({
                    value: i.dichVuDungKemId,
                    label: i.dichVuKemTheo?.ten,
                  })}
                  placeholder={t("common.chonDichVu")}
                  onChange={(data) => {
                    setState({ dichVuChinhId: data });
                  }}
                  value={state.dichVuChinhId}
                  style={{ width: "100%" }}
                  keySearch={"dichVuKemTheo.ten"}
                />
              </div>
            </div>
          </Card>
          <div className="header-filter">
            <div className="flex">
              <BaseSearch
                cacheData={state.dataSearch}
                dataInput={[
                  {
                    widthInput: "232px",
                    placeholder: t("common.tenDichVu"),
                    keyValueInput: "dichVuChinhId",
                    functionChangeInput: (data) => {
                      setState({
                        dataSearch: {
                          ...state.dataSearch,
                          dichVuChinhId: data.dichVuChinhId,
                        },
                      });
                    },
                    type: "select",
                    value: state.dichVuChinhId,
                    listSelect: listAllDichKemTheo,
                    putToQuerry: false,
                  },
                ]}
                filter={{
                  open: true,
                  width: "110px",
                  data: [],
                }}
              />
            </div>
          </div>
          <div className="table-wrapper">
            <TableWrapper
              columns={columns}
              dataSource={data?.data || []}
              isLoading={isLoading}
            />
            <Pagination
              listData={data?.data || []}
              onChange={onChangePage}
              current={state.page + 1}
              pageSize={state.size}
              total={totalElements}
              onShowSizeChange={handleSizeChange}
            />
          </div>
        </Main>
      </ModalTemplate>
    </>
  );
}

export default forwardRef(ModalChinhSuaHangLoatHangHoaDungKem);
