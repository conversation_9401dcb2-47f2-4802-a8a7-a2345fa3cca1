import React, {
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import { Input } from "antd";
import {
  DatePicker,
  HeaderSearch,
  Select,
  TableWrapper,
  Pagination,
  Button,
  SelectLoadMore,
  InputTimeout,
} from "components";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import { lowerFirst } from "lodash";
import { useEnum, useListAll, useThietLap } from "hooks";
import moment from "moment";
import dmKhoProvider from "data-access/categories/dm-kho-provider";
import {
  ENUM,
  LOAI_NHAP_XUAT,
  TRANG_THAI_THUOC,
  THIET_LAP_CHUNG,
} from "constants/index";
import { SVG } from "assets";
import { GlobalStyle } from "./styled";
import { isArray, parseListConfig } from "utils/index";

const { Setting } = TableWrapper;

const addParam = { active: true };
const { RangePicker } = DatePicker;
const DanhSach = (
  {
    linkDetail,
    khoaChiDinhId,
    isModal = false,
    onGoToDetail = () => {},
    onPrintPhieu = () => {},
  },
  ref
) => {
  const refTimeOut = useRef();
  const [dataLOAI_NHAP_XUAT_BS_DS_PHIEU_LINH] = useThietLap(
    THIET_LAP_CHUNG.LOAI_NHAP_XUAT_BS_DS_PHIEU_LINH
  );

  const [state, _setState] = useState({
    dataSortColumn: {},
    listCheckId: [],
    param: {
      page: 0,
      size: 10,
      dsLoaiNhapXuat: [80, 85, 88],
    },
  });
  const { dataSortColumn } = state;
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };
  const { t } = useTranslation();
  const { dsPhieuNhapXuat, totalElements } = useSelector(
    (state) => state.phieuNhapXuat
  );
  const [listTrangThaiPhieuNhapXuat] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);
  const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);
  const [listLoaiNhapXuat] = useEnum(ENUM.LOAI_NHAP_XUAT, []);
  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);
  const [listAllKhoa] = useListAll("khoa", { active: true }, true);
  const [listAllMaPhieuLinh] = useListAll("maPhieuLinh", {}, true);
  const listTrangThaiDls = Object.values(TRANG_THAI_THUOC);
  const [listloaiNhapXuat] = useEnum(ENUM.LOAI_NHAP_XUAT);

  const {
    phieuNhapXuat: { getListPhieu },
  } = useDispatch();

  const onGetListPhieu = (params) => {
    let listLoaiNx = parseListConfig(dataLOAI_NHAP_XUAT_BS_DS_PHIEU_LINH, true);
    getListPhieu({
      ...params,
      dsTrangThai: params.dsTrangThai ? [params.dsTrangThai] : undefined,
      dsKhoId: params.dsKhoId ? [params.dsKhoId] : undefined,
      dsKhoDoiUngId: params.dsKhoDoiUngId ? [params.dsKhoDoiUngId] : undefined,
      ...(isArray(listLoaiNx, 1)
        ? { dsLoaiNhapXuat: listLoaiNx }
        : { dsLoaiNhapXuat: params.dsLoaiNhapXuat }),
    });
  };

  const refSettings = useRef(null);

  useImperativeHandle(ref, () => ({
    showTableSetting: () => {
      onSettings();
    },
  }));

  useEffect(() => {
    setState({
      param: {
        ...state.param,
        dsKhoaChiDinhId: khoaChiDinhId ? [khoaChiDinhId] : null,
      },
    });
  }, [khoaChiDinhId]);

  const onClickSort = (key, value) => {
    const sort = { ...dataSortColumn, [key]: value };
    setState({ dataSortColumn: sort });
  };

  const onSearchInput = (key) => (e) => {
    if (refTimeOut.current) {
      clearTimeout(refTimeOut.current);
      refTimeOut.current = null;
    }
    refTimeOut.current = setTimeout(
      (key, e) => {
        console.log(e, "e.target");
        let value = e?.hasOwnProperty("_d")
          ? moment(e._d).format("YYYY-MM-DD")
          : e?.value
          ? e?.value
          : e;
        let newParam = {
          ...state.param,
          page: 0,
          [key]: value,
        };

        setState({ param: newParam });
        onGetListPhieu(newParam);
      },
      500,
      key,
      e?.target || e
    );
  };

  const history = useHistory();

  const onRow = (record) => {
    return {
      onClick: (e) => {
        if (isModal) return;
        if (e.target.nodeName !== "INPUT") {
          history.push(linkDetail + record.id);
        }
      },
    };
  };

  const onChangePage = (page) => {
    const newParam = {
      ...state.param,
      page: page - 1,
    };
    setState({ param: newParam });
    onGetListPhieu(newParam);
  };

  const onSizeChange = (size) => {
    const newParam = {
      ...state.param,
      page: 0,
      size,
    };
    setState({ param: newParam });
    onGetListPhieu(newParam);
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const onChangeDate = (key) => (e) => {
    let value = "";
    let value1 = "";
    if (e) {
      value = e[0].format("YYYY-MM-DD 00:00:00");
      value1 = e[1].format("YYYY-MM-DD 23:59:59");
    }
    if ("" == value) {
      funcRefreshData && funcRefreshData();
    }
    const newParam = {
      ...state.param,
      page: 0,
      [`tu${key}`]: value,
      [`den${key}`]: value1,
    };
    setState({ param: newParam });
    onGetListPhieu(newParam);
  };

  const renderTrangThaiDls = (data) => {
    const { trangThaiDls } = data || {};
    let title = "";
    if (trangThaiDls) {
      title = listTrangThaiDls?.find((item) => item.id === trangThaiDls)?.i18;
      if (title) return t(title);
    } else {
      return t("kho.choDuyetDLS");
    }
  };

  const columns = [
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.stt")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      dataIndex: "index",
      width: 60,
      align: "center",
      render: (_, __, index) => state.param.page * state.param.size + index + 1,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.ngayTaoPhieu")}
          sort_key="thoiGianTaoPhieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianTaoPhieu || 0}
          searchDate={
            <RangePicker
              format="DD/MM/YYYY"
              placeholder={[t("common.tuNgay"), t("common.denNgay")]}
              onChange={onChangeDate("ThoiGianTaoPhieu")}
            />
          }
        />
      ),
      width: 180,
      show: true,
      dataIndex: "thoiGianTaoPhieu",
      i18Name: "quanLyNoiTru.ngayTaoPhieu",
      render: (item) =>
        item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.ngayDuyetPhieu")}
          sort_key="thoiGianDuyet"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianDuyet || 0}
          searchDate={
            <RangePicker
              format="DD/MM/YYYY"
              placeholder={[t("common.tuNgay"), t("common.denNgay")]}
              onChange={onChangeDate("ThoiGianDuyet")}
            />
          }
        />
      ),
      dataIndex: "thoiGianDuyet",
      i18Name: "quanLyNoiTru.ngayDuyetPhieu",
      width: 180,
      show: true,
      render: (item) =>
        item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phieuLinh.soPhieuLinh")}
          sort_key="soPhieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soPhieu || 0}
          search={
            <Input
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("soPhieu")}
            />
          }
        />
      ),
      width: 100,
      show: true,
      i18Name: "quanLyNoiTru.phieuLinh.soPhieuLinh",
      dataIndex: "soPhieu",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.phieuLinhLan")}
          sort_key="lan"
          dataSort={dataSortColumn["lan"] || ""}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("lan")}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "lan",
      show: true,
      i18Name: "kho.phieuLinhLan",
      key: "lan",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.trangThaiDuyetDls")}
          sort_key="trangThaiDls"
          dataSort={dataSortColumn["trangThaiDls"] || ""}
          onClickSort={onClickSort}
        />
      ),
      show: true,
      width: 140,
      dataIndex: "trangThaiDls",
      key: "trangThaiDls",
      i18Name: "kho.trangThaiDuyetDls",
      render: (_, item) => renderTrangThaiDls(item),
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phieuLinh.trangThaiPhieuLinh")}
          sort_key="trangThai"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.trangThai || 0}
          searchSelect={
            <Select
              data={listTrangThaiPhieuNhapXuat}
              placeholder={t("common.chonTrangThai")}
              defaultValue=""
              onChange={onSearchInput("dsTrangThai")}
              hasAllOption={true}
            />
          }
        />
      ),
      show: true,
      width: 140,
      dataIndex: "trangThai",
      key: "trangThai",
      i18Name: "quanLyNoiTru.trangThaiPhieu",
      render: (item) =>
        listTrangThaiPhieuNhapXuat?.find((i) => i.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phieuLinh.loaiLinh")}
          sort_key="loaiNhapXuat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.loaiNhapXuat || 0}
          searchSelect={
            <Select
              data={listloaiNhapXuat?.filter((x) =>
                [
                  LOAI_NHAP_XUAT.LINH_BU_TU_TRUC,
                  LOAI_NHAP_XUAT.LINH_NOI_TRU,
                  LOAI_NHAP_XUAT.CHE_PHAM_MAU,
                ]?.includes(x.id)
              )}
              placeholder={t("quanLyNoiTru.phieuLinh.chonLoaiLinh")}
              defaultValue=""
              onChange={onSearchInput("loaiNhapXuat")}
              hasAllOption={true}
            />
          }
        />
      ),
      show: true,
      width: 120,
      dataIndex: "loaiNhapXuat",
      key: "loaiNhapXuat",
      i18Name: "quanLyNoiTru.phieuLinh.loaiLinh",
      render: (item) => listLoaiNhapXuat?.find((i) => i.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phieuLinh.khoXuat")}
          sort_key="kho.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["kho.ten"] || 0}
          searchSelect={
            <SelectLoadMore
              api={dmKhoProvider.searchAll}
              addParam={addParam}
              onChange={onSearchInput("dsKhoId")}
              keySearch={"ten"}
              placeholder={t("kho.chonKho")}
              dropdownMatchSelectWidth={250}
            />
          }
        />
      ),
      show: true,
      width: 120,
      dataIndex: "tenKho",
      i18Name: "quanLyNoiTru.phieuLinh.khoXuat",
      render: (item, list, index) => {
        if (list?.loaiNhapXuat == 80) return list?.tenKhoDoiUng;

        return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phieuLinh.khoYeuCau")}
          sort_key="khoDoiUng.ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["khoDoiUng.ten"] || 0}
          searchSelect={
            <SelectLoadMore
              api={dmKhoProvider.searchAll}
              addParam={addParam}
              onChange={onSearchInput("dsKhoDoiUngId")}
              keySearch={"ten"}
              placeholder={t("kho.chonKho")}
              dropdownMatchSelectWidth={250}
            />
          }
        />
      ),
      show: true,
      width: 120,
      i18Name: "quanLyNoiTru.phieuLinh.khoYeuCau",
      dataIndex: "tenKhoDoiUng",
      render: (item, list, index) => {
        if (list?.loaiNhapXuat == 80) return list?.tenKho;

        return item;
      },
    },
    {
      title: <HeaderSearch title={t("common.dienGiai")} />,
      show: true,
      i18Name: "common.dienGiai",
      width: 300,
      render: (item, list, index) => {
        const maPhieuLinh =
          (listAllMaPhieuLinh || []).find((x) => x.id == list?.phieuLinhId)
            ?.ten || "";
        const loaiHangHoa = listLoaiDichVu?.find(
          (i) => i.id === list?.loaiDichVu
        )?.ten;

        return (
          <div>
            {list.loaiToDieuTri == 20 ? (
              <>{t("kho.phieuLinhThuocDieuTriNgoaiTru")} </>
            ) : (
              <>
                {list.loaiNhapXuat === 85
                  ? t("quanLyNoiTru.phieuLinh.phieuLinhNoiTru")
                  : t("quanLyNoiTru.phieuLinh.phieuLinhBu")}{" "}
                {loaiHangHoa}
              </>
            )}{" "}
            {maPhieuLinh} {t("common.tuNgay")}{" "}
            {list?.tuThoiGian
              ? moment(list?.tuThoiGian)?.format("DD/MM/YYYY HH:mm:ss")
              : " "}{" "}
            {t("common.denNgay")}{" "}
            {list?.denThoiGian
              ? moment(list?.denThoiGian)?.format("DD/MM/YYYY HH:mm:ss")
              : " "}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phieuLinh.tenBuongPtTt")}
          sort_key="tenBuongPtTt"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenBuongPtTt || 0}
          search={
            <Input
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("tenBuongPtTt")}
            />
          }
        />
      ),
      show: true,
      width: 120,
      i18Name: "quanLyNoiTru.phieuLinh.tenBuongPtTt",
      dataIndex: "tenBuongPtTt",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.loaiHangHoa")}
          sort_key="loaiDichVu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.loaiDichVu || 0}
          searchSelect={
            <Select
              data={listLoaiDichVu.filter((item) =>
                [90, 100, 110, 50].includes(item.id)
              )}
              placeholder={t("quanLyNoiTru.chonLoaiHangHoa")}
              defaultValue=""
              onChange={onSearchInput("loaiDichVu")}
              hasAllOption={true}
            />
          }
        />
      ),
      show: false,
      width: 120,
      dataIndex: "loaiDichVu",
      i18Name: "quanLyNoiTru.loaiHangHoa",
      render: (item) => listLoaiDichVu?.find((i) => i.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.khoaChiDinh")}
          sort_key="khoaChiDinh.ten"
          // onClickSort={onClickSort}
          dataSort={dataSortColumn["khoaChiDinh.ten"] || 0}
          searchSelect={
            // <SelectLoadMore
            //   api={dmKhoaProvider.searchAll}
            //   addParam={addParam}
            //   onChange={onSearchInput("khoaChiDinhId")}
            //   keySearch={"ten"}
            //   placeholder="Chọn khoa"
            // />

            <Select
              data={listAllKhoa}
              placeholder={t("common.chonKhoa")}
              defaultValue=""
              onChange={onSearchInput("khoaChiDinhId")}
            />
          }
        />
      ),
      dataIndex: "tenKhoaChiDinh",
      width: 150,
      show: false,
      i18Name: "quanLyNoiTru.khoaChiDinh",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.nguoiTao")}
          sort_key="tenNguoiTaoPhieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenNguoiTaoPhieu || 0}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("tenNguoiTaoPhieu")}
            />
          }
        />
      ),
      show: true,
      width: "150px",
      dataIndex: "tenNguoiTaoPhieu",
      i18Name: "quanLyNoiTru.nguoiTao",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.nguoiDuyet")}
          sort_key="tenNguoiDuyet"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenNguoiDuyet || 0}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("tenNguoiDuyet")}
            />
          }
        />
      ),
      show: true,
      width: "150px",
      dataIndex: "tenNguoiDuyet",
      i18Name: "quanLyNoiTru.nguoiDuyet",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.cpdd.loaiChiDinh")}
          searchSelect={
            <Select
              data={listLoaiChiDinh}
              placeholder={t("danhMuc.chonTitle", {
                title: lowerFirst(t("quanLyNoiTru.cpdd.loaiChiDinh")),
              })}
              onChange={(e) => {
                let value =
                  e?.length && e[0] !== "" ? e.map(Number) : undefined;
                onSearchInput("dsLoaiChiDinh")(value);
              }}
              mode="multiple"
              hasAllOption={true}
            />
          }
        />
      ),
      dataIndex: "loaiChiDinh",
      key: "loaiChiDinh",
      width: 150,
      show: true,
      i18Name: "quanLyNoiTru.cpdd.loaiChiDinh",
      render: (item) => listLoaiChiDinh.find((i) => i.id === item)?.ten,
    },
    {
      title: <HeaderSearch title={t("common.tienIch")} />,
      key: "",
      width: 80,
      dataIndex: "",
      hideSearch: true,
      fixed: "right",
      hidden: !isModal,
      ignore: true,
      render: (_, item, index) => {
        return (
          <>
            <SVG.IcPrint
              className="ic-action"
              onClick={() => onPrintPhieu(item)}
            />
            <SVG.IcEye
              className="ic-action"
              onClick={() => {
                onGoToDetail(item);
              }}
            />
          </>
        );
      },
    },
  ];

  return (
    <>
      <GlobalStyle />
      <TableWrapper
        columns={columns}
        dataSource={dsPhieuNhapXuat}
        onRow={onRow}
        rowKey={(record) => record.id}
        scroll={{ x: 2100 }}
        tableName="table_QLNT_DanhSachPhieuLinh"
        ref={refSettings}
      />
      {!!totalElements && (
        <Pagination
          onChange={onChangePage}
          current={state.param.page + 1}
          pageSize={state.param.size}
          listData={dsPhieuNhapXuat}
          total={totalElements}
          onShowSizeChange={onSizeChange}
        />
      )}
      {state.listCheckId.length > 0 && (
        <div className="button-action">
          <Button
            className="btn_new"
            rightIcon={<SVG.IcDelete className="red" />}
            iconHeight={20}
            // onClick={
            //   refTimKiemPhieuNhap.current &&
            //   refTimKiemPhieuNhap.current.onCreateNew
            // }
          >
            <span>Xóa phiếu</span>
          </Button>
          <Button
            className="btn_new"
            rightIcon={<SVG.IcPrint className="blue" />}
            iconHeight={20}
            // onClick={
            //   refTimKiemPhieuNhap.current &&
            //   refTimKiemPhieuNhap.current.onCreateNew
            // }
          >
            <span>In phiếu</span>
          </Button>
          <Button
            className="btn_new"
            type="primary"
            rightIcon={<SVG.IcSend />}
            iconHeight={20}
            // onClick={
            //   refTimKiemPhieuNhap.current &&
            //   refTimKiemPhieuNhap.current.onCreateNew
            // }
          >
            <span>Gửi duyệt</span>
          </Button>
        </div>
      )}
    </>
  );
};

export default forwardRef(DanhSach);
