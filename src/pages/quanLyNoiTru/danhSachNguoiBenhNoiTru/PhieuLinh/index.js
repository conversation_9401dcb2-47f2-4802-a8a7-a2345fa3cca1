import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import { useDispatch } from "react-redux";
import DanhSach from "./DanhSach";
import ModalTaoPhieuLinh from "./ModalTaoPhieuLinh";
import { Button, KhoaThucHien } from "components";
import { useStore, useThietLap } from "hooks";
import ModalSuaSoLuongLe from "./ModalSuaSoLuongLe";
import { DS_TINH_CHAT_KHOA, THIET_LAP_CHUNG } from "constants/index";
import ModalTaoPhieuSuatAn from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinhSuatAn/ModalTaoPhieu";
import { Main, WrapperPage } from "./styled";
import { isArray, parseListConfig } from "utils/index";

const DanhSachPhieuLinh = (props) => {
  const history = useHistory();
  const refModalPhieuLinh = useRef();
  const refSuaSoLuongLe = useRef();
  const refModalTaoPhieuSuatAn = useRef();
  const [dataLOAI_NHAP_XUAT_BS_DS_PHIEU_LINH, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.LOAI_NHAP_XUAT_BS_DS_PHIEU_LINH
  );

  const [state, _setState] = useState({ dataSortColumn: {} });
  const { t } = useTranslation();
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  const khoaLamViec = useStore("phieuNhapXuat.khoaLamViec", {});

  const {
    phieuNhapXuat: { getListPhieu, updateData },
  } = useDispatch();

  const clickTaoPhieuLinh = () => {
    if (refModalPhieuLinh.current) {
      refModalPhieuLinh.current.show(
        {},
        () => {},
        (data) => {
          if (data?.dsDichVu.length == 1 && data?.code != 1032) {
            history.push(
              `/quan-ly-noi-tru/chi-tiet-phieu-linh/${data?.dsDichVu[0]?.phieuNhapXuatId}`
            );
          } else {
            refSuaSoLuongLe.current && refSuaSoLuongLe.current.show(data);
          }
        }
      );
    }
  };

  const isNoiTru = window.location.pathname.indexOf("/quan-ly-noi-tru") !== -1;

  const { linkDetail, breadcrumb } = useMemo(() => {
    const { pathname } = window.location;
    const isPttt = pathname.indexOf("/phau-thuat-thu-thuat") !== -1;
    const isCdha = pathname.indexOf("/chan-doan-hinh-anh") !== -1;
    const moduleName = isPttt
      ? "phau-thuat-thu-thuat"
      : isCdha
      ? "chan-doan-hinh-anh"
      : "quan-ly-noi-tru";

    return {
      linkDetail: `/${moduleName}/chi-tiet-phieu-linh/`,
      breadcrumb: [
        {
          link: `/${moduleName}`,
          title: isPttt
            ? t("pttt.quanLyPhauThuatThuThuat")
            : isCdha
            ? t("cdha.cdha")
            : t("quanLyNoiTru.quanLyNoiTru"),
        },
        {
          link: `/${moduleName}/${
            isPttt
              ? "danh-sach-nguoi-benh"
              : isCdha
              ? "thuc-hien-cdha-tdcn"
              : "danh-sach-nguoi-benh-noi-tru"
          }`,
          title: isPttt
            ? t("pttt.danhSachPhauThuatThuThuat")
            : isCdha
            ? t("cdha.thucHienCdhaTdcn")
            : t("quanLyNoiTru.danhSachNguoiBenhNoiTru"),
        },
        {
          link: `/${moduleName}/danh-sach-phieu-linh`,
          title: t("quanLyNoiTru.danhSachPhieuLinh"),
        },
      ],
    };
  }, [window.location]);

  useEffect(() => {
    if (!loadFinish || !khoaLamViec?.id) return;
    let listLoaiNx = parseListConfig(dataLOAI_NHAP_XUAT_BS_DS_PHIEU_LINH, true);
    getListPhieu({
      page: 0,
      size: 10,
      ...(isArray(listLoaiNx, 1) && { dsLoaiNhapXuat: listLoaiNx }),
      dsKhoaChiDinhId: [khoaLamViec.id],
    });
  }, [khoaLamViec, loadFinish, dataLOAI_NHAP_XUAT_BS_DS_PHIEU_LINH]);

  const onChangeKhoa = (khoa) => {
    setState({ khoaChiDinhId: khoa?.id });
    updateData({ khoaLamViec: khoa });
  };

  return (
    <WrapperPage
      breadcrumb={breadcrumb}
      title={
        <div>
          <div className="header-setting">
            <span>{t("quanLyNoiTru.danhSachPhieuLinh")}</span>
          </div>
          <div className="khoaLamViec">
            <span style={{ color: "#2d3540" }}>
              {t("quanLyNoiTru.khoaLamViec")}:
            </span>
            &nbsp;
            <KhoaThucHien
              dsTinhChatKhoa={isNoiTru ? DS_TINH_CHAT_KHOA.NOI_TRU : null}
              onChange={onChangeKhoa}
              justifyContent={"left"}
            />
          </div>
        </div>
      }
      titleRight={
        <Button
          className="btn_new"
          type={"success"}
          iconHeight={20}
          onClick={clickTaoPhieuLinh}
        >
          <span>{t("quanLyNoiTru.taoPhieuLinh")}</span>
        </Button>
      }
    >
      <Main>
        <DanhSach
          linkDetail={linkDetail}
          khoaChiDinhId={state?.khoaChiDinhId}
        />
      </Main>
      <ModalTaoPhieuLinh
        khoaLamViecId={khoaLamViec?.id}
        ref={refModalPhieuLinh}
        refModalTaoPhieuSuatAn={refModalTaoPhieuSuatAn}
      />
      <ModalTaoPhieuSuatAn
        ref={refModalTaoPhieuSuatAn}
        khoaLamViecId={khoaLamViec?.id}
      />
      <ModalSuaSoLuongLe ref={refSuaSoLuongLe} />
    </WrapperPage>
  );
};

export default DanhSachPhieuLinh;
