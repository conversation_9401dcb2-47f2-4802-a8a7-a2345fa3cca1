import React, { useEffect, useState, useMemo, forwardRef, useRef } from "react";
import moment from "moment";
import { Main, DivInfo, GlobalStyle, NoBox } from "./styled";
import { useDispatch } from "react-redux";
import {
  DateTimePicker,
  TextField,
  Select,
  Button,
  SelectLargeData,
  Tabs,
  Dropdown,
  ModalSignPrint,
  DatePicker,
  Popover,
  Radio,
} from "components";
import {
  DOI_TUONG_KCB,
  LOAI_DICH_VU,
  LOAI_IN,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB,
  ENUM,
  DOI_TUONG,
  MAN_HINH_PHIEU_IN,
  VI_TRI_PHIEU_IN,
  LOAI_TO_DIEU_TRI,
  LOAI_PHONG,
} from "constants/index";
import KeThuoc from "pages/khamBenh/DonThuoc/KeThuoc";
import {
  useConfirm,
  useEnum,
  useListAll,
  useLoading,
  useStore,
  useThietLap,
} from "hooks";
import { useParams } from "react-router-dom";
import ThuocDaChiDinh from "pages/khamBenh/DonThuoc/ThuocDaChiDinh";
import { checkRole } from "lib-utils/role-utils";
import { useTranslation } from "react-i18next";
import empty from "assets/images/kho/empty.png";
import { orderBy } from "lodash";
import { Col, Menu, Row, message, Space } from "antd";
import { SelectGroup, TextFieldGroup } from "../ToDieuTri/styled";
import { SVG } from "assets";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import {
  checkBatBuocPhanPhongGiuong,
  getDsMoTa,
  getMoTaChanDoan,
  openInNewTab,
} from "utils";
import { printJS } from "data-access/print-provider";
import CustomTag from "pages/khamBenh/KhamCoBan/ChanDoan/ChanDoanBenh/CustomTag";

const { SelectChanDoan } = SelectLargeData;

const DonThuocRaVien = (props, ref) => {
  const refModalSignPrint = useRef(null);
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const { khoaLamViec, isBatBuocPhanPhongGiuong } = props;
  const [state, _setState] = useState({ listPhieu: [] });
  const setState = (data) => {
    _setState((prevState) => ({
      ...prevState,
      ...data,
    }));
  };

  const {
    boChiDinh: { getBoChiDinh },
    lieuDung: { getListAllLieuDung },
    chiDinhKhamBenh: { updateConfigData },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    loiDan: { getListAllLoiDan },
    phong: { getListPhongTongHop },
    nbDotDieuTri: { luuLoiDan, getThongTinRaVien },
    toDieuTri: { onDelete, createOrEdit, getToDieuTriRaVien, patch },
    nhanVien: { getListBacSi },
    chiDinhDichVuKho: { getListDichVuThuoc },
    phieuIn: { getListPhieu, getFilePhieuIn, showFileEditor },
  } = useDispatch();

  const listAllLoiDan = useStore("loiDan.listAllLoiDan", []);
  const configData = useStore("chiDinhKhamBenh.configData", {});
  const { nhanVienId } = useStore("auth.auth");
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const nbThongTinRaVien = useStore("nbDotDieuTri.nbThongTinRaVien", {});
  const listPhongThucHien = useStore("phong.listPhong", []);
  const listToDieuTriRaVien = useStore("toDieuTri.listToDieuTriRaVien", []);
  const listToDieuTri = useStore("toDieuTri.listToDieuTri", []);
  const activeKey = useStore("toDieuTri.activeKey", null);

  const listBacSi = useStore("nhanVien.listBacSi", []);
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");

  const [listNgonNgu] = useEnum(ENUM.NGON_NGU, []);
  const [listAllMauDienBien] = useListAll("mauDienBien", {}, true);

  const [dataCAP_NHAT_NGAY_HEN_KHAM_THEO_NGAY_CHO_DON_THUOC] = useThietLap(
    THIET_LAP_CHUNG.CAP_NHAT_NGAY_HEN_KHAM_THEO_NGAY_CHO_DON_THUOC
  );
  const [CHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY] = useThietLap(
    THIET_LAP_CHUNG.CHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY
  );
  const [BAT_BUOC_THOI_GIAN_DUNG_THUOC_NGOAI_TRU] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_THOI_GIAN_DUNG_THUOC_NGOAI_TRU
  );
  const [CANH_BAO_CHUA_TAM_UNG_NOI_TRU] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_CHUA_TAM_UNG_NOI_TRU
  );
  const [dataCHAN_KHONG_BHYT_RA_VIEN_THUOC_KHO] = useThietLap(
    THIET_LAP_CHUNG.CHAN_KHONG_BHYT_RA_VIEN_THUOC_KHO
  );

  const [dataCHO_PHEP_NHAP_MO_TA_CHAN_DOAN] = useThietLap(
    THIET_LAP_CHUNG.CHO_PHEP_NHAP_MO_TA_CHAN_DOAN,
    "TRUE"
  );
  const [dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD,
    "FALSE"
  );
  const [dataDONG_BO_CHAN_DOAN_TDT_RA_VIEN] = useThietLap(
    THIET_LAP_CHUNG.DONG_BO_CHAN_DOAN_TDT_RA_VIEN,
    "FALSE"
  );
  const isCapNhatNgayHenKhamTheoNgay = useMemo(() => {
    return (
      (
        dataCAP_NHAT_NGAY_HEN_KHAM_THEO_NGAY_CHO_DON_THUOC || ""
      ).toLowerCase() == "true"
    );
  }, [dataCAP_NHAT_NGAY_HEN_KHAM_THEO_NGAY_CHO_DON_THUOC]);

  const toDieuTriRaVien = useMemo(() => {
    if (Array.isArray(listToDieuTriRaVien) && listToDieuTriRaVien.length > 0) {
      return listToDieuTriRaVien[0];
    }

    return null;
  }, [listToDieuTriRaVien]);

  const { isEdit, isKeThuoc } = useMemo(() => {
    const isReadonlyDonThuocRaVien =
      (chiTietNguoiBenhNoiTru?.khoaNbId !== khoaLamViec?.id &&
        !checkRole([ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_KHAC_KHOA])) ||
      (![
        TRANG_THAI_NB.DANG_DIEU_TRI,
        TRANG_THAI_NB.DANG_CHUYEN_KHOA,
        TRANG_THAI_NB.CHO_HOAN_TAT_THU_TUC_RA_VIEN,
        // TRANG_THAI_NB.DA_RA_VIEN,
        // TRANG_THAI_NB.HEN_DIEU_TRI,
      ].includes(chiTietNguoiBenhNoiTru?.trangThai) &&
        !checkRole([ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_DA_RA_VIEN]));

    let obj = {
      isEdit: !isReadonlyDonThuocRaVien,
      isKeThuoc: !isReadonlyDonThuocRaVien,
    };

    if (chiTietNguoiBenhNoiTru?.trangThai >= TRANG_THAI_NB.DA_RA_VIEN) {
      if (
        checkRole([
          ROLES["QUAN_LY_NOI_TRU"]
            .CHI_DINH_THUOC_VA_CHINH_SUA_DON_THUOC_RA_VIEN,
        ])
      ) {
        const checkTimeValid =
          chiTietNguoiBenhNoiTru?.thoiGianRaVien &&
          moment().diff(
            moment(chiTietNguoiBenhNoiTru.thoiGianRaVien),
            "hours"
          ) < 24;
        obj.isEdit = checkTimeValid;
        obj.isKeThuoc = checkTimeValid;
      } else {
        obj.isEdit = false;
        obj.isKeThuoc = true;
      }
    }
    return obj;
  }, [chiTietNguoiBenhNoiTru, khoaLamViec]);

  useEffect(() => {
    getBoChiDinh({
      dsLoaiDichVu: LOAI_DICH_VU.THUOC,
      bacSiChiDinhId: nhanVienId,
    });
    getListAllLieuDung({ active: true, page: "", size: "" });
    getListAllLoiDan(
      {
        active: true,
        page: "",
        size: "",
        danhChoThuoc: true,
      },
      "danhChoThuoc_" + true
    );
    getListPhongTongHop({
      page: 0,
      sort: "active,desc",
      size: 500,
      dsLoaiPhong: LOAI_PHONG.PHONG_KHAM,
    });
    getListBacSi({
      dsMaThietLapVanBang: THIET_LAP_CHUNG.BAC_SI,
      active: true,
      page: "",
      size: "",
    });
  }, []);

  useEffect(() => {
    if (chiTietNguoiBenhNoiTru.id && khoaLamViec?.id) {
      getToDieuTriRaVien({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        khoaChiDinhId: khoaLamViec?.id,
        loai: LOAI_TO_DIEU_TRI.RA_VIEN, //20: Ra viện
      });
    }
  }, [chiTietNguoiBenhNoiTru?.id, khoaLamViec?.id]);

  const onLuuLoiDan = async () => {
    if (!state?.thoiGianYLenh) {
      message.error(t("quanLyNoiTru.vuiLongNhapThoiGianYLenh"));
      return;
    }
    if (!state?.bacSiDieuTriId) {
      message.error(t("quanLyNoiTru.vuiLongNhapBacSiDieuTri"));
      return;
    }
    if (
      BAT_BUOC_THOI_GIAN_DUNG_THUOC_NGOAI_TRU?.toLowerCase() === "true" &&
      !(state.dataMore?.tuNgay && state.dataMore?.denNgay)
    ) {
      message.error(t("khamBenh.vuiLongNhapThoiGianTuNgayDenNgayChoDon"));
      return;
    }
    if (
      CHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY?.eval() &&
      state.dataMore?.soNgayChoDon &&
      Number(state.dataMore?.soNgayChoDon) > 7 &&
      chiTietNguoiBenhNoiTru?.doiTuong === DOI_TUONG.BAO_HIEM
    ) {
      message.error(
        t("khamBenh.vuiLongNhapSoNgayChoDon", {
          num: 7,
        })
      );
      return;
    }
    if (
      moment(state.dataMore.tuNgay).isBefore(
        moment(chiTietNguoiBenhNoiTru.thoiGianVaoVien).startOf("days")
      )
    ) {
      message.error(t("quanLyNoiTru.vuiLongNhapTuNgayLonHonNgayVaoVien"));
      return;
    }

    try {
      showLoading();

      //lưu lời dặn
      const {
        ghiChu,
        loiDanBacSi,
        dsPhongHenKhamId,
        soNgayChoDon = 0,
        thoiGianHenKham,
        tuNgay,
        denNgay,
      } = state.dataMore || {};
      let obj = {
        id: chiTietNguoiBenhNoiTru?.id,
        body: {
          loiDanBacSi,
          thoiGianHenKham,
          dsPhongHenKhamId,
          soNgayChoDon,
          ghiChu,
          tuNgay,
          denNgay,
        },
      };
      await luuLoiDan(obj);

      //lưu thông tin tờ điều trị
      if (toDieuTriRaVien) {
        const payload = {
          nbDotDieuTriId: toDieuTriRaVien?.nbDotDieuTriId,
          id: toDieuTriRaVien?.id,
          thoiGianYLenh:
            state?.thoiGianYLenh &&
            moment(state?.thoiGianYLenh).format("YYYY-MM-DD HH:mm:ss"),
          dienBienBenh: state?.dienBienBenh,
          bacSiDieuTriId: state?.bacSiDieuTriId,
          dsCdChinhId: state?.dsCdChinhId,
          dsCdKemTheoId: state?.dsCdKemTheoId,
          moTaChanDoan: {
            dsCdChinh: state.dsMoTaChinh,
            dsCdKemTheo: state.dsMoTaKemTheo,
          },
          moTa: state?.moTa,
          ghiChu,
        };

        await patch({
          ...payload,
        });

        //get lại ds thuốc đã chỉ định
        await getListDichVuThuoc({
          nbDotDieuTriId: toDieuTriRaVien?.nbDotDieuTriId,
          chiDinhTuDichVuId: toDieuTriRaVien?.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          dsTrangThaiHoan: [0, 10, 20],
        });

        getToDieuTriRaVien({
          nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
          khoaChiDinhId: khoaLamViec?.id,
          loai: 20, //20: Ra viện
        });
        getThongTinRaVien(chiTietNguoiBenhNoiTru.id);
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  useEffect(() => {
    if (chiTietNguoiBenhNoiTru.id && toDieuTriRaVien && thongTinCoBan.id) {
      updateConfigData({
        configData: {
          ...configData,
          chiDinhTuDichVuId: toDieuTriRaVien.id,
          dsChiDinhTuDichVuId: toDieuTriRaVien.id,
          nbThongTinId: chiTietNguoiBenhNoiTru.nbThongTinId,
          thongTinNguoiBenh: chiTietNguoiBenhNoiTru,
          phongThucHienId: chiTietNguoiBenhNoiTru.phongId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          trangThaiKham: "",
          nbDotDieuTriId: id,
          khoaChiDinhId: khoaLamViec?.id,
          isNgoaiTru: true,
          doiTuongKcb: chiTietNguoiBenhNoiTru.doiTuongKcb,
          tienTamUng: thongTinCoBan.tienTamUng,
          tienHoanUng: thongTinCoBan.tienHoanUng,
          canLamSang: false,
          thoiGianYLenh: toDieuTriRaVien?.thoiGianYLenh,
        },
      });
    }
  }, [chiTietNguoiBenhNoiTru, toDieuTriRaVien, thongTinCoBan, activeKey]);

  useEffect(() => {
    const {
      thoiGianHenKham,
      loiDanBacSi,
      dsPhongHenKhamId,
      ghiChu,
      soNgayChoDon,
      tuNgay,
      denNgay,
    } = nbThongTinRaVien;
    setState({
      dataMore: {
        ...state.dataMore,
        thoiGianHenKham,
        loiDanBacSi,
        soNgayChoDon,
        dsPhongHenKhamId,
        ghiChu,
        tuNgay: tuNgay ? tuNgay : moment().startOf("days"),
        denNgay,
      },
    });
  }, [nbThongTinRaVien, activeKey]);

  useEffect(() => {
    const clearHtml = (html) => {
      if (html) {
        const div = document.createElement("div");
        div.innerHTML = html;
        return div.textContent || div.innerText || "";
      }
      return html;
    };

    if (toDieuTriRaVien) {
      setState({
        bacSiDieuTriId: toDieuTriRaVien?.bacSiDieuTriId,
        dienBienBenh: clearHtml(toDieuTriRaVien?.dienBienBenh),
        dsCdChinhId: toDieuTriRaVien?.dsCdChinhId,
        dsCdKemTheoId: toDieuTriRaVien?.dsCdKemTheoId,
        moTa: toDieuTriRaVien?.moTa,
        dsMoTaChinh: getDsMoTa(toDieuTriRaVien, "dsCdChinh"),
        dsMoTaKemTheo: getDsMoTa(toDieuTriRaVien, "dsCdKemTheo"),
        thoiGianYLenh: moment(toDieuTriRaVien?.thoiGianYLenh),
      });
    }
  }, [toDieuTriRaVien, activeKey]);

  useEffect(() => {
    if (activeKey != 9) {
      setState({
        dienBienBenh: null,
        dataMore: {
          ...state.dataMore,
          loiDanBacSi: null,
        },
      });
    }
  }, [activeKey]);

  useEffect(() => {
    getListThietLapChonKhoTheoTaiKhoan({
      loaiDoiTuongId: configData.thongTinNguoiBenh?.loaiDoiTuongId,
      loaiDichVu: LOAI_DICH_VU.THUOC, // thuốc
      khoaNbId: khoaLamViec?.id,
      khoaChiDinhId: configData.khoaChiDinhId,
      doiTuong: configData.thongTinNguoiBenh?.doiTuong,
      noiTru:
        configData.thongTinNguoiBenh?.doiTuongKcb === DOI_TUONG_KCB.NGOAI_TRU
          ? false
          : true,
      capCuu: configData.thongTinNguoiBenh?.capCuu,
      phongId: configData.phongThucHienId,
      canLamSang: false,
    });
  }, [configData]);

  const hideThuocKho = useMemo(() => {
    return (
      toDieuTriRaVien?.id &&
      chiTietNguoiBenhNoiTru.doiTuong === DOI_TUONG.KHONG_BAO_HIEM &&
      dataCHAN_KHONG_BHYT_RA_VIEN_THUOC_KHO?.eval()
    );
  }, [
    toDieuTriRaVien,
    chiTietNguoiBenhNoiTru,
    dataCHAN_KHONG_BHYT_RA_VIEN_THUOC_KHO,
  ]);

  const dataPhongThucHien = useMemo(
    () =>
      (listPhongThucHien || []).reduce(
        (a, c) =>
          a.some((item) => item.id === c?.id)
            ? [...a]
            : [
                ...a,
                {
                  id: c.id,
                  ten: `${c?.ma} - ${c?.ten}`,
                },
              ],
        []
      ),
    [listPhongThucHien]
  );

  const onChangeSoNgay = (e) => {
    if (
      CHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY?.eval() &&
      Number(e) > 7 &&
      chiTietNguoiBenhNoiTru?.doiTuong === DOI_TUONG.BAO_HIEM
    ) {
      message.error(
        t("khamBenh.vuiLongNhapSoNgayChoDon", {
          num: 7,
        })
      );
      setState({
        dataMore: {
          ...state.dataMore,
          soNgayChoDon: e,
        },
      });
      return;
    }

    const thoiGianHenKham = moment().add(e, "days").format();
    let tuNgay = state?.dataMore?.tuNgay;
    let denNgay = state?.dataMore?.denNgay;
    if (state?.dataMore?.tuNgay && e) {
      denNgay = moment(state?.dataMore?.tuNgay).add(e - 1, "days");
    }
    if (state?.dataMore?.denNgay && e && !state?.dataMore?.tuNgay) {
      tuNgay = moment(state?.dataMore?.denNgay).subtract(e - 1, "days");
    }
    setState({
      dataMore: {
        ...state.dataMore,
        ...(isCapNhatNgayHenKhamTheoNgay && {
          thoiGianHenKham,
        }),
        soNgayChoDon: e,
        tuNgay,
        denNgay,
      },
    });
  };

  const onLuuSNCDLoiDan = (soNgayChoDon, thoiGianHenKham) => {
    const { loiDanBacSi, dsPhongHenKhamId, ghiChu, tuNgay, denNgay } =
      state.dataMore;

    let obj = {
      id: chiTietNguoiBenhNoiTru?.id,
      body: {
        loiDanBacSi,
        thoiGianHenKham,
        dsPhongHenKhamId,
        soNgayChoDon,
        ghiChu,
        tuNgay,
        denNgay,
      },
    };
    luuLoiDan(obj);
  };

  const handleSetData = (key) => (e) => {
    if (
      key == "thoiGianHenKham" &&
      e &&
      !moment(nbThongTinRaVien?.thoiGianHenKham).isSame(e, "day")
    ) {
      if (isCapNhatNgayHenKhamTheoNgay) {
        const soNgayMoi = e.diff(moment(), "days") + 1;
        onLuuSNCDLoiDan(
          Math.max(soNgayMoi, nbThongTinRaVien?.soNgayChoDon),
          e.format()
        );
      }
    }

    let value = e?.currentTarget ? e.currentTarget.innerHTML : e;
    let soNgayChoDon = state?.dataMore?.soNgayChoDon;
    let tuNgay = state?.dataMore?.tuNgay;
    let denNgay = state?.dataMore?.denNgay;
    if (key === "tuNgay" && state?.dataMore?.denNgay && e) {
      if (state?.dataMore?.soNgayChoDon) {
        denNgay = moment(e).add(state?.dataMore?.soNgayChoDon - 1, "days");
      } else {
        soNgayChoDon =
          moment(state?.dataMore?.denNgay).diff(moment(e), "days") + 1;
      }
    }

    if (key === "denNgay" && state?.dataMore?.tuNgay && e) {
      if (state?.dataMore?.soNgayChoDon) {
        tuNgay = moment(e).subtract(state?.dataMore?.soNgayChoDon - 1, "days");
      } else {
        soNgayChoDon =
          moment(e).diff(moment(state?.dataMore?.tuNgay), "days") + 1;
      }
    }

    if (key === "tuNgay" && !e) {
      value = moment().startOf("days");
    }

    if (
      CHAN_CHO_DON_THUOC_RA_VIEN_QUA_7_NGAY?.eval() &&
      soNgayChoDon &&
      Number(soNgayChoDon) > 7 &&
      chiTietNguoiBenhNoiTru?.doiTuong === DOI_TUONG.BAO_HIEM
    ) {
      message.error(
        t("khamBenh.vuiLongNhapSoNgayChoDon", {
          num: 7,
        })
      );
      return;
    }

    setState({
      dataMore: {
        ...state.dataMore,
        soNgayChoDon,
        denNgay,
        tuNgay: tuNgay,
        [key]: value,
      },
    });
  };
  const onSaveSoNgayChoDon = (e) => {
    const value = e?.target?.value || 0;

    if (
      isCapNhatNgayHenKhamTheoNgay &&
      value != nbThongTinRaVien?.soNgayChoDon
    ) {
      const thoiGianHenKham = moment().add(value, "days").format();
      onLuuSNCDLoiDan(value, thoiGianHenKham);
    }
  };

  const callBackFnModalChiDinhThuoc = ({ listDvThuoc }) => {
    const soNgayMax = Math.max(
      ...(listDvThuoc || []).map((item) => item.soNgay)
    );
    if (soNgayMax > nbThongTinRaVien?.soNgayChoDon) {
      const thoiGianHenKham = moment().add(soNgayMax, "days").format();
      onLuuSNCDLoiDan(soNgayMax, thoiGianHenKham);
    }
  };

  const taoToDieuTriDonThuoc = async () => {
    if (
      !checkRole([
        ROLES["QUAN_LY_NOI_TRU"].CHI_DINH_THUOC_VA_CHINH_SUA_DON_THUOC_RA_VIEN,
      ]) &&
      (thongTinCoBan?.tienTamUng || 0) - (thongTinCoBan?.tienHoanUng || 0) <=
        0 &&
      CANH_BAO_CHUA_TAM_UNG_NOI_TRU?.eval() &&
      !(thongTinCoBan?.theTam && thongTinBenhNhan?.maDoiTuongKcb?.ma === "1.7")
    ) {
      showConfirm({
        title: t("common.xacNhan"),
        content: t(
          "quanLyNoiTru.nguoiBenhChuaTamUngNoiTruVuiLongTamUngTruocKhiThemMoi",
          { title: t("quanLyNoiTru.donThuocRaVien") }
        ),
        cancelText: t("common.huy"),
      });
      return;
    }

    if (checkBatBuocPhanPhongGiuong(isBatBuocPhanPhongGiuong)) {
      return;
    }
    try {
      showLoading();

      const clearHtml = (html) => {
        if (html) {
          const div = document.createElement("div");
          div.innerHTML = html;
          return div.textContent || div.innerText || "";
        }
        return html;
      };

      let param = {},
        baseParam = {};
      let _listToDieuTri = listToDieuTri.filter(
        (item) => item.khoaChiDinhId === chiTietNguoiBenhNoiTru?.khoaNbId
      );

      if (_listToDieuTri.length) {
        //lấy thông tin từ tờ điều trị mới nhất
        _listToDieuTri = orderBy(_listToDieuTri, "thoiGianYLenh", "desc");
        const toDieuTriMoiNhat = _listToDieuTri[0];

        baseParam = {
          dsCdKemTheoId: toDieuTriMoiNhat.dsCdKemTheoId,
          dsCdChinhId: toDieuTriMoiNhat.dsCdChinhId,
          moTa: toDieuTriMoiNhat.moTa,
          khoaChiDinhId: toDieuTriMoiNhat.khoaChiDinhId,
          bacSiDieuTriId: listBacSi.map((item) => item.id).includes(nhanVienId)
            ? nhanVienId
            : toDieuTriMoiNhat.bacSiDieuTriId,
          dienBienBenh: clearHtml(toDieuTriMoiNhat.dienBienBenh),
        };
      } else {
        message.error(t("quanLyNoiTru.toDieuTri.chuaTaoToDieuTri"));
      }

      param = dataDONG_BO_CHAN_DOAN_TDT_RA_VIEN?.eval()
        ? {
            ...baseParam,
            dsCdKemTheoId:
              nbThongTinRaVien?.dsCdKemTheoId ?? baseParam.dsCdKemTheoId,
            dsCdChinhId: nbThongTinRaVien?.dsCdChinhId ?? baseParam.dsCdChinhId,
            moTa: nbThongTinRaVien?.moTa ?? baseParam.moTa,
          }
        : baseParam;

      const _thoiGianRaVien = chiTietNguoiBenhNoiTru?.thoiGianRaVien
        ? moment(chiTietNguoiBenhNoiTru?.thoiGianRaVien).format(
            "YYYY-MM-DD HH:mm:ss"
          )
        : moment().format("YYYY-MM-DD HH:mm:ss");

      await createOrEdit({
        ...param,
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        khoaChiDinhId: khoaLamViec?.id,
        loai: 20, //20: Ra viện
        thoiGianYLenh: _thoiGianRaVien,
        thoiGianKham: _thoiGianRaVien,
      });

      getToDieuTriRaVien({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        dsKhoaChiDinhId: khoaLamViec?.id,
        loai: 20, //20: Ra viện
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onChangeInput = (key) => (e, dataItems) => {
    if (key == "dsCdChinhId" || key == "dsCdKemTheoId") {
      let value = e;
      const keyMoTa = key == "dsCdChinhId" ? "dsMoTaChinh" : "dsMoTaKemTheo";
      const dsMoTa = value.map((id, index) => ({
        id: id,
        tenChanDoan: dataItems?.find((item2) => item2.id == id)?.label,
        moTa: (state[keyMoTa] || [])?.find((item) => item.id == id)?.moTa || "",
      }));
      const moTa = getMoTaChanDoan(
        key == "dsCdChinhId"
          ? [dsMoTa, state.dsMoTaKemTheo]
          : [state.dsMoTaChinh, dsMoTa]
      );
      if (dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD?.eval()) {
        setState({
          [key]: value,
          [keyMoTa]: dsMoTa,
          moTa: moTa,
        });
      } else {
        let value = "";
        if (e?.target) {
          if (e.target.hasOwnProperty("checked")) value = e.target.checked;
          else value = e.target.value;
        } else value = e;
        setState({ [key]: value });
      }
    } else {
      let value = "";
      if (e?.target) {
        if (e.target.hasOwnProperty("checked")) value = e.target.checked;
        else value = e.target.value;
      } else value = e;
      setState({ [key]: value });
    }
  };

  const getDsPhieuIn = () => {
    if (toDieuTriRaVien?.id) {
      getListPhieu({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        chiDinhTuDichVuId: toDieuTriRaVien?.id,
        dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
        maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
        maViTri: VI_TRI_PHIEU_IN.NOI_TRU.DON_THUOC_RA_VIEN,
      }).then((listPhieu) => {
        setState({
          listPhieu: listPhieu || [],
        });
      });
    }
  };

  const onInPhieu = (item) => async () => {
    try {
      if (item.type == "editor") {
        let mhParams = {};
        //kiểm tra phiếu ký số
        if (checkIsPhieuKySo(item)) {
          //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
          mhParams = {
            maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
            maViTri: VI_TRI_PHIEU_IN.NOI_TRU.DON_THUOC_RA_VIEN,
            chiDinhTuDichVuId: toDieuTriRaVien?.id,
            dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
            kySo: true,
            maPhieuKy: item.ma,
            nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
          };
        }

        showFileEditor({
          phieu: item,
          nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
          id: toDieuTriRaVien?.id,
          ma: item.ma,
          mhParams,
        });
      } else {
        if (checkIsPhieuKySo(item)) {
          refModalSignPrint.current &&
            refModalSignPrint.current.showToSign({
              phieuKy: item,
              payload: {
                nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id,
                maManHinh: MAN_HINH_PHIEU_IN.NOI_TRU,
                maViTri: VI_TRI_PHIEU_IN.NOI_TRU.DON_THUOC_RA_VIEN,
                dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                chiDinhTuDichVuId: toDieuTriRaVien?.id,
                ngonNguParams: {
                  ngonNgu: state[`valueNgonNgu_${item.id}`],
                  baoCaoId: state[`baoCaoId_${item.id}`],
                },
              },
            });
        } else {
          showLoading();
          const { finalFile, dsPhieu } = await getFilePhieuIn({
            listPhieus: [item],
            nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
            chiDinhTuDichVuId: toDieuTriRaVien?.id,
            dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
            ngonNguParams: {
              ngonNgu: state[`valueNgonNgu_${item.id}`],
              baoCaoId: state[`baoCaoId_${item.id}`],
            },
          });
          if ((dsPhieu || []).every((x) => x?.loaiIn == LOAI_IN.MO_TAB)) {
            openInNewTab(finalFile);
          } else {
            printJS({
              printable: finalFile,
              type: "pdf",
            });
          }
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const contentNgonNgu = (item) => {
    let _dsNgonNgu = [
      { id: 10, ten: t("account.tiengViet"), baoCaoId: item.baoCaoId },
    ];
    item.dsNgonNgu.forEach((element) => {
      const _selected = listNgonNgu.find((x) => x.id == element.ngonNgu);

      _dsNgonNgu.push({
        id: element.ngonNgu,
        ten: _selected?.ten || "",
        baoCaoId: element.id,
      });
    });

    return (
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={state[`valueNgonNgu_${item.id}`] || 10}
          onChange={(e) => {
            setState({
              [`valueNgonNgu_${item.id}`]: e.target.value,
              [`baoCaoId_${item.id}`]: e.target.baoCaoId,
              [`openPopover_${item.id}`]: false,
            });
          }}
        >
          {/* Tiếng Việt là mặc định */}
          <Space direction="vertical">
            {_dsNgonNgu.map((item, index) => (
              <Radio key={index} value={item.id} baoCaoId={item.baoCaoId}>
                {item.ten}
              </Radio>
            ))}
          </Space>
        </Radio.Group>
      </div>
    );
  };

  const rightAction = () => {
    let menuPrint = [];

    if (state?.listPhieu?.length) {
      state.listPhieu.forEach((item, index) => {
        menuPrint.push({
          key: index + 1,
          label: (
            <div style={{ display: "flex" }}>
              <a
                style={{ flex: 1 }}
                href={() => false}
                onClick={onInPhieu(item)}
              >
                {item.ten || item.tenBaoCao}
              </a>

              {item.dsNgonNgu?.length > 0 && (
                <Popover
                  getPopupContainer={(trigger) => trigger.parentNode}
                  overlayClassName={"step-wrapper-in-options right"}
                  placement="rightTop"
                  content={contentNgonNgu(item)}
                  trigger="click"
                  open={state[`openPopover_${item.id}`] || false}
                  onOpenChange={(value) => {
                    setState({
                      [`openPopover_${item.id}`]: value,
                    });
                  }}
                >
                  <SVG.IcLanguage
                    color={"var(--color-blue-primary)"}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  />
                </Popover>
              )}
            </div>
          ),
        });
      });
    }
    return (
      <>
        {toDieuTriRaVien && (
          <>
            <Dropdown
              overlayClassName="donRaVien__button-print"
              overlay={<Menu items={menuPrint} />}
              onClick={getDsPhieuIn}
              trigger="click"
            >
              <Button iconHeight={15} rightIcon={<SVG.IcPrint />}>
                {t("common.inGiayTo")}
              </Button>
            </Dropdown>
            {isEdit && (
              <Button
                type="primary"
                iconHeight={15}
                onClick={onLuuLoiDan}
                rightIcon={<SVG.IcSave color={"var(--color-blue-primary)"} />}
              >
                {`${t("common.luu")}`}
              </Button>
            )}
          </>
        )}
      </>
    );
  };

  if (!toDieuTriRaVien) {
    return (
      <NoBox>
        <img src={empty} alt="..." />
        <div className="label">
          {t("quanLyNoiTru.chuaTaoToDieuTriDonThuocRaVien")}
        </div>
        <Button
          disabled={!isEdit}
          type="primary"
          height={32}
          onClick={taoToDieuTriDonThuoc}
        >
          {t("quanLyNoiTru.taoToDieuTriDonThuocRaVien")}
        </Button>
      </NoBox>
    );
  }
  const onDeleteToDieuTriRaVien = () => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: `${t("quanLyNoiTru.toDieuTri.banChacChanMuonXoaToDieuTri")}`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        try {
          showLoading();

          onDelete(toDieuTriRaVien?.id).then(() => {
            getToDieuTriRaVien({
              nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
              khoaChiDinhId: khoaLamViec?.id,
              loai: 20, //20: Ra viện
            });
          });
        } catch (error) {
        } finally {
          hideLoading();
        }
      }
    );
  };

  const onChangeLoiDanBacSi = (e) => {
    if ((e || "").length >= 4000) {
      message.error(t("khamBenh.vuiLongNhapLoiDanKhongQua4000KyTu"));
    }
    handleSetData("loiDanBacSi")(e);
  };

  const onChangeMoTa = (type) => (id, value) => {
    const dsMoTa = state[type] || [];
    const item = dsMoTa.find((item) => item?.id == id);
    if (!item) {
      dsMoTa.push({ id, moTa: value });
    } else {
      item.moTa = value;
    }
    if (dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD.eval()) {
      const moTa = getMoTaChanDoan([state.dsMoTaChinh, state.dsMoTaKemTheo]);
      setState({ [type]: [...dsMoTa], moTa });
    } else {
      setState({ [type]: [...dsMoTa] });
    }
  };
  return (
    <Tabs.TabBox
      title={t("quanLyNoiTru.donThuocRaVien")}
      rightAction={
        <>
          {toDieuTriRaVien && isEdit && (
            <Button
              type="error"
              iconHeight={15}
              rightIcon={<SVG.IcDelete />}
              onClick={onDeleteToDieuTriRaVien}
            >
              {t("common.xoa")}
            </Button>
          )}
          {rightAction()}
        </>
      }
    >
      <Main>
        <GlobalStyle />
        <div className="don-thuoc-container">
          <Row>
            <Col span={11}>
              <div className="item">
                <span
                  style={{ paddingRight: 10 }}
                  className={`title ${state?.thoiGianYLenh ? "" : "validate"} `}
                >
                  {t("quanLyNoiTru.toDieuTri.ngayYLenh")}&nbsp;
                  <span style={{ color: "red" }}>*</span> :
                </span>
                <DateTimePicker
                  showTime={false}
                  format={"DD/MM/YYYY HH:mm:ss"}
                  value={state?.thoiGianYLenh}
                  onChange={onChangeInput("thoiGianYLenh")}
                  placeholder={t("quanLyNoiTru.chonThoiGian")}
                  disabled={!isEdit}
                  style={{
                    width: 180,
                  }}
                />
              </div>

              <div className="item">
                <SelectGroup>
                  <span
                    className={`title ${
                      state?.dsCdChinhId?.length ? "" : "validate"
                    }`}
                  >
                    {t("quanLyNoiTru.chanDoanBenh")}&nbsp;
                    <span style={{ color: "red" }}>*</span> :
                  </span>
                  <div className="select-box-chan-doan">
                    <SelectChanDoan
                      mode="multiple"
                      maxItem={1}
                      value={(state?.dsCdChinhId || []).map(
                        (item) => item + ""
                      )}
                      onChange={onChangeInput("dsCdChinhId")}
                      style={{
                        width: "100%",
                      }}
                      tagRender={CustomTag(
                        onChangeMoTa("dsMoTaChinh"),
                        state.dsMoTaChinh
                      )}
                      disabled={!isEdit}
                    />
                  </div>
                </SelectGroup>
              </div>

              <div className="item">
                <SelectGroup>
                  <span>{t("quanLyNoiTru.chanDoanKemTheo")}: </span>
                  <div className="select-box-chan-doan">
                    <SelectChanDoan
                      mode="multiple"
                      value={(state?.dsCdKemTheoId || []).map(
                        (item) => item + ""
                      )}
                      onChange={onChangeInput("dsCdKemTheoId")}
                      style={{
                        width: "100%",
                      }}
                      tagRender={CustomTag(
                        onChangeMoTa("dsMoTaKemTheo"),
                        state.dsMoTaKemTheo
                      )}
                      disabled={!isEdit}
                    />
                  </div>
                </SelectGroup>
              </div>

              <TextFieldGroup>
                <TextField
                  label={t("quanLyNoiTru.chanDoanMoTaChiTiet")}
                  className="input_custom"
                  marginTop={5}
                  onChange={onChangeInput("moTa")}
                  html={state?.moTa}
                  maxLine={1}
                  disabled={
                    !isEdit || !dataCHO_PHEP_NHAP_MO_TA_CHAN_DOAN.eval()
                  }
                />
              </TextFieldGroup>

              <div className="item">
                <span
                  className={`title ${state?.dienBienBenh ? "" : "validate"}`}
                >
                  {t("quanLyNoiTru.dienBienBenh")}&nbsp;
                  <span style={{ color: "red" }}>*</span> :
                </span>
                <Select
                  style={{ flex: 1 }}
                  data={listAllMauDienBien}
                  onChange={(e) =>
                    setState({
                      dienBienBenh: (listAllMauDienBien || []).find(
                        (x) => x.id === e
                      )?.dienBien,
                    })
                  }
                  placeholder={t("quanLyNoiTru.chonDienBienBenh")}
                  disabled={!isEdit}
                />
              </div>

              <TextFieldGroup>
                <TextField
                  className="input_custom"
                  marginTop={5}
                  onChange={onChangeInput("dienBienBenh")}
                  html={state?.dienBienBenh}
                  maxLine={1}
                  disabled={!isEdit}
                />
              </TextFieldGroup>
            </Col>
            <Col span={11} offset={2}>
              <TextField
                label={
                  <>
                    {t("khamBenh.donThuoc.ghiChu")}&nbsp;
                    {!state?.dataMore?.ghiChu && (
                      <span style={{ color: "red" }}>*</span>
                    )}
                  </>
                }
                html={state?.dataMore?.ghiChu}
                onChange={handleSetData("ghiChu")}
                disabled={!isEdit}
              />

              <SelectGroup>
                <span
                  className={`title ${state?.bacSiDieuTriId ? "" : "validate"}`}
                >
                  {t("quanLyNoiTru.bacSiDieuTri")}&nbsp;
                  <span style={{ color: "red" }}>*</span> :
                </span>
                <Select
                  value={state?.bacSiDieuTriId}
                  onChange={onChangeInput("bacSiDieuTriId")}
                  name="bacSiDieuTriId"
                  className="select-box-chan-doan"
                  data={listBacSi}
                  disabled={
                    !checkRole([
                      ROLES["QUAN_LY_NOI_TRU"].SUA_THONG_TIN_BAC_SI_DIEU_TRI,
                    ]) || !isEdit
                  }
                />
              </SelectGroup>

              <div>
                <DivInfo>
                  <span>
                    {t("khamBenh.donThuoc.loiDan")}&nbsp;
                    {!state?.dataMore?.loiDanBacSi && (
                      <span style={{ color: "red" }}>*</span>
                    )}
                    :
                  </span>
                  <Select
                    dropdownClassName="dropdown-loi-dan"
                    style={{ width: "200px", paddingLeft: "5px" }}
                    data={listAllLoiDan}
                    onChange={(e, data) => {
                      handleSetData("loiDanBacSi")(data?.ten);
                    }}
                    placeholder={t("khamBenh.donThuoc.chonLoiDan")}
                    disabled={!isEdit}
                  />
                </DivInfo>
                <TextField
                  html={state?.dataMore?.loiDanBacSi}
                  onChange={onChangeLoiDanBacSi}
                  maxLength={4000}
                  style={{
                    minHeight: 50,
                  }}
                  rows={2}
                  autoHeight={false}
                  className="noi-tru__text-field-loi-dan"
                  disabled={!isEdit}
                />
              </div>
            </Col>
          </Row>
          <Row>
            <Col span={11} style={{ display: "flex" }}>
              <DivInfo>
                <TextField
                  label={t("khamBenh.donThuoc.soNgayChoDon")}
                  html={state.dataMore?.soNgayChoDon}
                  onChange={onChangeSoNgay}
                  keyReload={"don-thuoc"}
                  onBlur={onSaveSoNgayChoDon}
                  readOnly={!isEdit}
                />
                <span>{t("khamBenh.donThuoc.ngay")}</span>
              </DivInfo>

              <DivInfo style={{ display: "-webkit-box" }}>
                <span style={{ paddingRight: "7px" }}>
                  {t("common.tuNgay")}:&nbsp;
                </span>
                <DatePicker
                  format={"DD/MM/YYYY"}
                  value={
                    state.dataMore?.tuNgay && moment(state?.dataMore?.tuNgay)
                  }
                  onChange={handleSetData("tuNgay")}
                  showTime={false}
                  disabledDate={(current) =>
                    current &&
                    (current.isAfter(state?.dataMore.denNgay) ||
                      current.isBefore(
                        moment(chiTietNguoiBenhNoiTru.thoiGianVaoVien).startOf(
                          "days"
                        )
                      ))
                  }
                  disabled={!isEdit}
                />
              </DivInfo>
            </Col>
            <Col span={11} offset={2}>
              <DivInfo style={{ flex: 2 }}>
                <span style={{ paddingRight: "7px" }}>
                  {t("common.denNgay")}:&nbsp;
                </span>
                <DatePicker
                  format={"DD/MM/YYYY"}
                  value={
                    state.dataMore?.denNgay && moment(state?.dataMore?.denNgay)
                  }
                  onChange={handleSetData("denNgay")}
                  showTime={false}
                  disabledDate={(current) =>
                    current && state?.dataMore.tuNgay
                      ? current.isBefore(state?.dataMore.tuNgay)
                      : current.isBefore(
                          moment(
                            chiTietNguoiBenhNoiTru.thoiGianVaoVien
                          ).toDate()
                        )
                  }
                  disabled={!isEdit}
                />
              </DivInfo>
            </Col>
          </Row>
          <Row>
            <Col span={11}>
              <DivInfo style={{ flex: 2 }}>
                <span style={{ paddingRight: "7px" }}>
                  {t("khamBenh.donThuoc.ngayHenKham")}:&nbsp;
                </span>
                <DateTimePicker
                  dropdownClassName="date-custom-kham-benh-don-thuoc"
                  format={"DD/MM/YYYY"}
                  value={
                    state.dataMore?.thoiGianHenKham &&
                    moment(state?.dataMore?.thoiGianHenKham)
                  }
                  onChange={handleSetData("thoiGianHenKham")}
                  showTime={false}
                  dateRender={(current) => {
                    const style = {};
                    if (current.day() === 0 || current.day() === 6) {
                      style.color = "#ff0000";
                    }
                    return (
                      <div className="ant-picker-cell-inner" style={style}>
                        {current.date()}
                      </div>
                    );
                  }}
                  disabled={!isEdit}
                />
              </DivInfo>
            </Col>
            <Col span={11} offset={2}>
              <DivInfo style={{ flex: 2 }}>
                <span style={{ paddingRight: "7px" }}>
                  {t("quanLyNoiTru.phongHenKham")}:&nbsp;
                </span>
                <Select
                  dropdownClassName="dropdown-loi-dan"
                  style={{ width: "200px", paddingLeft: "5px" }}
                  data={dataPhongThucHien}
                  value={state.dataMore?.dsPhongHenKhamId}
                  onChange={handleSetData("dsPhongHenKhamId")}
                  mode="multiple"
                  placeholder={t("quanLyNoiTru.chonPhongHenKham")}
                  disabled={!isEdit}
                />
              </DivInfo>
            </Col>
          </Row>
        </div>

        {isKeThuoc &&
          checkRole([ROLES["QUAN_LY_NOI_TRU"].THEM_CHI_DINH_THUOC]) && (
            <KeThuoc
              modeThuoc="DonThuocRaVien"
              callBackFnModalChiDinhThuoc={callBackFnModalChiDinhThuoc}
              tuNgay={state?.dataMore?.tuNgay}
              denNgay={state?.dataMore?.denNgay}
              hideThuocKho={hideThuocKho}
            />
          )}
        {/* <ThuocDaChiDinh isReadonlyDonThuocRaVien={isReadonlyDonThuocRaVien} modeThuoc="DonThuocRaVien" /> */}
        <ThuocDaChiDinh
          callBackFnModalChiDinhThuoc={() => {}}
          modeThuoc="DonThuocRaVien"
          canEdit={isKeThuoc}
          isReadonly={!isKeThuoc}
        />
      </Main>
      <ModalSignPrint ref={refModalSignPrint} />
    </Tabs.TabBox>
  );
};

export default forwardRef(DonThuocRaVien);
