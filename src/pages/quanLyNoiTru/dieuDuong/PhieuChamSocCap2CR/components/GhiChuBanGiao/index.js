import React from 'react'
import { InputTimeout, TextField } from 'components'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import { Main } from './styled'
import { get } from 'lodash'

const GhiChuBanGiao = ({ onChange, data, readOnly, ...props }) => {

    return (
        <Main>
            <FieldItem title={`Tiêu đề ghi chú`}>
                <InputTimeout
                    disabled={readOnly}
                    isTextArea={true}
                    className="flex1"
                    value={get(data, "ghiChu2")}
                    onChange={e => onChange(["ghiChu2"], e)}
                >
                </InputTimeout>
            </FieldItem>
            <FieldItem title={`Nội dung ghi chú`}>
                <InputTimeout
                    disabled={readOnly}
                    isTextArea={true}
                    className="flex1"
                    value={get(data, "ghiChu")}
                    onChange={e => onChange(["ghiChu"], e)}
                >
                </InputTimeout>
            </FieldItem>
            <FieldItem title={`Tiêu đề bàn giao`}>
                <InputTimeout
                    disabled={readOnly}
                    isTextArea={true}
                    className="flex1"
                    value={get(data, "banGiao2")}
                    onChange={e => onChange(["banGiao2"], e)}
                >
                </InputTimeout>
            </FieldItem>
            <FieldItem title={`Nội dung bàn giao`}>
                <InputTimeout
                    disabled={readOnly}
                    isTextArea={true}
                    className="flex1"
                    value={get(data, "banGiao")}
                    onChange={e => onChange(["banGiao"], e)}
                >
                </InputTimeout>
            </FieldItem>
        </Main >
    )
}
export default GhiChuBanGiao;