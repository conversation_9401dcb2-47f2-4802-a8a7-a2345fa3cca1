import React, { useEffect, useMemo, useRef } from 'react';
import { Main } from './styled';
import { SVG } from 'assets';
import { flatten, isArray } from 'lodash';
import { useSelector } from 'react-redux';

export const THUC_HIEN_THUOC = [
    { id: 1, ten: "(1) Thuốc uống" },
    { id: 2, ten: "(2) Thuốc tiêm" },
    { id: 3, ten: "(3) Thuốc truyền" },
    { id: 4, ten: "(4) Phun khí dung" },
    { id: 5, ten: "(5) Thuốc đặt" },
    { id: 6, ten: "(6) T<PERSON>yền máu và chế phẩm máu" },
    { id: 0, ten: "Khác" },
]


export const CHAM_SOC_DIEU_DUONG = [
    { ten: "(1) Thở oxy", id: 1 },
    { ten: "(2) Vỗ rung", id: 2 },
    { ten: "(3) Ăn qua sonde", id: 3 },
    { ten: "(4) Đặt sonde dạ dày", id: 4 },
    { ten: "(5) Đặt sonde tiểu", id: 5 },
    { ten: "(6) Đặt catheter TM", id: 6 },
    { ten: "(7) TB cắt chỉ", id: 7 },
    { ten: "(8) TB - RVT", id: 8 },
    { ten: "(9) TB-RVT-CL", id: 9 },
    { ten: "(10) Thụt tháo", id: 10 },
    { ten: "(11) Phòng ngừa loét", id: 11 },
    { ten: "(12) Rút dẫn lưu", id: 12 },
    { ten: "(13) Gội đầu", id: 13 },
    { ten: "(14) Tắm", id: 14 },
    { ten: "(15) Rút meches", id: 15 },
    { ten: "(16) PHCN", id: 16 },
    { ten: "(17) Hạ sốt", id: 17 },
    { ten: "(18) Vệ sinh răng miệng", id: 18 },
    { ten: "(19) Rút sonde dạ dày", id: 19 },
    { ten: "(20) Rút sonde tiểu", id: 20 },
    { ten: "(21) Rút catheter TM", id: 21 },
    { ten: "(22) Hút đờm", id: 22 },
    { ten: "(23) Cho người bệnh nằm đầu cao", id: 23 },
    { ten: "(24) Phòng ngừa té ngã", id: 24 },
    { id: 0, ten: "Khác" },
]

export const THUC_HIEN_CHI_DINH_CLS = [
    { id: 1, ten: "(1) Xét nghiệm máu" },
    { id: 2, ten: "(2) Xét nghiệm nước tiểu" },
    { id: 3, ten: "(3) Xét nghiệm đàm" },
    { id: 4, ten: "(4) Xét nghiệm phân" },
    { id: 5, ten: "(5) Xét nghiệm dịch" },
    { id: 6, ten: "(6) Điện tim" },
    { id: 7, ten: "(7) Siêu âm bụng" },
    { id: 8, ten: "(8) Siêu âm tim" },
    { id: 9, ten: "(9) X-Quang" },
    { id: 10, ten: "(10) CT" },
    { id: 11, ten: "(11) MRI" },
    { id: 12, ten: "(12) Nội soi dạ dày" },
    { id: 13, ten: "(13) Nội soi đại tràng" },
    { id: 14, ten: "(14) Nội soi phế quản" },
    { id: 0, ten: "Khác" },
]

export const getCanThiep = (type, data) => {
    return (flatten(data.map(chiTiet => {
        const listCanThiep = type == "chamSocDieuDuong" ? CHAM_SOC_DIEU_DUONG : type == "thucHienCls" ? THUC_HIEN_CHI_DINH_CLS : THUC_HIEN_THUOC;
        const keyKhac = type == "chamSocDieuDuong" ? "ctChamSocDieuDuong" : type == "thucHienCls" ? "ctThucHienCls" : "ctThucHienThuoc";
        return [{
            congViec: [(chiTiet[type] || "").split(",").map(item => listCanThiep.find(item2 => item2.id === item)?.ten).filter(item => item).join(", "), chiTiet[keyKhac] || ""].join("||"),
            thoiGianThucHien: chiTiet.thoiGianThucHien
        }];
    }))).filter(item => item.congViec && item.congViec != "||");
}


const CanThiep = ({ refModalChonCanThiep, type, onChange, data, dsCanThiep, readOnly, isEditMode = true, ...props }) => {
    const nhanVienId = useSelector(state => state.auth.auth.nhanVienId);
    const onDelete = (type, index) => () => {
        const list = (data[type] || []);
        list.splice(index, 1);
        onChange([type], list);
    }
    const onAdd = type => () => {
        const list = (data[type] || []);
        refModalChonCanThiep.current?.show({ dataSource: type == "thucHienThuoc3" ? THUC_HIEN_THUOC : type == "thucHienCls3" ? THUC_HIEN_CHI_DINH_CLS : CHAM_SOC_DIEU_DUONG }, data => {
            list.push(data);
            onChange([type], list);
        });
    }
    const onEdit = (type, index) => () => {
        const list = (data[type] || []);
        refModalChonCanThiep.current?.show({
            dataSource: type == "thucHienThuoc3" ? THUC_HIEN_THUOC : type == "thucHienCls3" ? THUC_HIEN_CHI_DINH_CLS : CHAM_SOC_DIEU_DUONG,
            dataItem: list[index]
        }, data => {
            list[index] = data;
            onChange([type], [...list]);
        });
    }
    const getCongViec = (congViec = "") => {
        let arr = [];
        if (isArray(congViec))
            arr = congViec;
        else
            arr = congViec.split("||");
        return arr?.filter(item => item).join(", ") || "";
    }
    return (
        <Main isEditMode={isEditMode}>
            <ul>
                {
                    (dsCanThiep || data?.[type] || []).map((item, index) => {
                        return <li key={index}>
                            <div className='can-thiep-dieu-duong'>
                                <span>
                                    {item?.thoiGianThucHien?.toDateObject().format("dd/MM/yyyy HH:mm")}:</span>
                                <span>{getCongViec(item.congViec)}</span>
                                <span>{item.tenNguoiThucHien ? `- ${item.tenNguoiThucHien}` : ""}
                                </span>
                                {!readOnly && (item.nguoiThucHienId == nhanVienId || !item.nguoiThucHienId) &&
                                    <>
                                        <SVG.IcEdit onClick={onEdit(type, index)} />
                                        <SVG.IcDelete onClick={onDelete(type, index)} />
                                    </>
                                }
                            </div>
                        </li>
                    })
                }
                {!readOnly &&
                    <li>
                        <SVG.IcAdd onClick={onAdd(type)} />
                    </li>
                }
            </ul>

        </Main >
    )
}
export default CanThiep;