import { Col, Row } from 'antd'
import React, { useEffect, useMemo, useRef } from 'react'
import { InputTimeout } from 'components'
import { get } from 'lodash'
import MultipleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/MultipleChoice'
import { Main } from './styled'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import ModalChonCanThiep from './ModalChonCanThiep';
import CanThiep from './CanThiep';

export const THEO_DOI_NGUOI_BENH = [
    { ten: "(1) DHST", id: 1, hint: [] },
    { ten: "(2) Cân nặng", id: 2, hint: [] },
    { ten: "(3) Dinh dưỡng", id: 3, hint: [] },
    { ten: "(4) ĐHMM", id: 4, hint: [] },
    { ten: "(5) <PERSON><PERSON> hấp", id: 5, hint: [] },
    { ten: "(6) N<PERSON>ớ<PERSON> tiểu", id: 6, hint: [] },
    { ten: "(7) V<PERSON> sinh", id: 7, hint: [] },
    { ten: "(8) Vết mổ", id: 8, hint: [] },
    { ten: "(9) Vận động", id: 9, hint: [] },
    { ten: "(10) Lọc máu", id: 10, hint: [] },
    { ten: "(11) Dịch dẫn lưu", id: 11, hint: [] },
    { ten: "(12) Dấu hiệu khản tiếng", id: 12, hint: [] },
    { ten: "(13) Theo dõi tri giác", id: 13, hint: [] },
]


const CanThiepDieuDuong = ({ onChange, onChangeMultipleKey, data, readOnly, ...props }) => {
    const refModalChonCanThiep = useRef(null);
    return (
        <Main>
            <Row gutter={[16, 16]} className="toan-than">
                <Col span={24}>
                    <FieldItem title={"(A) Thực hiện thuốc"}>
                        <CanThiep type="thucHienThuoc3"
                            refModalChonCanThiep={refModalChonCanThiep}
                            onChange={onChange}
                            data={data}
                            readOnly={readOnly}
                        />
                    </FieldItem>                    
                    <FieldItem title={"(B) Thực hiện cận lâm sàng"}>
                        <CanThiep type="thucHienCls3"
                            refModalChonCanThiep={refModalChonCanThiep}
                            onChange={onChange}
                            data={data}
                            readOnly={readOnly}
                        />
                    </FieldItem>                    
                    <FieldItem title={"(C) Chăm sóc điều dưỡng"}>
                        <CanThiep type="chamSocDieuDuong3"
                            refModalChonCanThiep={refModalChonCanThiep}
                            onChange={onChange}
                            data={data}
                            readOnly={readOnly}
                        />
                    </FieldItem>                    
                    <FieldItem title={"THEO DÕI NGƯỜI BỆNH"}>
                        <MultipleChoice
                            checkboxWidth={250}
                            type="checkbox"
                            disabled={readOnly}
                            value={get(data, "theoDoiNguoiBenh")}
                            data={THEO_DOI_NGUOI_BENH} onChange={e => {
                                onChange(["theoDoiNguoiBenh"], e);
                            }}></MultipleChoice>
                    </FieldItem>
                    <FieldItem title={"Khác"}>
                        <InputTimeout
                            disabled={readOnly}
                            value={get(data, "ctTheoDoiNguoiBenh")}
                            onChange={e => {
                                onChange(["ctTheoDoiNguoiBenh"], e);
                            }}></InputTimeout>
                    </FieldItem>
                </Col>

            </Row>
            <ModalChonCanThiep ref={refModalChonCanThiep} />
        </Main >
    )
}
export default CanThiepDieuDuong;