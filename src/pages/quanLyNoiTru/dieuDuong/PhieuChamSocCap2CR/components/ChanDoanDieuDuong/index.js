import { Col, Row } from 'antd'
import React, { useEffect } from 'react'
import { Button, InputTimeout, TextField } from 'components'
import { get } from 'lodash'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import SingleChoice from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/SingleChoice'
import { Main } from './styled'
import { SVG } from 'assets'
import { detectMob } from 'utils'

const ChanDoanDieuDuong = ({ onChange, data, readOnly, ...props }) => {

    const MUC_TIEU = [
        { id: 1, ten: "Bắt đầu" },
        { id: 2, ten: "Tiếp tục" },
        { id: 3, ten: "Kết thúc" }
    ]
    const VAN_DE = [
        { id: 1, ten: "Bắt đầu" },
        { id: 2, ten: "Tiếp tục" },
        { id: 3, ten: "Kết thúc" }
    ]
    const onAdd = () => {
        onChange([`khungChanDoanDd[${data["khungChanDoanDd"]?.length || 0}]`], {});
    }
    const onRemove = index => () => {
        const khungChanDoanDd = [...data.khungChanDoanDd];
        khungChanDoanDd.splice(index, 1);
        onChange(["khungChanDoanDd"], khungChanDoanDd);
    }
    const isMobile = detectMob();

    return (
        <Main>
            <FieldItem title="Chẩn đoán điều dưỡng" rightTitle={(data.khungChanDoanDd?.length < 10) && <Button
                type="primary" rightIcon={<SVG.IcAdd />} onClick={onAdd}>Thêm mới</Button>}
                maxWidth={isMobile ? 800 : 0}>
                <Row gutter={[8, 8]}>
                    {(data.khungChanDoanDd?.length ? data.khungChanDoanDd : [{}])?.map((item, index) => (
                        <Col span={isMobile ? 24 : 12}>
                            <FieldItem title={`Chẩn đoán điều dưỡng thứ ${index + 1}`} border={true} rightTitle={!readOnly && <SVG.IcDelete className="pointer" onClick={onRemove(index)} />}>
                                <FieldItem title="Chẩn đoán">
                                    <FieldItem
                                        titleMinWidth={200} title={<InputTimeout
                                            placeholder="Nhập chẩn đoán"
                                            disabled={readOnly}
                                            className="flex1"
                                            isTextArea={true}
                                            value={get(data, `khungChanDoanDd0[${index}].ctVanDe`)}
                                            data={VAN_DE} onChange={e => {
                                                onChange([`khungChanDoanDd0[${index}]`, "ctVanDe"], e);
                                            }}></InputTimeout>} align='flex-start' direction={2}>
                                        <SingleChoice
                                            disabled={readOnly}
                                            type="radio"
                                            className="flex1"
                                            outputTypeArray={true}
                                            value={Number(get(data, `khungChanDoanDd[${index}].vanDe`))}
                                            data={VAN_DE} onChange={e => {
                                                onChange([`khungChanDoanDd[${index}]`, "vanDe"], e);
                                            }}></SingleChoice>
                                    </FieldItem>
                                </FieldItem>
                                <FieldItem title="Mục tiêu">
                                    <FieldItem
                                        titleMinWidth={200}
                                        title={<InputTimeout
                                            placeholder="Nhập mục tiêu"
                                            disabled={readOnly}
                                            className="flex1"
                                            isTextArea={true}
                                            value={get(data, `khungChanDoanDd0[${index}].ctMucTieu`)}
                                            data={VAN_DE} onChange={e => {
                                                onChange([`khungChanDoanDd0[${index}]`, "ctMucTieu"], e);
                                            }}></InputTimeout>} align='flex-start' direction={2}>
                                        <SingleChoice
                                            disabled={readOnly}
                                            type="radio"
                                            className="flex1"
                                            outputTypeArray={true}
                                            value={Number(get(data, `khungChanDoanDd[${index}].mucTieu`))}
                                            data={MUC_TIEU} onChange={e => {
                                                onChange([`khungChanDoanDd[${index}]`, "mucTieu"], e);
                                            }}></SingleChoice>
                                    </FieldItem>
                                </FieldItem>
                            </FieldItem>
                        </Col>
                    ))}
                </Row>
            </FieldItem>
        </Main >
    )
}
export default ChanDoanDieuDuong;