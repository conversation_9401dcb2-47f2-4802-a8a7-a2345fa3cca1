import React, { useState, useEffect, useMemo } from "react";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import {
  TableWrapper,
  Pagination,
  HeaderSearch,
  Select,
  Checkbox,
} from "components";
import { useDispatch } from "react-redux";
import TabPanel from "components/MultiLevelTab/TabPanel";
import { ENUM, HIEU_LUC, YES_NO } from "constants/index";
import { InputNumber } from "antd";
import { useHistory } from "react-router-dom";
import { useEnum, useListAll, useStore } from "hooks";
import { useTranslation } from "react-i18next";
import { handleBlurInput, handleKeypressInput } from "utils";

function ThietLapChonKho(props) {
  const { t } = useTranslation();
  const { khoId, roleSave, roleEdit } = props;
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [state, _setState] = useState({
    active: false,
    data: [],
    services: [],
  });
  const [data, setData] = useState([]);
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    thietLapChonKho: {
      getListThietLapChonKho,
      onSortChange,
      onChangeInputSearch,
      createOrEdit,
    },
  } = useDispatch();

  const [listAllPhong] = useListAll("phong", {}, true);
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllKho] = useListAll("kho", {}, true);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, true);
  const [listChucVu] = useListAll("chucVu", {}, true);

  const [listAllAdminTaiKhoanHeThong] = useListAll(
    "adminTaiKhoanHeThong",
    {},
    true
  );
  const { listThietLapChonKho, size, page, totalElements, dataSortColumn } =
    useStore(
      "thietLapChonKho",
      {},
      { fields: "listThietLapChonKho,size,page,totalElements,dataSortColumn" }
    );

  const history = useHistory();

  useEffect(() => {
    getListThietLapChonKho({ khoId });
    setState({
      currentIndex: -1,
      currentItem: null,
    });
  }, [khoId]);

  useEffect(() => {
    const data = listThietLapChonKho.map((item, index) => {
      return { ...item, action: item, stt: page * size + index + 1 };
    });
    setData(data);
  }, [listThietLapChonKho, page, size]);

  const listAllAdminTaiKhoanHeThongMemo = useMemo(() => {
    return (listAllAdminTaiKhoanHeThong || []).map((item) => ({
      ten: item.ten,
      id: item.nhanVienId,
    }));
  }, [listAllAdminTaiKhoanHeThong]);

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
      khoId,
    });
  };

  const onAddNewRow = () => {
    history.push(`/kho/thiet-lap-kho-chi-dinh`);
  };

   const onSearchInput = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;

    onChangeInputSearch({
      [key]: value,
      khoId,
    });
  };

  const onChange = (key) => (e) => { 
    let value = ""; 
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) {
        value = e.target.checked;
      } else {
        value = e.target.value;
      }
    } else {
      value = e;
    }

    if (state.currentItem) {
      setState({ currentItem: { ...state.currentItem, [key]: value } });
    }
  };

  const onChangeCheckbox = (key, item, index) => (e) => {
    e.stopPropagation();
    const value = e.target.checked;
    const _currentItem =
      state.currentIndex === index && state.currentItem
        ? state.currentItem
        : item;
    const data = {
      currentItem: {
        ..._currentItem,
        [key]: value,
      },
      ...(state.currentIndex !== index && {
        currentIndex: index,
      }),
    };
    setState(data);
  };

  const onChangePage = (page) => {
    getListThietLapChonKho({ page: page - 1, khoId });
  };

  const onHandleSizeChange = (size) => {
    getListThietLapChonKho({ size: size, khoId });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 48,
      dataIndex: "stt",
      key: "stt",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.kho")}
          sort_key="khoId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["khoId"] || 0}
          searchSelect={
            <Select
              data={listAllKho}
              placeholder={t("kho.chonKho")}
              onChange={onSearchInput("khoId")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "kho",
      key: "kho",
      render: (item, list, index) => {
        if (index == state.currentIndex) {
          return (
            <Select
              data={listAllKho}
              placeholder={t("kho.chonKho")}
              onChange={onChange("khoId")}
              value={state.currentItem?.khoId}
            />
          );
        } else return item && item.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.khoaChiDinh")}
          sort_key="khoaChiDinhId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["khoaChiDinhId"] || 0}
          searchSelect={
            <Select
              data={listAllKhoa}
              placeholder={t("baoCao.chonKhoaChiDinh")}
              onChange={onSearchInput("khoaChiDinhId")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "khoaChiDinh",
      key: "khoaChiDinh",
      render: (item, list, index) => {
        if (index == state.currentIndex) {
          return (
            <Select
              data={listAllKhoa}
              placeholder={t("baoCao.chonKhoaChiDinh")}
              onChange={onChange("khoaChiDinhId")}
              value={state.currentItem?.khoaChiDinhId}
            />
          );
        } else return item && item.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("baoCao.khoaNB")}
          sort_key="khoaNbId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["khoaNbId"] || 0}
          searchSelect={
            <Select
              data={listAllKhoa}
              placeholder={t("kho.chonKhoaNb")}
              onChange={onSearchInput("khoaNbId")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "khoaNb",
      key: "khoaNb",
      render: (item, list, index) => {
        if (index == state.currentIndex) {
          return (
            <Select
              data={listAllKhoa}
              placeholder={t("kho.chonKhoaNb")}
              onChange={onChange("khoaNbId")}
              value={state.currentItem?.khoaNbId}
            />
          );
        } else return item && item.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.loaiDoiTuong")}
          sort_key="loaiDoiTuongId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["loaiDoiTuongId"] || 0}
          searchSelect={
            <Select
              placeholder={t("common.timKiem")}
              data={listAllLoaiDoiTuong}
              defaultValue=""
              onChange={onSearchInput("loaiDoiTuongId")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "loaiDoiTuong",
      key: "loaiDoiTuong",
      render: (item, list, index) => {
        if (index == state.currentIndex) {
          return (
            <Select
              data={listAllLoaiDoiTuong}
              placeholder={t("baoCao.chonLoaiDoiTuong")}
              onChange={onChange("loaiDoiTuongId")}
              value={state.currentItem?.loaiDoiTuongId}
            />
          );
        } else return item && item.ten;
      },
    },

    {
      title: (
        <HeaderSearch
          title={t("common.doiTuong")}
          sort_key="doiTuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["doiTuong"] || 0}
          searchSelect={
            <Select
              placeholder={t("common.timKiem")}
              data={listDoiTuong}
              defaultValue=""
              onChange={onSearchInput("doiTuong")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "doiTuong",
      key: "doiTuong",
      render: (item, list, index) => {
        if (index == state.currentIndex) {
          return (
            <Select
              data={listDoiTuong}
              placeholder={t("baoCao.chonLoaiDoiTuong")}
              onChange={onChange("doiTuong")}
              value={state.currentItem?.doiTuong}
            />
          );
        } else return listDoiTuong.find((e) => e.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          searchSelect={
            <Select
              data={YES_NO}
              placeholder={t("dashboard.noiTru")}
              defaultValue=""
              onChange={onSearchInput("noiTru")}
              hasAllOption={true}
            />
          }
          sort_key="noiTru"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.noiTru || 0}
          title={t("dashboard.noiTru")}
        />
      ),
      width: 100,
      dataIndex: "noiTru",
      key: "noiTru",
      align: "center",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={
              index == state.currentIndex ? state.currentItem?.noiTru : item
            }
            onChange={onChangeCheckbox("noiTru", list, index)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.laCapCuu")}
          sort_key="capCuu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["capCuu"] || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("tenTruong.capCuu")}
              onChange={onSearchInput("capCuu")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "capCuu",
      key: "capCuu",
      align: "center",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={
              index == state.currentIndex ? state.currentItem?.capCuu : item
            }
            onChange={onChangeCheckbox("capCuu", list, index)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.covid.canLamSang")}
          sort_key="canLamSang"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["canLamSang"] || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("khamBenh.covid.canLamSang")}
              onChange={onSearchInput("canLamSang")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "canLamSang",
      key: "canLamSang",
      align: "center",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={
              index == state.currentIndex ? state.currentItem?.canLamSang : item
            }
            onChange={onChangeCheckbox("canLamSang", list, index)}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.phong")}
          sort_key="phongId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.active || 0}
          searchSelect={
            <Select
              placeholder={t("common.timKiem")}
              data={listAllPhong}
              onChange={onSearchInput("phongId")}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "phong",
      key: "phong",
      render: (item, list, index) => {
        if (index == state.currentIndex) {
          return (
            <Select
              data={listAllPhong}
              placeholder={t("common.chonPhong")}
              onChange={onChange("phongId")}
              value={state.currentItem?.phongId}
            />
          );
        } else return item && item.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.chucVu")}
          sort_key="chucVuId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.active || 0}
          searchSelect={
            <Select
              data={listChucVu}
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("chucVuId")}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "chucVu",
      key: "chucVu",
      render: (item, list, index) => {
        if (index == state.currentIndex) {
          return (
            <Select
              data={listChucVu}
              placeholder={t("tiepDon.chonChucVu")}
              onChange={onChange("chucVuId")}
              value={state.currentItem?.chucVuId}
            />
          );
        } else return item && item.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.taiKhoan")}
          sort_key="nhanVienId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.active || 0}
          searchSelect={
            <Select
              placeholder={t("common.timKiem")}
              data={listAllAdminTaiKhoanHeThongMemo}
              onChange={onSearchInput("nhanVienId")}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "nhanVien",
      key: "nhanVien",
      align: "center",
      render: (item, list, index) => {
        if (index == state.currentIndex) {
          return (
            <Select
              data={listAllAdminTaiKhoanHeThongMemo}
              placeholder={t("kho.chonNhanVien")}
              onChange={onChange("nhanVienId")}
              value={state.currentItem?.nhanVienId}
            />
          );
        } else return item && item.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.uuTien")}
          sort_key="uuTien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.uuTien || 0}
          search={
            <InputNumber
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("uuTien")}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "uuTien",
      key: "uuTien",
      align: "center",
      render: (item, list, index) => {
        if (index == state.currentIndex) {
          return (
            <InputNumber
              min={0}
              type="number"
              value={state.currentItem?.uuTien}
              onKeyDown={handleKeypressInput}
              onBlur={handleBlurInput}
              placeholder={t("kho.nhapUuTien")}
              onChange={onChange("uuTien")}
            />
          );
        } else return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.coHieuLuc")}
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.active || 0}
          searchSelect={
            <Select
              data={HIEU_LUC}
              placeholder={t("kho.chonHieuLuc")}
              onChange={onSearchInput("active")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "active",
      key: "active",
      align: "center",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={
              index == state.currentIndex ? state.currentItem?.active : item
            }
            onChange={onChangeCheckbox("active", list, index)}
          />
        );
      },
    },
  ];
  const onRow = (record = {}, index) => {
    return {
      onClick: (event) => {
        if (state?.currentIndex !== index) {
          setState({
            currentItem: record,
            currentIndex: index,
          });
        }
      },
    };
  };

  const onCancel = () => {
    setState({
      currentIndex: -1,
      currentItem: null,
    });
  };

  const onSave = () => {
    const {
      id,
      active = true,
      khoId: idKho,
      loaiDoiTuongId,
      khoaChiDinhId,
      khoaNbId,
      doiTuong,
      noiTru,
      uuTien,
      nhanVienId,
      phongId,
      chucVuId,
      canLamSang,
      capCuu,
      loaiDichVu,
    } = state.currentItem || {};

    const data = {
      id,
      active,
      khoId: idKho,
      loaiDoiTuongId,
      khoaChiDinhId,
      khoaNbId,
      doiTuong,
      noiTru,
      uuTien,
      nhanVienId,
      phongId,
      chucVuId,
      canLamSang,
      capCuu,
      loaiDichVu,
    };
    if (state.currentItem) {
      createOrEdit(data).then((s) => {
        setState({
          currentIndex: -1,
          currentItem: null,
        });
        getListThietLapChonKho({ khoId });
      });
    }
  };

  return (
    <TabPanel>
      <EditWrapper
        title={t("kho.thietLapKhoChiDinh")}
        showAdded={true}
        onAddNewRow={onAddNewRow}
        roleSave={roleSave}
        roleEdit={roleEdit}
        onCancel={onCancel}
        onSave={onSave}
      >
        <TableWrapper
          scroll={{ y: 500, x: 700 }}
          columns={columns}
          dataSource={khoId ? data : []}
          onRow={onRow}
        ></TableWrapper>
        {khoId && totalElements ? (
          <Pagination
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            listData={khoId ? data : []}
            total={totalElements}
            onShowSizeChange={onHandleSizeChange}
            style={{ flex: 1, justifyContent: "flex-end" }}
          />
        ) : null}
      </EditWrapper>
    </TabPanel>
  );
}

export default ThietLapChonKho;
