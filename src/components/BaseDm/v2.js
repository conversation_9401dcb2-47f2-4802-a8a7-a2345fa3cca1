import { Form } from "antd";
import { checkRole } from "lib-utils/role-utils";
import FormWraper from "components/FormWraper";
import { HOTKEY, PAGE_DEFAULT, SIZE_DEFAULT } from "constants/index";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { combineSort } from "utils";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useLoading, useConfirm, useGuid } from "hooks";
import StyleDm11 from "pages/danhMuc/BaseDm/StyleDm11";
import { isBoolean } from "lodash";

const BaseDm = ({
  customOnSearchInput, // function

  // remove
  page = 0,
  size = 10,
  onDelete = () => {},

  //
  autoFocus = true,
  titleHead = "",
  titleMain = "",
  titleTable = "",
  totalElements,
  roleSave = [],
  roleEdit = [],
  listLink,
  initFormValue = {}, // khởi tạo giá trị cho form
  dataSearch = {},
  classNameForm = "",
  mapToForm = (data) => data,
  mapToBody = (data) => data,
  beforeSubmit = (data) => data,
  afterSubmit = () => {},
  onReset = () => {},
  addedDisabledOk = true,
  onShowUpdate,
  onCancel,
  isSubmit = () => true,
  loadingBtnSave,

  // props
  onExport,
  onImport,
  isUpdateData,

  // bắt buộc
  renderForm = () => {},
  getColumns = () => [],
  getData = () => {},
  createOrEdit,
  updateData,
  listData = [],
  dataEditDefault,
  isShowSync,
  listAllDataSync,
  titleSync,
  apiSync,
  customSetFieldsValue,
  onGetTemplate,
  onAddRow,
  menuCustom,
}) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const { showConfirm } = useConfirm();
  const [form] = Form.useForm();
  const [state, _setState] = useState({
    editStatus: false,
    dataSortColumn: { createdAt: 2 },
    disabledBtnOk: true,
    layoutMode: "default", // collapse, fullTable
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { editStatus, dataSortColumn, layoutMode } = state;
  const layerId = useGuid();
  const refAutoFocus = useRef();
  const refClickBtnAdd = useRef(null);
  const refClickBtnSave = useRef(null);
  const { onAddLayer, onRemoveLayer, onRegisterHotkey } = useDispatch().phimTat;
  const { canSave, canEdit } = useMemo(() => {
    return {
      canSave: checkRole(roleSave),
      canEdit: checkRole(roleEdit),
    };
  }, []);

  const setFieldsValue = (value) => {
    if (customSetFieldsValue) {
      customSetFieldsValue({ form })(value);
    } else {
      form.setFieldsValue(value);
    }
  };

  const setDefaultForm = (initFormValue = {}) => {
    setState({
      editStatus: false,
      disabledBtnOk:
        initFormValue && Object.values(initFormValue)?.length ? false : true,
    });
    updateData({ dataEdit: initFormValue });
    // form.resetFields();
    // form.setFieldsValue(initFormValue);

    // sử dụng cách dưới để tránh render lại form 2 lần (2 lần set như trên)
    const formObj = form.getFieldsValue();
    Object.keys(formObj).forEach((item) => (formObj[item] = undefined));
    setFieldsValue({ ...formObj, ...initFormValue });

    onReset({ setState, updateData, form, refAutoFocus });
  };
  ///
  useEffect(() => {
    const sort = combineSort(dataSortColumn);
    const params = { ...dataSearch, sort };
    getData(params);
    onAddLayer({ layerId });
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: () => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current(e);
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId });
    };
  }, []);

  const onClickSort = (key, value) => {
    const sort = { ...dataSortColumn, [key]: value };
    delete sort.createdAt;
    const res = combineSort(sort);
    setState({ dataSortColumn: sort });
    getData({
      isSave: true,
      ...dataSearch,
      page: PAGE_DEFAULT,
      sort: res,
    });
  };

  ///
  const onSearchInput = customOnSearchInput
    ? customOnSearchInput({ dataSortColumn })
    : (name) => (e) => {
        const value = e?.hasOwnProperty("target")
          ? e?.target?.type === "checkbox"
            ? e?.target?.checked
            : e?.target?.value
          : e?.hasOwnProperty("_d")
          ? moment(e._d)
          : e;
        getData({
          isSave: true,
          ...dataSearch,
          page: PAGE_DEFAULT,
          size,
          [name]: value,
          sort: combineSort(dataSortColumn),
        });
      };

  ///
  const onPageChange = (page) => {
    getData({
      isSave: false,
      ...dataSearch,
      page: page - 1,
      sort: combineSort(dataSortColumn),
    });
  };

  ///
  const onSizeChange = (size) => {
    getData({
      isSave: true,
      ...dataSearch,
      page: 0,
      size,
      sort: combineSort(dataSortColumn),
    });
  };

  ///
  const handleAdded = (e) => {
    if (e?.preventDefault) e.preventDefault();

    form
      .validateFields()
      .then(async (values) => {
        if ((state.disabledBtnOk && addedDisabledOk) || !isSubmit()) return;
        const body = await beforeSubmit({
          ...values,
          id: dataEditDefault.id,
        });
        if (!editStatus) {
          setState({ dataSortColumn: { createdAt: 2 } });
        }

        showLoading();

        createOrEdit(mapToBody(body))
          .then((res) => {
            if (res && res.code === 0) {
              const params = {
                ...dataSearch,
                isSave: true,
                page:
                  !editStatus || isUpdateData ? dataSearch.page : PAGE_DEFAULT,
                sort: combineSort(
                  dataEditDefault.id ? dataSortColumn : { createdAt: 2 }
                ),
              };
              if (isUpdateData) {
                getData(params);
                onClickItem(res.data);
              } else {
                setDefaultForm();
                getData(params);
                afterSubmit({ form });
              }
            }
          })
          .catch((err) => {
            console.error(err);
          })
          .finally(() => {
            hideLoading();
          });
      })
      .catch((error) => {
        console.error(error);
      });
  };
  refClickBtnSave.current = handleAdded;

  ///
  const onAdd = () => {
    setDefaultForm(initFormValue);
    updateData({
      dataEditDefault: initFormValue || {},
      dataEdit: initFormValue || {},
    });
    refAutoFocus.current && refAutoFocus.current.focus();
    if (onAddRow) onAddRow();
  };
  refClickBtnAdd.current = onAdd;

  ///
  const handleCancel = () => {
    if (editStatus) {
      setFieldsValue(mapToForm(dataEditDefault));
    } else {
      setDefaultForm();
    }
    if (onCancel) onCancel({ form, dataEditDefault });
  };

  ///
  const handleDeleteItem = (item) => {
    showConfirm(
      {
        title: t("common.thongBao"),
        content: t("common.banCoChacMuonXoaBanGhiNay"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showImg: true,
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        onDelete(item?.id);
      }
    );
  };

  ///
  const checkChangeField =
    (fieldName, returnBool = false) =>
    (data) => {
      const equalField = (formValue, editValue) =>
        formValue === editValue ||
        (!formValue &&
          !editValue &&
          !isBoolean(formValue) &&
          !isBoolean(editValue));

      if (returnBool) {
        return equalField(data, dataEditDefault[fieldName]);
      }

      const values = form.getFieldsValue() || {};

      const mapBool = Object.keys(values).reduce(
        (acc, item) => ({
          ...acc,
          [item]: equalField(values[item], dataEditDefault[item]),
        }),
        {}
      );
      if (fieldName) {
        mapBool[fieldName] = equalField(data, dataEditDefault[fieldName]);
      }

      const disabledBtnOk = Object.values(mapBool).reduce(
        (acc, item) => acc && item,
        true
      );

      if (fieldName) setFieldsValue({ [fieldName]: data });
      if (disabledBtnOk != state.disabledBtnOk) {
        setState({ disabledBtnOk: disabledBtnOk });
      }
    };

  const onClickItem = (record) => {
    // onShowAndHandleUpdate(record);
    setState({ editStatus: true, disabledBtnOk: true });
    updateData({ dataEdit: record, dataEditDefault: record });
    setFieldsValue(mapToForm(record));
    onShowUpdate && onShowUpdate({ form }, record);
  };

  return (
    <>
      <StyleDm11
        title={
          titleTable
            ? titleTable
            : `${t("danhMuc.danhMuc")} ${titleHead || titleMain}`
        }
        breadcrumb={listLink}
        columns={getColumns({
          dataSearch,
          onClickSort,
          dataSortColumn,
          onSearchInput,
          handleDeleteItem,
          page: dataSearch.page || PAGE_DEFAULT,
          size: dataSearch.size || SIZE_DEFAULT,
        })}
        rightContent={
          <FormWraper
            disabled={editStatus ? !canEdit : !canSave}
            form={form}
            layout="vertical"
            className={`form-custom ${classNameForm}`}
            onFieldsChange={checkChangeField()}
            {...(initFormValue && { initialValues: initFormValue })} // xử lý cho case mới vào chưa click button Thêm mới đang ko tự set vào form
          >
            {renderForm({
              form,
              editStatus,
              autoFocus,
              refAutoFocus,
              checkChangeField,
              dataEditDefault,
            })}
          </FormWraper>
        }
        roleSave={roleSave}
        roleEdit={roleEdit}
        dataEditDefault={dataEditDefault}
        onAdd={canSave ? onAdd : null}
        onCancel={handleCancel}
        onSave={handleAdded}
        editStatus={editStatus}
        onClickItem={onClickItem}
        onChangePage={onPageChange}
        onSizeChange={onSizeChange}
        listData={listData}
        totalElements={totalElements}
        page={page}
        size={size}
        onExport={onExport}
        onImport={onImport}
        disabledBtnOk={state.disabledBtnOk && addedDisabledOk}
        isShowSync={isShowSync}
        apiSync={apiSync}
        titleSync={titleSync}
        listAllDataSync={listAllDataSync}
        loadingBtnSave={loadingBtnSave}
        onGetTemplate={onGetTemplate}
        menuCustom={menuCustom}
      ></StyleDm11>
    </>
  );
};

export default BaseDm;
