import React, {
  Fragment,
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Col, message, Row } from "antd";
import { useLocation } from "react-router-dom";
import { GlobalStyle, Main } from "./styled";
import stringUtils from "mainam-react-native-string-utils";
import InputGroup from "components/editor/cores/InputGroup";
import moment from "moment";
import { ReactQrCode, TextField } from "components";
import { useTranslation } from "react-i18next";
import {
  A4,
  DOI_TUONG_KCB,
  ENUM,
  TRANG_THAI_NB,
  HOTKEY,
  LOAI_IN_BANG_KE_CHI_PHI,
} from "constants/index";
import { addFooterPage, pageType, pdfGenerator } from "utils/editor-utils";
import printProvider from "data-access/print-provider";
import { useEnum, useGuid, useLoading, useStore } from "hooks";
import { Button } from "components";
import { SVG } from "assets/index";
import { docSoViet, isArray, openInNewTab } from "utils/index";
import useBangKe from "./hook";
import Barcode from "components/editor/cores/Barcode";
import { debounce } from "lodash";
import File from "pages/editor/report/components/File"; //không xóa cái này, ImageSign -> useSign -> refValue đang dùng cần import File
import ImageSign from "../../cores/ImageSign";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import useThietLapChung from "./hook/useThietLapChung";
import {
  renderTextKiemTra,
  renderThongTinCon,
  renderThongTinSanPhu,
  renderTuyen,
} from "./utils";
import { useIsFetching } from "@tanstack/react-query";
import { Document, Page, pdfjs } from "react-pdf";
import { centralizedErrorHandling } from "lib-utils";

pdfjs.GlobalWorkerOptions.workerSrc = `/js/pdf.worker.min.js`;

// import FontItalicURL from "/fonts/roboto/roboto-v30-vietnamese_latin-ext_latin-italic.ttf";
// import FontBoldURL from "/fonts/roboto/roboto-v30-vietnamese_latin-ext_latin-900.ttf";

const BangKePrint = (props) => {
  const refMain = useRef(null);
  const refIsObserving = useRef(null);
  const refTimeoutPrint = useRef(null);
  const refPrint = useRef(null);
  const refTimeout = useRef(null);
  const { t } = useTranslation();
  const { hideLoading } = useLoading();
  const [listDoiTuongKCB] = useEnum(ENUM.DOI_TUONG_KCB);
  const { search, pathname } = useLocation();
  const layerId = useGuid();
  const { nbDotDieuTriId } = useParams();

  const {
    dataTHIET_LAP_CHAN_KY_BANG_KE,
    dataTHIET_LAP_CHAN_KY_NGANG_BANG_KE,
    dataBANG_KE_TONG_HOP_SUA_DOI,
    dataTHEM_TEXT_BANG_KE_CHUA_DUYET_BH,
    dataKHONG_HIEN_THI_TEXT_TONG_HOP_TREN_BANG_KE,
    dataCHI_HIEN_THI_SO_PHIEU_TRONG_SO_PHOI,
    dataSO_PHOI_THANH_TOAN,
    dataBANG_KE_CHI_PHI_HIEN_THI_TONG_KET,
    dataBANG_KE_CHI_PHI_AN_HOAN_TAM_UNG,
    dataPHIEU_THU_BIEN_LAI_VIEN_PHI,
    dataCHAN_DOAN_THEO_BANG_KE,
    dataBANG_KE_THONG_TIN_BO_SUNG,
    dataHIEN_THU_TIEN_NB_PHAI_TRA_TREN_BANG_KE,
    dataHIEN_THI_CHAN_DOAN_MO_TA_CUNG_HANG_CHAN_DOAN_CHINH_TREN_BANG_KE,
    dataCHO_IN_BANG_KE_TIEN_ICH_NGUOI_BENH_NGOAI_TRU,
    dataBANG_KE_CHI_PHI_KHUNG_DICH_VU,
  } = useThietLapChung();

  const isFetching = useIsFetching();

  const [state, _setState] = useState({
    listPhieu: null,
    isLoadedPhieu: false,
    notFoundData: true,
    urlFileLocal: null,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const headless = pathname.indexOf("headless-print-file") != -1;
  const currentBaoCao = useStore("files.currentBaoCao", null);

  const {
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    phieuIn: { getFilePhieuIn },
  } = useDispatch();

  const refTimout = useRef();
  const queries = useMemo(() => {
    const queries = {};
    search
      .substring("1")
      .split("&")
      .forEach((item) => {
        const arr = item.split("=");
        let value = arr[1];
        if (
          arr[0].toLowerCase().includes("thoigian") &&
          moment(+arr[1]).isValid()
        ) {
          value = moment(+arr[1]).format("DD-MM-YYYY HH:mm:ss");
        }
        queries[arr[0]] = value;
      });
    return queries;
  }, [search]);

  const isHienThiChanDoanMoTaCungHangChanDoanChinhTrenBangKe =
    dataHIEN_THI_CHAN_DOAN_MO_TA_CUNG_HANG_CHAN_DOAN_CHINH_TREN_BANG_KE?.eval() &&
    (queries.loai == 22 || queries.loai == 12);

  const {
    renderPrice,
    renderNumber,
    getDataToPrint,
    colgroup,
    renderBody,
    renderTime,
    numberSpan,
    showButtonPrint,
    print,
    dsChuKyProps,
    isHideTenNguoiLapBangKe,
    isTangSizeTienTraLai,
    isTangSizeTienThuThem,
    isBangKeTienIchNbNgoaiTru,
    hideTenNguoiInBangKe,
    listViTriChanKyBangKe,
    showSoTienTamUng,
    isShowMucHuongBHYT,
    gopChanTongKhiQuaTrang,
    showTextNguoiBaoLanh,
    hideTextToiDaNhanPhim,
    hideTenNguoiLapBangKe,
    showTextQuanHeVoiNguoiBenh,
    onChangePhim,
    showTextNguoiBenhKyTen,
    formatName,
    dsPhieuInKem,
    hienThiTienDvPhaiTraKhac,
  } = useBangKe(queries);

  refPrint.current = print;

  useEffect(() => {
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F3, //F3
          onEvent: () => {
            refPrint.current();
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      const { listPhieu } = await getDataToPrint({
        nbDotDieuTriId,
      });
      hideLoading();

      const listPhieuFilter = (listPhieu || []).filter((item) => {
        if (isBangKeTienIchNbNgoaiTru) {
          //61: Thu phí, 62 Tiện ích, 63: Hao phí, 64: Tăng cường
          return item.dataPrint?.loai === 62; //lọc chỉ hiển thị loại tiện ích
        }
        return true;
      });

      if (
        isBangKeTienIchNbNgoaiTru &&
        (listPhieu || []).length &&
        !listPhieuFilter.length
      ) {
        message.error(t("thuNgan.nguoiBenhKhongCoDichVuTuTra"));
        setState({ notFoundData: true, listPhieu: [] });
      } else setState({ listPhieu: listPhieuFilter, notFoundData: false });
    };
    if (isFetching === 0) {
      fetchData();
    }
  }, [isBangKeTienIchNbNgoaiTru, nbDotDieuTriId, isFetching]);

  const renderImageSign = (chuKyField, dataPrint) => {
    const chuKyProps = dsChuKyProps.find(
      (item) => item.props.chuKyBangKe === chuKyField
    );
    if (!chuKyProps) return null;
    //nếu cấp ký < chuKySo hiện tại thì ẩn button ký
    if (chuKyProps.props?.capKy > (dataPrint.lichSuKy?.chuKySo || 1) + 1) {
      return null;
    }

    return (
      <ImageSign
        component={{
          props: {
            ...chuKyProps.props,
            dataSign: {
              soPhieu: dataPrint?.id,
              duLieu: dataPrint,
              id: dataPrint?.id,
              api: "api/his/v1/nb-dot-dieu-tri/bang-ke-chi-phi",
            },
          },
        }}
        form={dataPrint}
      />
    );
  };

  const checkShowTenKy = (key, dataPrint) => {
    let isShow = true;
    const _chuKyProps = dsChuKyProps.find(
      (item) => item.props.chuKyBangKe === key
    );

    if (_chuKyProps) {
      isShow = _chuKyProps?.props?.capKy > (dataPrint?.lichSuKy?.chuKySo || 0);
    }
    return isShow;
  };

  const renderMain = useMemo(() => {
    let soPhieu = "";
    if (dataCHI_HIEN_THI_SO_PHIEU_TRONG_SO_PHOI?.eval()) {
      const array = dataSO_PHOI_THANH_TOAN.split(":");
      soPhieu = array?.[1]?.slice(0, -1);
    }
    const tenPhongThucHien =
      (state.listPhieu || []).find(
        (item) => !!item?.dataPrint?.tenPhongThucHien
      )?.dataPrint?.tenPhongThucHien || null;

    return state.listPhieu?.map((item, index) => {
      const { dataPrint = {}, listTable } = item;

      const isShowTenNguoiIn = checkShowTenKy("nguoi_lap_bang_ke", dataPrint);
      const isShowTenThuNgan = checkShowTenKy("ke_toan_vien_phi", dataPrint);

      const parseChuaDuyetBhConfig = (str) => {
        if (!str) return { enabled: false };

        const [enabledStr, doiTuongStr] = str.split("/");
        const enabled = enabledStr.eval();

        let doiTuongKcbList = null;

        if (doiTuongStr) {
          doiTuongKcbList = doiTuongStr
            .split(",")
            .map((s) => parseInt(s.trim()))
            .filter((n) => !isNaN(n));
        }

        return { enabled, doiTuongKcbList };
      };

      const { enabled, doiTuongKcbList } = parseChuaDuyetBhConfig(
        dataTHEM_TEXT_BANG_KE_CHUA_DUYET_BH
      );

      const isDoiTuongKcbPhuHop = doiTuongKcbList
        ? doiTuongKcbList.includes(dataPrint.doiTuongKcb)
        : [
            DOI_TUONG_KCB.NGOAI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ].includes(dataPrint.doiTuongKcb) ||
          ([
            DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
          ].includes(dataPrint.doiTuongKcb) &&
            [
              TRANG_THAI_NB.DA_RA_VIEN,
              TRANG_THAI_NB.HEN_DIEU_TRI,
              TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
              TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
              TRANG_THAI_NB.DA_TIEP_DON_HEN_DIEU_TRI,
            ].includes(dataPrint.trangThaiNb));

      const isShowChuaDuyetBH =
        enabled &&
        dataPrint.tienBhThanhToan > 0 &&
        [null, 10].includes(dataPrint.trangThaiQuyetToan) &&
        isDoiTuongKcbPhuHop;

      const isShowTongKetChiPhi =
        [
          DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
        ].includes(dataPrint.doiTuongKcb) &&
        [
          LOAI_IN_BANG_KE_CHI_PHI.TONG_HOP,
          LOAI_IN_BANG_KE_CHI_PHI.NOI_TRU,
          LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI,
          LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_DICH_VU,
        ].findIndex((item) => item == queries.loai) > -1 &&
        dataBANG_KE_CHI_PHI_HIEN_THI_TONG_KET?.eval();

      const renderChanKy = () => {
        const renderChanKyGiamDinhBhYt = () => {
          return (
            <Col span={numberSpan}>
              <div className="center bold fs13"> GIÁM ĐỊNH BHYT</div>
              <div className="center kySo">
                <i>(Ký, ghi rõ họ tên)</i>
                {renderImageSign("giam_dinh_bhyt", dataPrint)}
              </div>
              <div style={{ height: "70px" }}></div>
              <div className="minH-14"></div>
            </Col>
          );
        };

        const renderChanKyKeToanVienPhi = () => {
          const showTenKeToanVienPhiLaNguoiIn =
            showTextNguoiBenhKyTen &&
            queries.maManHinh == "004" &&
            queries.maViTri === "00401";
          // đối với viện Đống Đa dùng chung thiếp lập chung dataHIEN_THI_TEN_NB_BANG_KE để hiển thị tên kế toán viện phí
          // chỉ hiện khi được in ở màn thu ngân
          return (
            <Col span={numberSpan}>
              <div className="center bold fs13">KẾ TOÁN VIỆN PHÍ</div>
              <div className="center kySo">
                <i>(Ký, ghi rõ họ tên)</i>
                {renderImageSign("ke_toan_vien_phi", dataPrint)}
              </div>
              {isShowTenThuNgan && (
                <>
                  <div
                    style={{
                      height: showTenKeToanVienPhiLaNguoiIn ? 55 : "70px",
                    }}
                  ></div>
                  <div className="center minH-14">
                    {showTenKeToanVienPhiLaNguoiIn
                      ? dataPrint?.tenNguoiIn
                      : dataPrint.tenThuNgan}
                  </div>
                </>
              )}
            </Col>
          );
        };

        const renderChanKyXacNhanCuaNb = () => {
          const isShowNguoiBaoLanh =
            dataBANG_KE_THONG_TIN_BO_SUNG?.eval() &&
            ["EMR_BA251", "EMR_BA134"].includes(queries?.maBaoCao);

          const _tenNguoiBaoLanh =
            dataPrint?.tenNguoiBaoLanh || dataPrint?.tenNguoiBaoLanh2;
          const _tenMoiQuanHe =
            dataPrint?.tenMoiQuanHe || dataPrint?.tenMoiQuanHe2;

          return (
            <Col
              span={numberSpan}
              className={numberSpan === "4-8" ? "sign-nb" : ""}
            >
              <div className="center bold fs13">XÁC NHẬN CỦA NGƯỜI BỆNH</div>
              <div className="center kySo">
                <i>(Ký, ghi rõ họ tên)</i>
                {renderImageSign("xac_nhan_nguoi_benh", dataPrint)}
              </div>
              {showTextNguoiBaoLanh && (
                <div className="center minH-14 ten-nguoi-bao-lanh">
                  {_tenNguoiBaoLanh ? (
                    <span>{_tenNguoiBaoLanh}</span>
                  ) : (
                    <TextField />
                  )}
                </div>
              )}

              {!hideTextToiDaNhanPhim && (
                <div className="center toi-da-nhan-phim-xquang">
                  <b>(Tôi đã nhận</b>
                  <TextField
                    html={dataPrint?.soPhim}
                    onChange={onChangePhim("soPhim")}
                  />
                  <b>phim</b>
                  <TextField
                    onChange={onChangePhim("loaiPhim")}
                    html={`${dataPrint.loaiPhim || "Xquang/CT/MRI"})`}
                    style={{ minWidth: "100px", fontSize: "10px" }}
                  />
                </div>
              )}
              {showTextNguoiBenhKyTen && (
                <div className="center minH-14 ten-nguoi-bao-lanh">
                  <div style={{ height: 50 }}></div>
                  <span>{formatName(dataPrint.tenNb)}</span>
                </div>
              )}
              {showTextQuanHeVoiNguoiBenh && (
                <div className="center minH-14">Quan hệ với người bệnh</div>
              )}
              {isShowNguoiBaoLanh && (
                <div className="center minH-14 moi-quan-he">
                  {_tenMoiQuanHe ? <span>{_tenMoiQuanHe}</span> : <TextField />}
                </div>
              )}
            </Col>
          );
        };

        const renderChanKyNguoiLapBangKe = () => {
          return (
            <Col span={numberSpan}>
              <div className="center bold fs13">NGƯỜI LẬP BẢNG KÊ</div>
              <div className="center kySo">
                <i>(Ký, ghi rõ họ tên)</i>
                {renderImageSign("nguoi_lap_bang_ke", dataPrint)}
              </div>
              {isShowTenNguoiIn &&
              !isHideTenNguoiLapBangKe &&
              !isBangKeTienIchNbNgoaiTru &&
              !hideTenNguoiLapBangKe ? (
                <>
                  <div
                    style={{ height: hideTextToiDaNhanPhim ? 50 : 75 }}
                  ></div>
                  <div className="center minH-14">{dataPrint.tenNguoiIn}</div>
                </>
              ) : null}
            </Col>
          );
        };

        return (
          <>
            {dataTHIET_LAP_CHAN_KY_NGANG_BANG_KE?.eval() ? (
              <>
                {isArray(listViTriChanKyBangKe, 4) &&
                ["EMR_BA380", "EMR_BA381", "EMR_BA251", "EMR_BA134"].includes(
                  queries?.maBaoCao
                ) ? (
                  listViTriChanKyBangKe.map((viTri, idx) => (
                    <Fragment key={idx}>
                      {viTri == 1 && renderChanKyGiamDinhBhYt()}
                      {viTri == 2 && renderChanKyKeToanVienPhi()}
                      {viTri == 3 && renderChanKyXacNhanCuaNb()}
                      {viTri == 4 && renderChanKyNguoiLapBangKe()}
                    </Fragment>
                  ))
                ) : (
                  <>
                    {!["EMR_BA405"].includes(queries?.maBaoCao) &&
                      renderChanKyGiamDinhBhYt()}
                    {renderChanKyKeToanVienPhi()}
                    {renderChanKyXacNhanCuaNb()}
                    {!["EMR_BA405"].includes(queries?.maBaoCao) &&
                      dataTHIET_LAP_CHAN_KY_BANG_KE?.eval() &&
                      dataPrint?.doiTuongKcb == 3 && (
                        <Col span={numberSpan}>
                          <div className="center bold fs13">
                            ĐẠI DIỆN CƠ SỞ KCB
                          </div>
                          <div className="center kySo">
                            <i>(Ký, ghi rõ họ tên)</i>
                            {renderImageSign("dai_dien_co_so_kcb", dataPrint)}
                          </div>
                          <div style={{ height: "70px" }}></div>
                          <div className="minH-14"></div>
                        </Col>
                      )}
                    {!["EMR_BA405"].includes(queries?.maBaoCao) &&
                      renderChanKyNguoiLapBangKe()}
                  </>
                )}
              </>
            ) : (
              <>
                <Col span={numberSpan}>
                  <div className="center bold fs13">NGƯỜI LẬP BẢNG KÊ</div>
                  <div className="center kySo">
                    <i>(Ký, ghi rõ họ tên)</i>
                    {renderImageSign("nguoi_lap_bang_ke", dataPrint)}
                  </div>
                  {isShowTenNguoiIn &&
                  !isHideTenNguoiLapBangKe &&
                  !isBangKeTienIchNbNgoaiTru &&
                  !hideTenNguoiLapBangKe ? (
                    <>
                      <div style={{ height: "70px" }}></div>
                      <div className="center minH-14">
                        {dataPrint.tenNguoiIn}
                      </div>
                    </>
                  ) : null}
                </Col>
                <Col span={numberSpan}>
                  <div className="center bold fs13">KẾ TOÁN VIỆN PHÍ</div>
                  <div className="center kySo">
                    <i>(Ký, ghi rõ họ tên)</i>
                    {renderImageSign("ke_toan_vien_phi", dataPrint)}
                  </div>
                  {isShowTenThuNgan && (
                    <>
                      <div style={{ height: "70px" }}></div>
                      <div className="center minH-14">
                        {dataPrint.tenThuNgan}
                      </div>
                    </>
                  )}
                </Col>
                {dataTHIET_LAP_CHAN_KY_BANG_KE?.eval() &&
                  dataPrint?.doiTuongKcb == 3 && (
                    <Col span={numberSpan}>
                      <div className="center bold fs13">ĐẠI DIỆN CƠ SỞ KCB</div>
                      <div className="center kySo">
                        <i>(Ký, ghi rõ họ tên)</i>
                        {renderImageSign("dai_dien_co_so_kcb", dataPrint)}
                      </div>
                      <div style={{ height: "70px" }}></div>
                      <div className="minH-14"></div>
                    </Col>
                  )}
                <Col span={numberSpan}>
                  <div className="center bold fs13">
                    XÁC NHẬN CỦA NGƯỜI BỆNH
                  </div>
                  <div className="center kySo">
                    <i>(Ký, ghi rõ họ tên)</i>
                    {renderImageSign("xac_nhan_nguoi_benh", dataPrint)}
                  </div>
                  {!hideTextToiDaNhanPhim && (
                    <div className="center" style={{ height: 20 }}>
                      <b style={{ fontSize: 10 }}>
                        (Tôi đã nhận ...... phim ...... Xquang/CT/MRI)
                      </b>{" "}
                    </div>
                  )}
                  <div style={{ height: "50px" }}></div>
                  {showTextQuanHeVoiNguoiBenh && (
                    <div className=" minH-14">Quan hệ với người bệnh</div>
                  )}
                </Col>
                <Col span={numberSpan}>
                  <div className="center bold fs13"> GIÁM ĐỊNH BHYT</div>
                  <div className="center kySo">
                    <i>(Ký, ghi rõ họ tên)</i>
                    {renderImageSign("giam_dinh_bhyt", dataPrint)}
                  </div>
                  <div style={{ height: "70px" }}></div>
                  <div className="minH-14"></div>
                </Col>
              </>
            )}
          </>
        );
      };
      const renderTongTienVaChanKy = () => {
        return (
          (
            <Row gutter={12}>
              <Col span={12}></Col>
            </Row>
          ),
          (
            <Row style={{ marginTop: 5 }}>
              {["EMR_BA251", "EMR_BA381"].includes(queries?.maBaoCao) && (
                <Col span={12}>
                  Người bệnh đã được tư vấn và đồng ý thanh toán
                </Col>
              )}
              <Col
                span={
                  ["EMR_BA251", "EMR_BA381"].includes(queries?.maBaoCao)
                    ? 12
                    : 24
                }
                style={{
                  textAlign: "end",
                  paddingRight: dataTHIET_LAP_CHAN_KY_NGANG_BANG_KE?.eval()
                    ? "25px"
                    : numberSpan === 8
                    ? "40px"
                    : "125px",
                }}
              >
                {moment(
                  dataPrint.thoiGianRaVien || dataPrint.thoiGianIn || new Date()
                ).format("HH:mm \\Ngà\\y DD t\\háng MM nă\\m YYYY")}
              </Col>
              {renderChanKy()}
            </Row>
          )
        );
      };

      const spanTienTamUng = gopChanTongKhiQuaTrang
        ? isTangSizeTienThuThem
          ? 18
          : 16
        : 20;

      const renderSoTienThuThem = () => {
        if (
          queries?.maBaoCao == "EMR_BA134" &&
          queries?.loai == 10 &&
          queries?.ma == "P032" &&
          dataPrint?.tienTraLai > 0
        ) {
          return null;
        } else {
          return (
            <>
              <Col
                span={spanTienTamUng}
                className="flex jSb"
                style={
                  isTangSizeTienThuThem
                    ? {
                        fontSize: 22,
                      }
                    : {}
                }
              >
                <span>Số tiền cần thu thêm: </span>
                <span className="bold">
                  {renderPrice(dataPrint.tienThuThem)}
                </span>
              </Col>
              <Col
                span={4}
                style={
                  isTangSizeTienThuThem
                    ? {
                        fontSize: 22,
                      }
                    : {}
                }
              >
                đồng
              </Col>
            </>
          );
        }
      };

      const renderSoTienCanTraLai = () => {
        return (
          <>
            <Col
              span={spanTienTamUng}
              className="flex jSb"
              style={
                isTangSizeTienTraLai
                  ? {
                      fontSize: 22,
                    }
                  : {}
              }
            >
              <span>Số tiền cần trả lại: </span>
              <span className="bold">{renderPrice(dataPrint.tienTraLai)}</span>
            </Col>
            <Col
              span={4}
              style={
                isTangSizeTienTraLai
                  ? {
                      fontSize: 22,
                    }
                  : {}
              }
            >
              đồng
            </Col>
          </>
        );
      };

      const renderChanDoanXacDinh = () => {
        const chanDoanXacDinh =
          ["EMR_BA134", "EMR_BA251"].includes(queries.maBaoCao) &&
          dataCHAN_DOAN_THEO_BANG_KE.eval()
            ? dataPrint.cdXacDinh1
            : dataPrint.cdChinh;
        const moTaRaVien = isHienThiChanDoanMoTaCungHangChanDoanChinhTrenBangKe
          ? dataPrint.moTaRaVien
          : "";
        return (
          <>
            {chanDoanXacDinh}
            {chanDoanXacDinh && moTaRaVien && `; ${moTaRaVien}`}
          </>
        );
      };

      const showPhongThucHien =
        (dataCHO_IN_BANG_KE_TIEN_ICH_NGUOI_BENH_NGOAI_TRU?.eval() &&
          queries.ma == "P032" &&
          queries.loai == 10 &&
          queries.maBaoCao == "EMR_BA134") ||
        isBangKeTienIchNbNgoaiTru;

      return (
        <div
          className="bang-ke"
          key={index}
          style={{
            pageBreakBefore: index ? "always" : "avoid",
            pageBreakInside: "avoid",
          }}
        >
          {[
            <div className="form-header flex" key={index}>
              <div className="header-left ">
                <div>{dataPrint.tieuDeTrai1}</div>
                <div>
                  <b>{dataPrint.tieuDeTrai2}</b>
                </div>

                <div className="header-level-1">
                  {showPhongThucHien && tenPhongThucHien ? (
                    <b>Phòng: {tenPhongThucHien}</b>
                  ) : (
                    <b>Khoa: {dataPrint?.tenKhoaNb}</b>
                  )}
                </div>
                <div className="header-level-2">
                  <b>Mã khoa: </b> {dataPrint?.maBhyt}
                </div>
              </div>
              <div>
                <Barcode
                  component={{
                    props: {
                      fieldName: "maHoSo",
                      width: 170,
                      height: 40,
                    },
                  }}
                  form={{
                    maHoSo: dataPrint?.maHoSo,
                  }}
                ></Barcode>
              </div>
              <div className="header-right">
                <div>
                  <b>Mẫu số: 01/KBCB</b>
                </div>
                <div>
                  <b>{t("common.maNguoiBenh")}:</b> {dataPrint?.maNb}
                </div>
                <div>
                  <b>Mã sổ khám bệnh:</b> {dataPrint?.maHoSo}
                </div>
                {dataPrint.doiTuongKcb == 3 && (
                  <div>
                    <b>Mã bệnh án:</b> {dataPrint?.maBenhAn}
                  </div>
                )}
                {(queries.loai == 10 ||
                  ["EMR_BA106", "EMR_BA134", "EMR_BA222", "EMR_BA323"].includes(
                    queries?.maBaoCao
                  )) &&
                  (dataCHI_HIEN_THI_SO_PHIEU_TRONG_SO_PHOI?.eval() ? (
                    <div>
                      <b>Số phơi:</b>{" "}
                      <span style={{ fontSize: "24px", fontWeight: "bold" }}>
                        {dataPrint?.soPhoi?.substr(
                          dataPrint?.soPhoi?.length - soPhieu
                        )}
                      </span>
                    </div>
                  ) : (
                    <div>
                      <b>Số phơi:</b> {dataPrint?.soPhoi}
                    </div>
                  ))}
              </div>
            </div>,
            <div className="title relative">
              <div
                className="flex jCenter"
                style={{
                  fontSize: "20px",
                  textTransform: "uppercase",
                }}
              >
                <b>
                  BẢNG KÊ CHI PHÍ{" "}
                  {queries.loai == "52"
                    ? "DỊCH VỤ THU NGOÀI"
                    : [DOI_TUONG_KCB.NGOAI_TRU].includes(dataPrint.doiTuongKcb)
                    ? t("common.KHAM_BENH")
                    : [
                        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
                        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
                      ].includes(dataPrint.doiTuongKcb)
                    ? t("khamBenh.dieuTriNgoaiTru")
                    : (listDoiTuongKCB || []).find(
                        (item) => item.id == dataPrint.doiTuongKcb
                      )?.ten}{" "}
                  {!dataPrint.tenBangKe && queries.loai !== "52" && (
                    <>
                      {["10", "12"].includes(queries.loai)
                        ? "BẢO HIỂM"
                        : dataKHONG_HIEN_THI_TEXT_TONG_HOP_TREN_BANG_KE?.eval() &&
                          ["20", "30", "50", "22"].includes(queries.loai)
                        ? ""
                        : "TỔNG HỢP"}
                    </>
                  )}{" "}
                  {renderTextKiemTra(dataPrint)}
                  {isShowChuaDuyetBH && "CHƯA DUYỆT BH"}
                </b>
                <div className="box kcb">
                  {[
                    DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
                    DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
                    DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
                  ].includes(dataPrint?.doiTuongKcb)
                    ? 2
                    : dataPrint?.doiTuongKcb}
                </div>
              </div>
              {dataPrint.tenBangKe &&
                queries.loai !== "50" &&
                !isBangKeTienIchNbNgoaiTru && (
                  <div
                    className="flex jCenter"
                    style={{
                      fontSize: "20px",
                      textTransform: "uppercase",
                    }}
                  >
                    (
                    {isBangKeTienIchNbNgoaiTru
                      ? "Người bệnh tự chi trả chênh lệch"
                      : dataPrint.tenBangKe}
                    )
                  </div>
                )}
              {queries.loai === "50" && (
                <div
                  className="flex jCenter"
                  style={{
                    fontSize: "20px",
                    textTransform: "uppercase",
                  }}
                >
                  (Miễn phí)
                </div>
              )}
              {isBangKeTienIchNbNgoaiTru && (
                <div
                  className="flex jCenter"
                  style={{
                    fontSize: "20px",
                    fontStyle: "italic",
                  }}
                >
                  (Người bệnh tự chi trả)
                </div>
              )}
              {isShowMucHuongBHYT && (
                <div style={{ fontSize: 15, textAlign: "center" }}>
                  <i>
                    <b>(Mức hưởng BHYT: {dataPrint.mucHuongTheBhyt || 80}%)</b>
                  </i>
                </div>
              )}
            </div>,
            <div className="hanh-chinh">
              <div className="bold title mrb-3">I. PHẦN HÀNH CHÍNH</div>
            </div>,
            <div className="line-empty"></div>,
            <Row>
              <Col span={13}>
                {Object.values(LOAI_IN_BANG_KE_CHI_PHI).includes(+queries.loai)
                  ? "(1) "
                  : ""}
                Họ và tên: <b>{dataPrint.tenNb}</b>
              </Col>
              <Col span={3}>Giới tính: {dataPrint.gioiTinh}</Col>
              <Col span={8}>Ngày sinh: {dataPrint.ngaySinh}</Col>
            </Row>,
            <Row>
              <Col span={17} className="flex">
                <div>(2)</div>{" "}
                <div style={{ marginLeft: "5px" }}>
                  {" "}
                  Địa chỉ hiện tại: {dataPrint.diaChi}
                  {[
                    "EMR_BA134",
                    "EMR_BA251",
                    "EMR_BA380",
                    "EMR_BA381",
                  ].includes(queries.maBaoCao) &&
                    dataPrint.soHoNgheo &&
                    ` - ${dataPrint.soHoNgheo}`}
                </div>
              </Col>
              <Col span={7} className="flex jCEnd alCenter">
                (3) Mã khu vực (K1/K2/K3):{" "}
                <div
                  className="box"
                  style={{
                    padding: "5px",
                    width: "50px",
                    marginLeft: "5px",
                    alignItems: "center",
                  }}
                >
                  {dataPrint.khuVuc}
                </div>
              </Col>
            </Row>,
            <Row>
              <Col span={10} className="flex ma-the">
                <span>(4) Mã thẻ BHYT: </span>
                <InputGroup
                  component={{
                    props: {
                      width: 15,
                      height: 15,
                      disabled: true,
                      rule: "xx_x_xx_xxxxxxxxxx",
                      fieldName: "maTheBhyt",
                    },
                  }}
                  disabled={true}
                  form={dataPrint.tenBangKe ? "" : dataPrint}
                ></InputGroup>
              </Col>
              {!dataPrint.tenBangKe && (
                <Col span={12}>
                  Giá trị từ: {renderTime(dataPrint.tuNgayTheBhyt)}{" "}
                  {dataPrint.denNgayTheBhyt
                    ? `đến: ${renderTime(dataPrint.denNgayTheBhyt)}`
                    : ""}
                </Col>
              )}
            </Row>,
            <Row>
              <Col span={16}>
                (5) Nơi ĐK KCB ban đầu: <b>{dataPrint.tenNoiDangKy}</b>
              </Col>
              <Col span={8} className="flex jCEnd alCenter">
                (6) Mã:{" "}
                <div className="box mgL-10" style={{ width: "90px" }}>
                  {dataPrint.maNoiDangKy}
                </div>
              </Col>
            </Row>,
            <Row>
              <Col span={12}>
                (7) Đến khám:{" "}
                {renderTime(
                  dataPrint.thoiGianVaoVien,
                  "HH giờ mm p\\hút, \\ngà\\y DD/MM/YYYY"
                )}
              </Col>
            </Row>,
            <Row>
              <Col span={12}>
                (8) Điều trị ngoại trú/ nội trú từ:{" "}
                <b>
                  {renderTime(
                    dataPrint.thoiGianLapBenhAn,
                    "HH giờ mm p\\hút, \\ngà\\y DD/MM/YYYY"
                  )}
                </b>
              </Col>
            </Row>,
            <Row>
              <Col span={12}>
                (9) Kết thúc khám/ điều trị:{" "}
                <b>
                  {renderTime(
                    dataPrint.thoiGianRaVien,
                    "HH giờ mm p\\hút,\\ngà\\y DD/MM/YYYY"
                  )}
                </b>
              </Col>
              <Col span={6} className="flex alCenter">
                Tổng số ngày điều trị:{" "}
                <div
                  className="box mgL-10 alCenter "
                  style={{ width: "50px", fontSize: "16px" }}
                >
                  {["EMR_BA134", "EMR_BA251"].includes(queries.maBaoCao) &&
                  dataCHAN_DOAN_THEO_BANG_KE.eval()
                    ? dataPrint.soNgayDieuTri2
                    : dataPrint.soNgayDieuTri}
                </div>
              </Col>
              <Col span={6} className="flex jCEnd alCenter">
                (10) Tình trạng ra viện:{" "}
                <div className="box mgL-10 alCenter " style={{ width: "50px" }}>
                  {dataPrint.huongDieuTri}
                </div>
              </Col>
            </Row>,
            renderTuyen(dataPrint),
            <Row>
              <Col span={12}>
                Nơi chuyến đến từ: {dataPrint.tenNoiGioiThieu}
              </Col>
              <Col span={12}>Nơi chuyển đi: {dataPrint.tenVienChuyenDen}</Col>
            </Row>,
            <Row gutter={12}>
              <Col span={16} className="flex">
                <div>(15)</div>{" "}
                <div style={{ marginLeft: "5px" }}>
                  Chẩn đoán xác định: {renderChanDoanXacDinh()}
                </div>
              </Col>
              <Col span={8} className="flex jCEnd alCenter">
                (16) Mã bệnh (ICD-10):{" "}
                <div className="box mgL-10 alCenter" style={{ width: "100px" }}>
                  {dataPrint.maCdChinh}
                </div>
              </Col>
            </Row>,
            <Row>
              <Col span={24} className="flex">
                <div>(17)</div>{" "}
                <div style={{ marginLeft: "5px" }}>
                  {["EMR_BA134", "EMR_BA251"].includes(queries.maBaoCao) &&
                  dataCHAN_DOAN_THEO_BANG_KE.eval() ? (
                    <> Bệnh kèm theo: {dataPrint?.cdXacDinh2 || ""}</>
                  ) : (
                    <>
                      {" "}
                      Bệnh kèm theo:
                      {isHienThiChanDoanMoTaCungHangChanDoanChinhTrenBangKe
                        ? dataPrint.cdKemTheo || ""
                        : `${dataPrint.cdKemTheo || ""} ${
                            dataPrint.cdKemTheo && dataPrint.moTaRaVien
                              ? ";"
                              : ""
                          } ${dataPrint.moTaRaVien || ""}`}
                    </>
                  )}
                </div>
              </Col>
            </Row>,
            <Row>
              <Col span={24} className="flex alCenter">
                <div>(18) Mã bệnh kèm theo: </div>
                <div className="mgL-10 f1">
                  <span
                    className="bold"
                    style={{
                      border: "1px solid #000",
                      padding: "0 5px",
                    }}
                  >
                    {dataPrint.maCdKemTheo}
                  </span>
                </div>
              </Col>
            </Row>,
            <Row style={{ marginBottom: 5 }}>
              <Col span={12}>
                (19) Thời điểm đủ 05 năm liên tục từ ngày:{" "}
                {renderTime(dataPrint.thoiGianDu5Nam)}
              </Col>
              <Col span={12}>
                (20) Miễn cùng chi trả trong năm từ ngày:{" "}
                {renderTime(dataPrint.tuNgayMienCungChiTra)}
              </Col>
            </Row>,
            queries.loai == 50 && (
              <Row style={{ marginBottom: 5 }}>
                <Col span={12}>
                  Người duyệt miễn phí: <b>{dataPrint.tenNguoiDuyetMienGiam}</b>
                </Col>
                <Col span={12}>Ngày duyệt miễn phí: </Col>
              </Row>
            ),
            queries.loai == 50 && (
              <Row style={{ marginBottom: 5 }}>
                <Col span={12}>
                  Loại miễn phí: <b>{dataPrint.loaiMienGiam}</b>
                </Col>
              </Row>
            ),
            ["EMR_BA106", "EMR_BA134", "EMR_BA222"].includes(
              queries?.maBaoCao
            ) && (
              <>
                {renderThongTinSanPhu(dataPrint)}
                {renderThongTinCon(dataPrint)}
              </>
            ),
            <div className="title mrb-3">II.PHẦN CHI PHÍ KHÁM CHỮA BỆNH</div>,
            <>
              {(listTable?.rows || []).map((item, index) => {
                return (
                  <Fragment key={index}>
                    <div
                      className="info"
                      data-type="block"
                      data-level="3"
                      id-info={stringUtils.guid()}
                    >
                      <div
                        className="flex jSpaceBetween"
                        data-type="line"
                        data-level="4"
                      >
                        <span className="ma-the flex ml5">
                          <span>Mã thẻ BHYT:</span>
                          <div className="ml5">
                            <InputGroup
                              component={{
                                props: {
                                  width: 15,
                                  height: 15,
                                  disabled: true,
                                  rule: "xx_x_xx_xxxxxxxxxx",
                                  fieldName: "maTheBhyt",
                                },
                              }}
                              disabled={true}
                              form={listTable?.values[index]}
                            ></InputGroup>
                          </div>
                        </span>
                        <span className="ma-the">
                          Giá trị từ:{" "}
                          {listTable?.values[index]?.tuNgayTheBhyt
                            ? moment(
                                listTable?.values[index]?.tuNgayTheBhyt,
                                "YYYY-MM-DD"
                              ).format("DD/MM/YYYY")
                            : " "}{" "}
                          đến{" "}
                          {listTable?.values[index]?.denNgayTheBhyt
                            ? moment(
                                listTable?.values[index]?.denNgayTheBhyt
                              ).format("DD/MM/YYYY")
                            : ""}
                        </span>
                        <span className="muc-huong">
                          <span> Mức hưởng:</span>
                          <span className="box">
                            {listTable?.values[index]?.mucHuongBhyt}
                          </span>{" "}
                          %
                        </span>
                      </div>
                      <div data-type="line" data-level="4">
                        <span>
                          (Chi phí KBCB tính từ ngày{" "}
                          {queries?.chotDotDieuTriId
                            ? dataPrint?.tuThoiGianChotDotDieuTri
                              ? moment(
                                  dataPrint?.tuThoiGianChotDotDieuTri
                                ).format("DD/MM/YYYY")
                              : ""
                            : dataPrint?.thoiGianVaoVien
                            ? moment(dataPrint?.thoiGianVaoVien).format(
                                "DD/MM/YYYY"
                              )
                            : ""}{" "}
                          đến ngày{" "}
                          {queries?.chotDotDieuTriId
                            ? dataPrint?.denThoiGianChotDotDieuTri
                              ? moment(
                                  dataPrint?.denThoiGianChotDotDieuTri
                                ).format("DD/MM/YYYY")
                              : ""
                            : dataPrint?.thoiGianRaVien
                            ? moment(dataPrint?.thoiGianRaVien).format(
                                "DD/MM/YYYY"
                              )
                            : ""}
                          )
                        </span>
                      </div>
                    </div>
                    <table
                      key={index}
                      className="table"
                      data-type={`table`}
                      id-table={stringUtils.guid()}
                    >
                      <colgroup>{colgroup()}</colgroup>
                      <thead className="table-head">
                        <tr>
                          <td rowSpan={2} className="center td">
                            {t("editor.bangKeChiPhi.noiDung")}
                          </td>
                          {queries.loai !=
                            LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU && (
                            <>
                              <td rowSpan={2} className="center">
                                {t("editor.bangKeChiPhi.donViTinh")}
                              </td>
                              <td rowSpan={2} className="center">
                                {t("editor.bangKeChiPhi.soLuong")}
                              </td>
                              <td rowSpan={2} className="center">
                                {queries.loai == 50
                                  ? t("editor.donGiaKhongBaoHiem")
                                  : t("editor.bangKeChiPhi.donGiaBV(dong)")}
                              </td>
                              <td rowSpan={2} className="center">
                                {t("editor.bangKeChiPhi.donGiaBH(dong)")}
                              </td>
                              <td rowSpan={2} className="center">
                                {queries.loai == 50
                                  ? t("editor.giaChenh")
                                  : t(
                                      "editor.bangKeChiPhi.tiLeThanhToanTheoDichVu(%)"
                                    )}
                              </td>
                              <td rowSpan={2} className="center">
                                {queries.loai == 50
                                  ? t("editor.thanhTien(dong)")
                                  : t("editor.bangKeChiPhi.thanhTienBV(dong)")}
                              </td>
                              {queries.loai != 50 && (
                                <td rowSpan={2} className="center">
                                  {t(
                                    "editor.bangKeChiPhi.tiLeThanhToanBHYT(%)"
                                  )}
                                </td>
                              )}
                            </>
                          )}
                          {queries.loai != 50 && (
                            <td rowSpan={2} className="center">
                              {t("editor.bangKeChiPhi.thanhTienBH(dong)")}
                            </td>
                          )}
                          <td
                            colSpan={
                              queries.loai ==
                              LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU
                                ? 3
                                : queries.loai == 50
                                ? 5
                                : ["10", "12"].every((el) => el != queries.loai)
                                ? 4
                                : 3
                            }
                            className="center"
                          >
                            {t("editor.bangKeChiPhi.nguonThanhToan(dong)")}
                          </td>
                          {queries.loai !=
                            LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU &&
                            queries.loai != 50 && <td></td>}
                        </tr>
                        <tr>
                          <td className="center">
                            {t("editor.bangKeChiPhi.quyBHYT")}
                          </td>
                          <td className="center">
                            {t("editor.bangKeChiPhi.nguoiBenhCungChiTra")}
                          </td>
                          {queries.loai !=
                            LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU && (
                            <td className="center">
                              {queries.loai == 50
                                ? t("editor.bangKeChiPhi.nguoiBenhTuTra")
                                : t("editor.bangKeChiPhi.nguonKhac")}
                            </td>
                          )}
                          <td className="center">
                            {queries.loai == 50
                              ? t("editor.mienGiam")
                              : t("editor.bangKeChiPhi.nguoiBenhTuTra")}
                          </td>
                          {queries.loai == 50 && (
                            <td className="center">
                              {t("editor.nguoiBenhThanhToan")}
                            </td>
                          )}
                          {queries.loai !=
                            LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU &&
                            ["10", "12", "50"].every(
                              (el) => el != queries.loai
                            ) && (
                              <td className="center">
                                {t("editor.bangKeChiPhi.daTT")}
                              </td>
                            )}
                        </tr>

                        <tr>{renderNumber()}</tr>
                      </thead>
                      <tbody className="table-tbody">{renderBody(item)}</tbody>
                    </table>
                  </Fragment>
                );
              })}
            </>,
            <div className="bold">
              Tổng chi phí lần khám bệnh/cả đợt điều trị (làm tròn đến đơn vị
              đồng):{" "}
              {isBangKeTienIchNbNgoaiTru
                ? renderPrice(
                    dataPrint.loai === 62
                      ? dataPrint.tienNbTuTra2
                      : dataPrint.tienNbTuTra
                  )
                : renderPrice(dataPrint.tongTien)}{" "}
              đồng
            </div>,
            <div>
              (
              <i>
                Viết bằng chữ:{" "}
                {isBangKeTienIchNbNgoaiTru
                  ? docSoViet(
                      Math.trunc(
                        dataPrint.loai === 62
                          ? dataPrint.tienNbTuTra2
                          : dataPrint.tienNbTuTra
                      )
                    )
                  : dataPrint.tongTienBangChu}{" "}
                đồng
              </i>
              )
            </div>,

            <div>
              {queries.loai == 40 ? (
                <div>
                  {dataPrint.tenBangKe?.toLowerCase() === "tiện ích" && (
                    <div>
                      <div className="bold">Trong đó, số tiền do:</div>
                      <div>
                        2
                        <Row gutter={12}>
                          <Col span={16}>
                            <Row gutter={12}>
                              <Col span={12} className="flex jSb">
                                <span className="ml-7">
                                  - Quỹ BHYT thanh toán:{" "}
                                </span>
                                <span className="bold">
                                  {renderPrice(dataPrint.tienBhThanhToan)}
                                </span>
                              </Col>
                              <Col span={4}>đồng</Col>
                            </Row>
                            <Row gutter={12}>
                              <Col span={12} className="flex jSb">
                                <span className="ml-7">
                                  - Người bệnh trả, trong đó:{" "}
                                </span>
                                <span className="bold">
                                  {renderPrice(dataPrint.tienNb)}
                                </span>
                              </Col>
                              <Col span={4}>đồng</Col>
                            </Row>
                            <Row gutter={12}>
                              <Col span={12} className="flex jSb">
                                <span style={{ marginLeft: "25px" }}>
                                  {" "}
                                  + Cùng trả trong phạm vi BHYT:{" "}
                                </span>
                                <span className="bold">
                                  {renderPrice(dataPrint.tienNbCungChiTra)}
                                </span>
                              </Col>
                              <Col span={4}>đồng</Col>
                            </Row>
                            <Row gutter={12}>
                              <Col span={12} className="flex jSb">
                                <span style={{ marginLeft: "25px" }}>
                                  {" "}
                                  + Các khoản phải trả khác:{" "}
                                </span>
                                <span className="bold">
                                  {renderPrice(dataPrint.tienNbTuTra)}
                                </span>
                              </Col>
                              <Col span={4}>đồng</Col>
                            </Row>

                            <Row gutter={12}>
                              <Col span={12} className="flex jSb">
                                <span className="ml-7">- Nguồn khác: ​ </span>
                                <span className="bold">
                                  {renderPrice(dataPrint.tienNguonKhac)}
                                </span>
                              </Col>
                              <Col span={4}>đồng</Col>
                            </Row>
                          </Col>
                          <Col span={8}>
                            <Row gutter={12}>
                              <Col span={20} className="flex jSb">
                                <span>Số tiền miễn giảm: </span>
                                <span className="bold">
                                  {renderPrice(dataPrint.tienMienGiam)}
                                </span>
                              </Col>
                              <Col span={4}>đồng</Col>
                            </Row>
                            {showSoTienTamUng ? (
                              <Row gutter={12}>{renderSoTienThuThem()}</Row>
                            ) : null}
                            {showSoTienTamUng && dataPrint.tienTraLai > 0 ? (
                              <Row gutter={12}>{renderSoTienCanTraLai()}</Row>
                            ) : (
                              ""
                            )}
                          </Col>
                        </Row>
                      </div>
                    </div>
                  )}
                  <div className="bold">
                    Tổng tiền ký quỹ: {renderPrice(dataPrint.tienTamUngConLai)}{" "}
                    đồng
                  </div>
                  {gopChanTongKhiQuaTrang ? renderTongTienVaChanKy() : null}
                </div>
              ) : null}
            </div>,

            !dataPrint.tenBangKe ||
            isBangKeTienIchNbNgoaiTru ||
            dataPrint.loai == LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_BHYT ? (
              <div>
                {isBangKeTienIchNbNgoaiTru ? (
                  <Row gutter={12}>
                    <Col span={12}>
                      {showSoTienTamUng && (
                        <>
                          <Row gutter={12}>
                            <Col span={24} className="flex gap-8">
                              <div>
                                <span>Tạm ứng: </span>
                                <span className="bold">
                                  {renderPrice(dataPrint.tienTamUngConLai)}
                                </span>
                              </div>
                              <span>đồng</span>
                            </Col>
                          </Row>
                        </>
                      )}
                    </Col>
                    <Col span={12}>
                      {showSoTienTamUng && (
                        <Row gutter={12}>
                          <Col span={24} className="flex gap-8">
                            <div
                              style={
                                isTangSizeTienThuThem
                                  ? {
                                      fontSize: ["P178", "P032"].includes(
                                        queries.ma
                                      )
                                        ? 20
                                        : 22,
                                    }
                                  : {}
                              }
                            >
                              <span>Số tiền cần thu thêm: </span>
                              <span className="bold">
                                {renderPrice(dataPrint.tienThuThem)}
                              </span>
                            </div>
                            <span
                              style={
                                isTangSizeTienThuThem
                                  ? {
                                      fontSize: ["P178", "P032"].includes(
                                        queries.ma
                                      )
                                        ? 20
                                        : 22,
                                    }
                                  : {}
                              }
                            >
                              đồng
                            </span>
                          </Col>
                        </Row>
                      )}
                      {showSoTienTamUng && dataPrint.tienTraLai > 0 ? (
                        <Row gutter={12}>
                          <Col span={24} className="flex gap-8">
                            <div
                              style={
                                isTangSizeTienTraLai
                                  ? {
                                      fontSize: ["P178", "P032"].includes(
                                        queries.ma
                                      )
                                        ? 20
                                        : 22,
                                    }
                                  : {}
                              }
                            >
                              <span>Số tiền cần trả lại: </span>
                              <span className="bold">
                                {renderPrice(dataPrint.tienTraLai)}
                              </span>
                            </div>
                            <span
                              style={
                                isTangSizeTienTraLai
                                  ? {
                                      fontSize: ["P178", "P032"].includes(
                                        queries.ma
                                      )
                                        ? 20
                                        : 22,
                                    }
                                  : {}
                              }
                            >
                              đồng
                            </span>
                          </Col>
                        </Row>
                      ) : (
                        ""
                      )}
                    </Col>
                  </Row>
                ) : (
                  <>
                    <div className="bold">Trong đó, số tiền do:</div>
                    <div>
                      <Row gutter={12}>
                        <Col span={12}>
                          <Row gutter={12}>
                            <Col span={20} className="flex jSb">
                              <span className="ml-7">
                                - Quỹ BHYT thanh toán:{" "}
                              </span>
                              <span className="bold">
                                {renderPrice(dataPrint.tienBhThanhToan)}
                              </span>
                            </Col>
                            <Col span={4}>đồng</Col>
                          </Row>
                          <Row gutter={12}>
                            <Col span={20} className="flex jSb">
                              <span className="ml-7">
                                - Người bệnh trả, trong đó:{" "}
                              </span>
                              <span className="bold">
                                {renderPrice(dataPrint.tienNb)}
                              </span>
                            </Col>
                            <Col span={4}>đồng</Col>
                          </Row>
                          <Row gutter={12}>
                            <Col span={20} className="flex jSb">
                              <span style={{ marginLeft: "25px" }}>
                                {" "}
                                + Cùng trả trong phạm vi BHYT:{" "}
                              </span>
                              <span className="bold">
                                {renderPrice(dataPrint.tienNbCungChiTra)}
                              </span>
                            </Col>
                            <Col span={4}>đồng</Col>
                          </Row>
                          <Row gutter={12}>
                            <Col span={20} className="flex jSb">
                              <span style={{ marginLeft: "25px" }}>
                                {" "}
                                + Các khoản phải trả khác:{" "}
                              </span>
                              <span className="bold">
                                {renderPrice(dataPrint.tienNbTuTra)}
                              </span>
                            </Col>
                            <Col span={4}>đồng</Col>
                          </Row>
                          {![
                            LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_BHYT,
                            LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU,
                          ].includes(dataPrint.loai) &&
                            ["10", "12"].every((el) => el != queries.loai) &&
                            [2, 3, 4].some(
                              (item) => item === dataPrint.doiTuongKcb
                            ) &&
                            hienThiTienDvPhaiTraKhac && (
                              <>
                                <Row gutter={12}>
                                  <Col span={20} className="flex jSb">
                                    <span style={{ marginLeft: "35px" }}>
                                      {" "}
                                      - Các khoản phải trả khác bảo hiểm:{" "}
                                    </span>
                                    <span className="bold">
                                      {renderPrice(dataPrint.tienNbTuTra1)}
                                    </span>
                                  </Col>
                                  <Col span={4}>đồng</Col>
                                </Row>
                                <Row gutter={12}>
                                  <Col span={20} className="flex jSb">
                                    <span style={{ marginLeft: "35px" }}>
                                      {" "}
                                      - Các khoản phải trả khác không bảo hiểm:{" "}
                                    </span>
                                    <span className="bold">
                                      {renderPrice(dataPrint.tienNbTuTra2)}
                                    </span>
                                  </Col>
                                  <Col span={4}>đồng</Col>
                                </Row>
                              </>
                            )}

                          {dataPrint.loai !=
                            LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU && (
                            <Row gutter={12}>
                              <Col span={20} className="flex jSb">
                                <span className="ml-7">- Nguồn khác: ​ </span>
                                <span className="bold">
                                  {renderPrice(dataPrint.tienNguonKhac)}
                                </span>
                              </Col>
                              <Col span={4}>đồng</Col>
                            </Row>
                          )}
                          {dataPrint.loai !=
                            LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_BHYT &&
                            dataPHIEU_THU_BIEN_LAI_VIEN_PHI?.eval() &&
                            (queries.loai == 20 ||
                              queries?.maBaoCao == "EMR_BA381") && (
                              <Row gutter={12}>
                                <Col span={20} className="flex jSb">
                                  <span className="ml-7">
                                    - Số tiền biên lai: ​{" "}
                                  </span>
                                  <span className="bold">
                                    {renderPrice(dataPrint.tienBienLai)}
                                  </span>
                                </Col>
                                <Col span={4}>đồng</Col>
                              </Row>
                            )}

                          {dataPrint.loai ==
                            LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU && (
                            <>
                              <Row gutter={12}>
                                <Col span={20} className="flex jSb">
                                  <span className="ml-7">
                                    - Đã đóng tạm: ​{" "}
                                  </span>
                                  <span className="bold">
                                    {renderPrice(dataPrint.tienTamUngConLai)}
                                  </span>
                                </Col>
                                <Col span={4}>đồng</Col>
                              </Row>
                              {dataPrint.tienTraLai > 0 && (
                                <Row gutter={12}>
                                  <Col span={20} className="flex jSb">
                                    <span className="ml-7">
                                      - Hoàn lại cho người bệnh: ​{" "}
                                    </span>
                                    <span className="bold">
                                      {renderPrice(dataPrint.tienTraLai)}
                                    </span>
                                  </Col>
                                  <Col span={4}>đồng</Col>
                                </Row>
                              )}
                              {dataPrint.tienThuThem > 0 && (
                                <Row gutter={12}>
                                  <Col span={20} className="flex jSb">
                                    <span className="ml-7">
                                      - Tiền cần thu thêm: ​{" "}
                                    </span>
                                    <span className="bold">
                                      {renderPrice(dataPrint.tienThuThem)}
                                    </span>
                                  </Col>
                                  <Col span={4}>đồng</Col>
                                </Row>
                              )}
                            </>
                          )}
                        </Col>
                        {dataPrint.loai !=
                          LOAI_IN_BANG_KE_CHI_PHI.KCB_NOI_TRU &&
                          (dataPrint.loai !=
                            LOAI_IN_BANG_KE_CHI_PHI.BANG_KE_BHYT ||
                            showSoTienTamUng) && (
                            <Col span={12}>
                              {!queries.chotDotDieuTriId && (
                                <Row gutter={12}>
                                  {(["10", "12"].every(
                                    (el) => el != queries.loai
                                  ) ||
                                    showSoTienTamUng) && (
                                    <>
                                      <Col
                                        span={spanTienTamUng}
                                        className="flex jSb"
                                      >
                                        <span>Tạm ứng: </span>
                                        <span className="bold">
                                          {renderPrice(
                                            dataPrint.tienTamUngConLai
                                          )}
                                        </span>
                                      </Col>
                                      <Col span={4}>đồng</Col>
                                    </>
                                  )}
                                </Row>
                              )}
                              {!queries.chotDotDieuTriId && (
                                <Row gutter={12}>
                                  {(["20", "30", "50", "22"].includes(
                                    queries.loai
                                  )
                                    ? dataBANG_KE_CHI_PHI_AN_HOAN_TAM_UNG?.eval()
                                    : ["10", "12"].every(
                                        (el) => el != queries.loai
                                      )) && (
                                    <>
                                      <Col
                                        span={spanTienTamUng}
                                        className="flex jSb"
                                      >
                                        <span>Hoàn tạm ứng: </span>
                                        <span className="bold">
                                          {renderPrice(dataPrint.tienHoanUng)}
                                        </span>
                                      </Col>
                                      <Col span={4}>đồng</Col>
                                    </>
                                  )}
                                </Row>
                              )}
                              {!queries.chotDotDieuTriId && (
                                <Row gutter={12}>
                                  {["10", "12"].every(
                                    (el) => el != queries.loai
                                  ) && (
                                    <>
                                      <Col
                                        span={spanTienTamUng}
                                        className="flex jSb"
                                      >
                                        <span>Số tiền miễn giảm: </span>
                                        <span className="bold">
                                          {renderPrice(dataPrint.tienMienGiam)}
                                        </span>
                                      </Col>
                                      <Col span={4}>đồng</Col>
                                    </>
                                  )}
                                </Row>
                              )}
                              {["EMR_BA106", "EMR_BA222"].includes(
                                queries?.maBaoCao
                              ) &&
                                dataHIEN_THU_TIEN_NB_PHAI_TRA_TREN_BANG_KE?.eval() && (
                                  <Row gutter={12}>
                                    {["10", "12"].every(
                                      (el) => el != queries.loai
                                    ) && (
                                      <>
                                        <Col
                                          span={spanTienTamUng}
                                          className="flex jSb"
                                        >
                                          <span>Số tiền NB phải trả: </span>
                                          <span className="bold">
                                            {renderPrice(
                                              dataPrint.tienChuaThanhToan
                                            )}
                                          </span>
                                        </Col>
                                        <Col span={4}>đồng</Col>
                                      </>
                                    )}
                                  </Row>
                                )}
                              {!queries.chotDotDieuTriId &&
                                dataBANG_KE_TONG_HOP_SUA_DOI?.eval() && (
                                  <Row gutter={12}>
                                    {["10", "12"].every(
                                      (el) => el != queries.loai
                                    ) || showSoTienTamUng
                                      ? renderSoTienThuThem()
                                      : null}
                                  </Row>
                                )}
                              <Row gutter={12}>
                                {["10", "12"].every(
                                  (el) => el != queries.loai
                                ) && (
                                  <>
                                    <Col
                                      span={spanTienTamUng}
                                      className="flex jSb"
                                    >
                                      <span>
                                        {dataBANG_KE_TONG_HOP_SUA_DOI?.eval()
                                          ? "Số tiền thanh toán"
                                          : "Số tiền đã thanh toán"}
                                        :{" "}
                                      </span>
                                      <span className="bold">
                                        {renderPrice(dataPrint.tienDaThanhToan)}
                                      </span>
                                    </Col>
                                    <Col span={4}>đồng</Col>
                                  </>
                                )}
                              </Row>
                              {!queries.chotDotDieuTriId &&
                                !dataBANG_KE_TONG_HOP_SUA_DOI?.eval() && (
                                  <Row gutter={12}>
                                    {["10", "12"].every(
                                      (el) => el != queries.loai
                                    ) || showSoTienTamUng
                                      ? renderSoTienThuThem()
                                      : null}
                                  </Row>
                                )}

                              {!queries.chotDotDieuTriId && (
                                <Row gutter={12}>
                                  {(["10", "12"].every(
                                    (el) => el != queries.loai
                                  ) ||
                                    (showSoTienTamUng &&
                                      dataPrint.tienTraLai > 0)) && (
                                    <>{renderSoTienCanTraLai()}</>
                                  )}
                                </Row>
                              )}
                              {dataPHIEU_THU_BIEN_LAI_VIEN_PHI?.eval() &&
                                (queries.loai == 20 ||
                                  queries?.maBaoCao == "EMR_BA381") && (
                                  <Row gutter={12}>
                                    <Col
                                      span={spanTienTamUng}
                                      className="flex jSb"
                                    >
                                      <span>Số tiền hoá đơn: ​ </span>
                                      <span className="bold">
                                        {renderPrice(dataPrint.tienHoaDon)}
                                      </span>
                                    </Col>
                                    <Col span={4}>đồng</Col>
                                  </Row>
                                )}
                            </Col>
                          )}
                      </Row>
                    </div>
                  </>
                )}

                {gopChanTongKhiQuaTrang && renderTongTienVaChanKy()}
              </div>
            ) : (
              <></>
            ),
            gopChanTongKhiQuaTrang ? <></> : renderTongTienVaChanKy(),
          ].map((line, index) => {
            return <Fragment key={index}>{line}</Fragment>;
          })}
          {isShowTongKetChiPhi ? (
            <div
              className="tong-ket-chi-phi"
              style={{
                pageBreakBefore: "always",
                pageBreakInside: "avoid",
                position: "relative",
                minHeight: 1150,
              }}
            >
              <div className="form-header flex" key={index}>
                <div className="header-left ">
                  <div>{dataPrint.tieuDeTrai1}</div>
                  <div>
                    <b>{dataPrint.tieuDeTrai2}</b>
                  </div>

                  <div className="header-level-1">
                    <b>Khoa: {dataPrint?.tenKhoaNb}</b>
                  </div>
                  <div className="header-level-2">
                    <b>Mã khoa: </b> {dataPrint?.maBhyt}
                  </div>
                </div>
                <div>
                  <Barcode
                    component={{
                      props: {
                        fieldName: "maHoSo",
                        width: 170,
                        height: 40,
                      },
                    }}
                    form={{
                      maHoSo: dataPrint?.maHoSo,
                    }}
                  ></Barcode>
                </div>
                <div className="header-right">
                  <div>
                    <b>Mẫu số: 01/KBCB</b>
                  </div>
                  <div>
                    <b>{t("common.maNguoiBenh")}:</b> {dataPrint?.maNb}
                  </div>
                  <div>
                    <b>Mã sổ khám bệnh:</b> {dataPrint?.maHoSo}
                  </div>

                  <div>
                    <b>Mã bệnh án:</b> {dataPrint?.maBenhAn}
                  </div>
                  {(queries.loai == 10 ||
                    [
                      "EMR_BA106",
                      "EMR_BA134",
                      "EMR_BA222",
                      "EMR_BA323",
                    ].includes(queries?.maBaoCao)) &&
                    (dataCHI_HIEN_THI_SO_PHIEU_TRONG_SO_PHOI?.eval() ? (
                      <div>
                        <b>Số phơi:</b>{" "}
                        <span style={{ fontSize: "24px", fontWeight: "bold" }}>
                          {dataPrint?.soPhoi?.substr(
                            dataPrint?.soPhoi?.length - soPhieu
                          )}
                        </span>
                      </div>
                    ) : (
                      <div>
                        <b>Số phơi:</b> {dataPrint?.soPhoi}
                      </div>
                    ))}
                </div>
              </div>
              <div className="title relative">
                <div
                  className="flex jCenter"
                  style={{
                    fontSize: "20px",
                    textTransform: "uppercase",
                  }}
                >
                  <b>TỔNG KẾT CHI PHÍ KHÁM CHỮA BỆNH</b>
                  <br></br>
                </div>
                {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI && (
                  <div
                    className="flex jCenter"
                    style={{
                      fontSize: "20px",
                      textTransform: "uppercase",
                    }}
                  >
                    <b>(Miễn phí giám đốc duyệt)</b>
                  </div>
                )}
              </div>
              <Row>
                <Col span={13}>
                  (1) Họ tên người bệnh: <b>{dataPrint.tenNb}</b>
                </Col>
                <Col span={8}>Ngày, tháng, năm sinh: {dataPrint.ngaySinh}</Col>
                <Col span={3}>Giới tính: {dataPrint.gioiTinh}</Col>
              </Row>
              <Row>
                <Col span={24}>Địa chỉ: {dataPrint.diaChi}</Col>
              </Row>
              <Row>
                <Col span={12}>
                  Ngày vào viện:{" "}
                  {dataPrint.thoiGianVaoVien
                    ? moment(dataPrint.thoiGianVaoVien).format(
                        "HH [giờ] mm [phút], [ngày] DD/MM/YYYY"
                      )
                    : ""}
                </Col>
                <Col span={12}>
                  Ngày ra viện:{" "}
                  {dataPrint.thoiGianRaVien
                    ? moment(dataPrint.thoiGianRaVien).format(
                        "HH [giờ] mm [phút], [ngày] DD/MM/YYYY"
                      )
                    : ""}
                </Col>
              </Row>
              {(dataPrint?.tongKet?.dsThe || []).map((item, index) => (
                <div>
                  <Row key={index}>
                    <Col span={12}>
                      {index + 1}. Tổng chi phí khám chữa bệnh thẻ{" "}
                      {item.maTheBhyt || ""}
                    </Col>
                    <Col span={12} className="flex jSpaceBetween">
                      <div>(1) = (2) + (3):</div>
                      <div className="bold">{renderPrice(item.tongTienBh)}</div>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={12}>
                      + Quỹ BHYT thanh toán ({item.mucHuongBhyt || 0}%)
                    </Col>
                    <Col span={12} className="flex jSpaceBetween">
                      <div>(2):</div>
                      <div className="bold">
                        {renderPrice(item.tienBhThanhToan)}
                      </div>
                    </Col>
                  </Row>
                  <Row span={24}>
                    <Col span={12}>
                      + Số tiền người bệnh đồng chi trả (
                      {100 - (item.mucHuongBhyt || 0)}%)
                    </Col>
                    <Col span={12} className="flex jSpaceBetween">
                      <div>(3):</div>
                      <div className="bold">
                        {renderPrice(item.tienNbCungChiTra)}
                      </div>
                    </Col>
                  </Row>
                </div>
              ))}
              <div>
                <Row>
                  <Col span={12}>Số tiền người bệnh trả DV TYC</Col>
                  <Col span={12} className="flex jSpaceBetween">
                    <div>(4):</div>
                    <div className="bold">
                      {renderPrice(dataPrint?.tongKet?.tienNbTuTra)}
                    </div>
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>Số tiền phẫu thuật phải thu thêm</Col>
                  <Col span={12} className="flex jSpaceBetween">
                    <div>(5):</div>
                    <div className="bold">
                      {renderPrice(dataPrint?.tongKet?.tienNbTuTraPtTt)}
                    </div>
                  </Col>
                </Row>
                {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI && (
                  <Row>
                    <Col span={12}>Số tiền Miễn phí giám đốc duyệt</Col>
                    <Col span={12} className="flex jSpaceBetween">
                      <div>(6):</div>
                      <div className="bold">
                        {renderPrice(dataPrint?.tongKet?.tienMienGiam)}
                      </div>
                    </Col>
                  </Row>
                )}

                <Row span={24}>
                  <Col span={12}>Số tiền người bệnh phải trả</Col>
                  <Col span={12} className="flex jSpaceBetween">
                    {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI ? (
                      <div>(7) = (3) + (4) + (5) - (6):</div>
                    ) : (
                      <div>(6) = (3) + (4) + (5):</div>
                    )}

                    <div className="bold">
                      {renderPrice(
                        queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI
                          ? dataPrint?.tongKet?.tienNbSauGiam
                          : dataPrint?.tongKet?.tienNb
                      )}
                    </div>
                  </Col>
                </Row>
                <Row span={24}>
                  <div>
                    <b>
                      <i>Số tiền ghi bằng chữ:</i>
                    </b>
                  </div>
                </Row>
              </div>
              <Row>
                <Col span={6}>
                  <div>Tổng chi phí cả đợt điều trị:</div>
                </Col>
                <Col span={3}>
                  {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI
                    ? "(1) + (4) "
                    : "(1) + (4)  + (5)"}
                  :
                </Col>
                <Col span={13}>
                  <div className="bold">
                    {dataPrint?.tongKet?.tongTienBangChu}
                  </div>
                </Col>
              </Row>
              <Row>
                <Col span={6}>
                  <div>Quỹ bảo hiểm thanh toán:</div>
                </Col>
                <Col span={3}>(2):</Col>
                <Col span={13}>
                  <div className="bold">
                    {dataPrint?.tongKet?.tienBhThanhToanBangChu}
                  </div>
                </Col>
              </Row>
              {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI && (
                <Row>
                  <Col span={6}>
                    <div>Số tiền Miễn phí giám đốc duyệt:</div>
                  </Col>
                  <Col span={3}>(6) :</Col>
                  <Col span={13}>
                    <div className="bold">
                      {dataPrint?.tongKet?.tienMienGiamBangChu}
                    </div>
                  </Col>
                </Row>
              )}
              <Row>
                <Col span={6}>
                  <div>Số tiền người bệnh phải trả:</div>
                </Col>
                <Col span={3}>
                  {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI
                    ? "(7)"
                    : "(6)"}
                  :
                </Col>
                <Col span={13}>
                  <div className="bold">
                    {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI
                      ? dataPrint?.tongKet?.tienNbSauGiamBangChu
                      : dataPrint?.tongKet?.tienNbBangChu}
                  </div>
                </Col>
              </Row>
              <Row gutter={[0, 12]}>
                <Col span={6}>
                  <div>
                    <b>Tổng tiền tạm ứng:</b>
                  </div>
                </Col>
                <Col span={3}>
                  {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI
                    ? "(8)"
                    : "(7)"}
                  :
                </Col>
                <Col span={6}>
                  <div className="bold flex jSb">
                    <span>{renderPrice(dataPrint?.tongKet?.tienTamUng)}</span>
                    <span>đồng</span>
                  </div>
                </Col>
                <Col span={9}>
                  <div className="bold" style={{ marginLeft: 10 }}>
                    <span>
                      {" "}
                      Số lần tạm ứng: {dataPrint?.tongKet?.soLanTamUng || ""}
                    </span>
                  </div>
                </Col>
              </Row>
              <Row>
                <Col span={6}>
                  <div>
                    <b>Số tiền đã trả lại người bệnh:</b>
                  </div>
                </Col>
                <Col span={3}>
                  {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI
                    ? "(9)"
                    : "(8)"}
                  :
                </Col>
                <Col span={6}>
                  <div className="bold flex jSb">
                    <span>
                      {renderPrice(dataPrint?.tongKet?.tienConLaiHoanUng)}
                    </span>{" "}
                    <span>đồng</span>
                  </div>
                </Col>
                <Col span={9}></Col>
              </Row>
              <Row>
                <Col span={6}>
                  <div>
                    <b>Số tiền bệnh nhân nhận lại:</b>
                  </div>
                </Col>
                <Col span={3}>
                  {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI
                    ? "(8) - (7) - (9)"
                    : " (7) - (6) - (8)"}
                  :
                </Col>
                <Col span={6}>
                  <div className="bold flex jSb">
                    <span>
                      {renderPrice(
                        queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI
                          ? dataPrint?.tongKet?.tienTraLaiSauGiam
                          : dataPrint?.tongKet?.tienTraLai
                      )}
                    </span>
                    <span>đồng</span>{" "}
                  </div>
                </Col>
                <Col span={9}></Col>
              </Row>
              <Row>
                <Col span={6}>
                  <div>
                    <b>Số tiền viện phải thu:</b>
                  </div>
                </Col>
                <Col span={3}>
                  {queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI
                    ? "(7) - (8) + (9)"
                    : "(6) - (7) + (8)"}{" "}
                  :
                </Col>
                <Col span={6}>
                  <div className="bold flex jSb">
                    <span>
                      {renderPrice(
                        queries.loai == LOAI_IN_BANG_KE_CHI_PHI.MIEN_PHI
                          ? dataPrint?.tongKet?.tienThuThemSauGiam
                          : dataPrint?.tongKet?.tienThuThem
                      )}
                    </span>
                    <span>đồng</span>
                  </div>
                </Col>
                <Col span={9}></Col>
              </Row>
              <Row>
                <Col
                  span={24}
                  style={{
                    textAlign: "right",
                    marginRight: 50,
                  }}
                >
                  <i>
                    {dataPrint.thoiGianIn
                      ? moment(dataPrint.thoiGianIn).format(
                          "HH:mm [ngày] DD [tháng] MM [năm] YYYY"
                        )
                      : ""}
                  </i>
                </Col>
              </Row>
              <Row>
                <Col span={8} className="" offset={16}>
                  <div className="bold center">KẾ TOÁN VIỆN PHÍ</div>
                  <div className="center">
                    <i className="center">(Ký, ghi rõ họ tên)</i>
                  </div>
                </Col>
              </Row>
              <div
                className="bold note"
                style={{
                  position: "absolute",
                  bottom: "20px",
                  textDecoration: "underline",
                  textAlign: "center",
                  width: "100%",
                }}
              >
                Lưu ý: Người bệnh kiểm tra lại phiếu thanh toán ra viện, nếu
                thấy không hợp lý liên hệ với khoa điều trị để giải quyết
              </div>
            </div>
          ) : null}
          {[
            "EMR_BA134",
            "EMR_BA222",
            "EMR_BA251",
            "EMR_BA106",
            "EMR_BA381",
            "EMR_BA380",
          ].includes(queries?.maBaoCao) && dataPrint.qrTamUngTt?.length ? (
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 10,
              }}
            >
              {dataPrint.qrTamUngTt.map((item) => {
                return (
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                    }}
                  >
                    {!!item.qr && (
                      <ReactQrCode
                        key={item.qr}
                        id={"qrcode"}
                        value={item.qr}
                        margin={0}
                        size={128}
                      />
                    )}
                    {`${item?.thanhTien?.formatPrice()} đồng - ${dataPrint?.tenNb?.toUpperCase()} - ${
                      dataPrint.maHoSo
                    }`}
                  </div>
                );
              })}
            </div>
          ) : null}
        </div>
      );
    });
  }, [
    state.listPhieu,
    listDoiTuongKCB,
    dataTHIET_LAP_CHAN_KY_BANG_KE,
    dataTHIET_LAP_CHAN_KY_NGANG_BANG_KE,
    dataTHEM_TEXT_BANG_KE_CHUA_DUYET_BH,
    dataCHI_HIEN_THI_SO_PHIEU_TRONG_SO_PHOI,
    dataSO_PHOI_THANH_TOAN,
    dataPHIEU_THU_BIEN_LAI_VIEN_PHI,
    isBangKeTienIchNbNgoaiTru,
    hideTenNguoiInBangKe,
    listViTriChanKyBangKe,
    showSoTienTamUng,
    showTextQuanHeVoiNguoiBenh,
    gopChanTongKhiQuaTrang,
    showTextNguoiBaoLanh,
    hideTextToiDaNhanPhim,
    hideTenNguoiLapBangKe,
    isHienThiChanDoanMoTaCungHangChanDoanChinhTrenBangKe,
    dataCHO_IN_BANG_KE_TIEN_ICH_NGUOI_BENH_NGOAI_TRU,
    showTextNguoiBenhKyTen,
    hienThiTienDvPhaiTraKhac,
    dataBANG_KE_CHI_PHI_KHUNG_DICH_VU,
  ]);

  const callback = debounce((observer) => {
    clearTimeout(refTimeoutPrint.current);
    const print = () => {
      hideLoading();
      refMain.current.classList.add("editor-loaded-data");
      var iframe = window.frameElement;
      if (iframe) {
        const iframeId = iframe.getAttribute("data-id");
        const height = iframe.contentDocument.body.scrollHeight;
        window.parent?.postMessage(
          {
            TYPE: "IFRAME-EDITOR-HEIGHT",
            DATA: { iframeId, height: height, width: A4.width + 5 },
          },
          window.location.origin
        );
      }
      setState({ isLoadedPhieu: true });
      observer.disconnect();
      refIsObserving.current = false;
    };
    if (!document.getElementById("barcode"))
      // nếu barcode vẫn chưa load xong thì chờ thêm 1s
      refTimeoutPrint.current = setTimeout(() => {
        print();
      }, 1000);
    else {
      print(); // ngược lại thì print luôn
    }
  }, 1500);

  useLayoutEffect(() => {
    const targetNode = document.getElementById("root");
    if (!targetNode || refIsObserving.current) return;

    refIsObserving.current = true;
    let observer = new MutationObserver((mutationList, observer) => {
      callback(observer);
    });
    const config = { attributes: true, childList: true, subtree: true };
    observer.observe(targetNode, config);
  }, [state.listPhieu]);

  useEffect(() => {
    if (state.listPhieu && state.isLoadedPhieu) {
      if (queries.notPrint === "false" || !queries.notPrint) {
        try {
          clearTimeout(refTimeout.current);
          if (state.notFoundData) {
            if (queries.fromIframe) {
              window.parent.postMessage({
                type: "PDF-FILE",
                value: null,
              });
            }
          } else {
            refTimeout.current = setTimeout(async () => {
              const { pdfUrls } = await pdfGenerator({
                layout: pageType.A4.v,
                resultHml: false,
                htmlNotEdit: true,
                pageType: "A4",
              });
              const filePdf = await addFooterPage({
                dataPrint: state.listPhieu[0]?.dataPrint || {},
                urls: pdfUrls,
                showSdt: hideTenNguoiInBangKe,
              });
              const dataPrint = {
                loaiIn: currentBaoCao?.loaiIn,
                huongGiay: currentBaoCao?.huongGiay,
                khoGiay: currentBaoCao?.khoGiay,
                hinhThucIn: currentBaoCao?.hinhThucIn,
                file: { pdf: filePdf },
              };

              let dsFilePdf = [filePdf];

              if (dsPhieuInKem.length > 0) {
                const { nbDotDieuTriId, chiDinhTuLoaiDichVu } = queries || {};

                const { finalFile } = await getFilePhieuIn({
                  listPhieus: dsPhieuInKem,
                  showError: true,
                  nbDotDieuTriId,
                  chiDinhTuLoaiDichVu,
                });

                dsFilePdf.push(finalFile);
              }

              const s = await centralizedErrorHandling(
                printProvider.getMergePdf(dsFilePdf)
              );

              if (queries.fromIframe) {
                window.parent.postMessage({
                  type: "PDF-FILE",
                  value: dataPrint,
                });
              } else {
                if (queries.openNewTab) {
                  openInNewTab(filePdf);
                } else {
                  try {
                    await printProvider.printPdf([dataPrint], {
                      isEditor: true,
                      mergePdfFile: s,
                      timeout: 2000,
                      onFinish: () => {
                        if (!(queries.khongDongTab == "true")) {
                          window.close();
                        }
                      },
                      onError: () => {
                        if (!(queries.khongDongTab == "true")) {
                          window.close();
                        }
                      },
                    });
                  } catch (error) {}
                }
              }
              window.parent.postMessage({
                type: "FINISH",
                value: "print-done",
              });
            }, 50);
          }
        } catch (error) {
          console.log(error);
        } finally {
          hideLoading();
        }
      } else {
        hideLoading();
      }
    }
  }, [
    state.listPhieu,
    state.isLoadedPhieu,
    currentBaoCao,
    state.notFoundData,
    dsPhieuInKem,
  ]);

  useEffect(async () => {
    if (dsPhieuInKem.length > 0) {
      const { nbDotDieuTriId, chiDinhTuLoaiDichVu } = queries || {};

      const { finalFile } = await getFilePhieuIn({
        listPhieus: dsPhieuInKem,
        showError: true,
        nbDotDieuTriId,
        chiDinhTuLoaiDichVu,
      });

      setState({ urlFileLocal: finalFile });
    }
  }, [dsPhieuInKem]);

  const showPrintBtn = () => {
    if (
      [
        "P032",
        "P107",
        "P062",
        "P178",
        "P541",
        "P678",
        "P677",
        "P727",
        "P746",
        "P747",
      ].includes(queries.ma)
    ) {
      // Bỏ qua việc check kySo: true từ màn thu ngân
      // các queries gồm notPrint, kySo, maViTri phải có kySo: true khi in bên thu ngân mới truyền đủ
      return (
        (!headless && queries.khongDongTab) ||
        (queries.notPrint && queries.maViTri === "02101")
      );
    } else {
      return (
        !headless &&
        queries.khongDongTab &&
        queries.kySo &&
        ["00401", "00101"].includes(queries.maViTri)
      ); //màn thu ngân và HSBA
    }
    // showButtonPrint để render Button
    // showPrintBtn() để style display: flex/none
  };

  const onDocumentLoadSuccess = ({ numPages }) => {
    setState({
      numPages: numPages,
    });
  };
  const onDocumentComplete = (pages) => {
    setState({
      pageNumber: 1,
      numPages: pages,
    });
  };
  const onPageComplete = (page) => {
    setState({
      pageNumber: page,
    });
  };

  return (
    <>
      <GlobalStyle
        dataBANG_KE_CHI_PHI_KHUNG_DICH_VU={dataBANG_KE_CHI_PHI_KHUNG_DICH_VU}
      />
      <Main
        ref={refMain}
        className="main-bang-ke"
        style={{
          transform: queries.scale ? `scale(${queries.scale})` : "scale(1)",
          transformOrigin: queries.scale ? "top" : "unset",
        }}
        showPrintBtn={showPrintBtn()}
      >
        {([
          "P032",
          "P107",
          "P062",
          "P178",
          "P541",
          "P107",
          "P678",
          "P677",
          "P727",
          "P746",
          "P747",
        ].includes(queries.ma)
          ? showPrintBtn()
          : showButtonPrint) && (
          <Button
            id="btn-print"
            onClick={print}
            type="primary"
            rightIcon={<SVG.IcPrint />}
          >
            {t("common.inPhieu")} [F3]
          </Button>
        )}
        <div
          className="form-content bang-ke-chi-phi"
          style={{ width: A4.width }}
        >
          {renderMain}
        </div>

        {state.urlFileLocal && (
          <div style={{ display: "flex", justifyContent: "center" }}>
            {state.urlFileLocal ? (
              <Document
                className="docs"
                file={state.urlFileLocal}
                onLoadSuccess={onDocumentLoadSuccess}
                onDocumentComplete={onDocumentComplete}
                onPageComplete={onPageComplete}
                height={200}
              >
                {Array.apply(null, Array(state.numPages)).map((item, index) => {
                  return (
                    <Page
                      pageNumber={index + 1}
                      key={index}
                      scale={queries.scale || 1}
                    />
                  );
                })}
              </Document>
            ) : null}
          </div>
        )}
      </Main>
    </>
  );
};

export default BangKePrint;
