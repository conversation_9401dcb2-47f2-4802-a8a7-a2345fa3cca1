import { THIET_LAP_CHUNG } from "constants/index";
import { useThietLap } from "hooks";

const useThietLapChung = () => {
  const [dataTHIET_LAP_CHAN_KY_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.THIET_LAP_CHAN_KY_BANG_KE
  );
  const [dataTHIET_LAP_CHAN_KY_NGANG_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.THIET_LAP_CHAN_KY_NGANG_BANG_KE
  );

  const [dataBANG_KE_TONG_HOP_SUA_DOI] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_TONG_HOP_SUA_DOI
  );

  const [dataTHEM_TEXT_BANG_KE_CHUA_DUYET_BH] = useThietLap(
    THIET_LAP_CHUNG.THEM_TEXT_BANG_KE_CHUA_DUYET_BH
  );
  const [dataKHONG_HIEN_THI_TEXT_TONG_HOP_TREN_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HIEN_THI_TEXT_TONG_HOP_TREN_BANG_KE
  );
  const [dataCHI_HIEN_THI_SO_PHIEU_TRONG_SO_PHOI] = useThietLap(
    THIET_LAP_CHUNG.CHI_HIEN_THI_SO_PHIEU_TRONG_SO_PHOI
  );
  const [dataSO_PHOI_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.SO_PHOI_THANH_TOAN
  );
  const [dataBANG_KE_CHI_PHI_HIEN_THI_TONG_KET] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_CHI_PHI_HIEN_THI_TONG_KET
  );
  const [dataBANG_KE_CHI_PHI_AN_HOAN_TAM_UNG] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_CHI_PHI_AN_HOAN_TAM_UNG
  );
  const [dataPHIEU_THU_BIEN_LAI_VIEN_PHI] = useThietLap(
    THIET_LAP_CHUNG.PHIEU_THU_BIEN_LAI_VIEN_PHI
  );

  const [dataCHAN_DOAN_THEO_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.CHAN_DOAN_THEO_BANG_KE
  );
  const [dataBANG_KE_THONG_TIN_BO_SUNG] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_THONG_TIN_BO_SUNG
  );
  const [dataHIEN_THU_TIEN_NB_PHAI_TRA_TREN_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THU_TIEN_NB_PHAI_TRA_TREN_BANG_KE
  );

  const [dataHIEN_THI_CHAN_DOAN_MO_TA_CUNG_HANG_CHAN_DOAN_CHINH_TREN_BANG_KE] =
    useThietLap(
      THIET_LAP_CHUNG.HIEN_THI_CHAN_DOAN_MO_TA_CUNG_HANG_CHAN_DOAN_CHINH_TREN_BANG_KE
    );

  const [dataCHO_IN_BANG_KE_TIEN_ICH_NGUOI_BENH_NGOAI_TRU] = useThietLap(
    THIET_LAP_CHUNG.CHO_IN_BANG_KE_TIEN_ICH_NGUOI_BENH_NGOAI_TRU
  );

  const [dataBANG_KE_CHI_PHI_KHUNG_DICH_VU] = useThietLap(
    THIET_LAP_CHUNG.BANG_KE_CHI_PHI_KHUNG_DICH_VU
  );

  const [dataHIEN_THI_TEN_NB_BANG_KE] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_TEN_NB_BANG_KE
  );
  return {
    dataTHIET_LAP_CHAN_KY_BANG_KE,
    dataTHIET_LAP_CHAN_KY_NGANG_BANG_KE,
    dataBANG_KE_TONG_HOP_SUA_DOI,
    dataTHEM_TEXT_BANG_KE_CHUA_DUYET_BH,
    dataKHONG_HIEN_THI_TEXT_TONG_HOP_TREN_BANG_KE,
    dataCHI_HIEN_THI_SO_PHIEU_TRONG_SO_PHOI,
    dataSO_PHOI_THANH_TOAN,
    dataBANG_KE_CHI_PHI_HIEN_THI_TONG_KET,
    dataBANG_KE_CHI_PHI_AN_HOAN_TAM_UNG,
    dataPHIEU_THU_BIEN_LAI_VIEN_PHI,
    dataCHAN_DOAN_THEO_BANG_KE,
    dataBANG_KE_THONG_TIN_BO_SUNG,
    dataHIEN_THU_TIEN_NB_PHAI_TRA_TREN_BANG_KE,
    dataHIEN_THI_CHAN_DOAN_MO_TA_CUNG_HANG_CHAN_DOAN_CHINH_TREN_BANG_KE,
    dataCHO_IN_BANG_KE_TIEN_ICH_NGUOI_BENH_NGOAI_TRU,
    dataBANG_KE_CHI_PHI_KHUNG_DICH_VU,
    dataHIEN_THI_TEN_NB_BANG_KE,
  };
};

export default useThietLapChung;
